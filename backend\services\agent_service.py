from .langchain_service import AdvancedLangChainService
from .rag_service import RAGService

class AgentService:
    def __init__(self):
        self.langchain_service = AdvancedLangChainService()
        self.rag_service = RAGService()

        # Available tools list
        self.available_tools = ["search", "calculator", "knowledge_base"]

    def _use_rag_tool(self, query):
        """Use RAG tool to search knowledge base"""
        try:
            context = self.rag_service.get_context(query)
            return f"Relevant context from knowledge base:\n{context}"
        except Exception as e:
            return f"RAG search error: {str(e)}"

    def run_enhanced_agent(self, message, conversation_history=None, use_rag=False):
        """Run agent with enhanced capabilities"""
        try:
            # Check if message requires RAG
            message_lower = message.lower()
            enhanced_message = message

            if use_rag or any(word in message_lower for word in ['knowledge', 'document', 'internal', 'company']):
                # Use RAG tool
                rag_result = self._use_rag_tool(message)
                enhanced_message = f"{message}\n\nKnowledge Base Result: {rag_result}"

            # Use the enhanced langchain service with tools
            return self.langchain_service.run_agent(enhanced_message, conversation_history)

        except Exception as e:
            return f"Enhanced agent error: {str(e)}"

    def get_available_tools(self):
        """Get list of available tools"""
        return self.available_tools
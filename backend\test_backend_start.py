#!/usr/bin/env python3
"""
Test script to verify backend can start and all services initialize properly
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

print("🚀 Testing Backend Startup Sequence")
print("=" * 50)

# Test 1: Import all required modules
print("\n1️⃣ Testing Core Imports...")
try:
    import eventlet
    print("✓ eventlet imported")
    
    from flask import Flask, request, jsonify
    print("✓ Flask imported")
    
    from flask_cors import CORS
    print("✓ Flask-CORS imported")
    
    from flask_socketio import SocketIO
    print("✓ Flask-SocketIO imported")
    
    print("✅ All core imports successful!")
    
except Exception as e:
    print(f"❌ Core import failed: {e}")
    sys.exit(1)

# Test 2: Import all services
print("\n2️⃣ Testing Service Imports...")
try:
    from services.langchain_service import LangChainService
    print("✓ LangChainService imported")
    
    from services.rag_service import RAGService
    print("✓ RAGService imported")
    
    from services.web_search_service import WebSearchService
    print("✓ WebSearchService imported")
    
    from services.gemini_web_service import GeminiWebService
    print("✓ GeminiWebService imported")
    
    from services.agent_service import AgentService
    print("✓ AgentService imported")
    
    print("✅ All service imports successful!")
    
except Exception as e:
    print(f"❌ Service import failed: {e}")
    import traceback
    traceback.print_exc()

# Test 3: Initialize Flask app (without running)
print("\n3️⃣ Testing Flask App Initialization...")
try:
    app = Flask(__name__)
    CORS(app)
    
    # Configure app
    app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL')
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SECRET_KEY'] = os.getenv('SECRET_KEY')
    
    print("✓ Flask app configured")
    
    # Initialize SocketIO
    socketio = SocketIO(app, cors_allowed_origins="*", async_mode='eventlet')
    print("✓ SocketIO initialized")
    
    print("✅ Flask app initialization successful!")
    
except Exception as e:
    print(f"❌ Flask app initialization failed: {e}")

# Test 4: Initialize all services
print("\n4️⃣ Testing Service Initialization...")
try:
    print("   Initializing LangChain Service...")
    langchain_service = LangChainService()
    print("   ✓ LangChain Service initialized")
    
    print("   Initializing RAG Service...")
    rag_service = RAGService()
    print("   ✓ RAG Service initialized")
    
    print("   Initializing Web Search Service...")
    web_search_service = WebSearchService()
    print("   ✓ Web Search Service initialized")
    
    print("   Initializing Gemini Web Service...")
    gemini_web_service = GeminiWebService()
    print("   ✓ Gemini Web Service initialized")
    
    print("   Initializing Agent Service...")
    agent_service = AgentService()
    print("   ✓ Agent Service initialized")
    
    print("✅ All services initialized successfully!")
    
except Exception as e:
    print(f"❌ Service initialization failed: {e}")
    import traceback
    traceback.print_exc()

# Test 5: Check service status
print("\n5️⃣ Testing Service Status...")
try:
    # RAG Service status
    rag_status = rag_service.get_status()
    print(f"   RAG Service: {rag_status['status']} - {rag_status['message']}")
    
    # Web Search Service status
    web_configured = web_search_service.is_configured()
    print(f"   Web Search: {'Configured' if web_configured else 'Not Configured'}")
    
    # Gemini Web Service status
    gemini_configured = gemini_web_service.is_configured()
    print(f"   Gemini Web: {'Configured' if gemini_configured else 'Not Configured'}")
    
    print("✅ Service status check complete!")
    
except Exception as e:
    print(f"❌ Service status check failed: {e}")

print("\n" + "=" * 50)
print("🎉 Backend Startup Test Complete!")
print("\nThe backend is ready to handle requests!")
print("You can now start the Flask app with: python app.py")

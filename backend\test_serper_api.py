#!/usr/bin/env python3
"""
Test script to debug Serper API issues
"""

import os
import requests
from dotenv import load_dotenv

load_dotenv()

serper_api_key = os.getenv('SERPER_API_KEY')

print("🔍 Testing Serper API Endpoints")
print("=" * 50)
print(f"API Key: {serper_api_key[:10]}...{serper_api_key[-10:]}")

# Test different endpoints
endpoints = [
    "https://google.serper.dev/search",
    "https://api.serper.dev/search",
    "https://serper.dev/api/search"
]

headers = {
    "X-API-KEY": serper_api_key,
    "Content-Type": "application/json",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
}

payload = {
    "q": "test query",
    "num": 3,
    "hl": "en",
    "gl": "us"
}

for endpoint in endpoints:
    print(f"\n🌐 Testing endpoint: {endpoint}")
    try:
        response = requests.post(
            endpoint,
            headers=headers,
            json=payload,
            timeout=10
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Success! Found {len(data.get('organic', []))} results")
            break
        else:
            print(f"   ❌ Error: {response.text[:100]}")
    except Exception as e:
        print(f"   ❌ Exception: {str(e)}")

print("\n🔧 Testing with curl-like approach...")
try:
    import subprocess
    result = subprocess.run([
        'curl', '-X', 'POST',
        'https://google.serper.dev/search',
        '-H', f'X-API-KEY: {serper_api_key}',
        '-H', 'Content-Type: application/json',
        '-d', '{"q":"test","num":3}'
    ], capture_output=True, text=True, timeout=10)
    print(f"Curl result: {result.stdout[:200]}")
except Exception as e:
    print(f"Curl failed: {e}")

"""
Comprehensive Image Analysis Service using Gemini Vision API
Provides detailed visual analysis, OCR, object detection, and contextual interpretation
"""

import os
import sys
import base64
import io
from typing import Dict, List, Optional, Any, Union
from PIL import Image, ImageEnhance, ImageFilter
import google.generativeai as genai
from dotenv import load_dotenv

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.gemini_config import GeminiConfig
from services.language_service import LanguageService

# Load environment variables
load_dotenv()

class ImageAnalyzer:
    """
    Advanced Image Analysis Service using Gemini Vision API
    Provides comprehensive visual analysis without summarization
    """
    
    def __init__(self):
        """Initialize the Image Analyzer with Gemini Vision API"""
        # Initialize Gemini configuration
        if not GeminiConfig.is_initialized():
            GeminiConfig.initialize()
        
        # Get vision model for image analysis
        try:
            self.vision_model = GeminiConfig.get_model('vision')
            self.text_model = GeminiConfig.create_detailed_model('chat')
            print("✅ Image Analyzer initialized with Gemini Vision API")
        except Exception as e:
            print(f"⚠️  Failed to initialize Gemini Vision model: {e}")
            self.vision_model = None
            self.text_model = None
        
        # Initialize language service for multilingual support
        self.language_service = LanguageService()
        
        # Supported image formats
        self.supported_formats = {
            'JPEG', 'JPG', 'PNG', 'GIF', 'BMP', 'TIFF', 'WEBP'
        }
        
        # Image processing settings
        self.max_image_size = (2048, 2048)  # Max dimensions for processing
        self.quality_threshold = 0.7  # Minimum quality for analysis
    
    def analyze_image_comprehensive(self, image_data: Union[bytes, str, Image.Image], 
                                  user_query: str = "", 
                                  language_context: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Perform comprehensive image analysis with detailed results
        
        Args:
            image_data: Image data (bytes, base64 string, or PIL Image)
            user_query: Specific user question about the image
            language_context: Language context from language service
            
        Returns:
            Comprehensive analysis results
        """
        try:
            if not self.vision_model:
                return {
                    "success": False,
                    "error": "Gemini Vision API not available",
                    "analysis": {}
                }
            
            # Process and prepare image
            processed_image = self._prepare_image(image_data)
            if not processed_image:
                return {
                    "success": False,
                    "error": "Failed to process image",
                    "analysis": {}
                }
            
            # Detect language from user query if provided
            if user_query and not language_context:
                language_context = self.language_service.detect_and_prepare_message(user_query)
            
            # Perform comprehensive analysis
            analysis_results = self._perform_detailed_analysis(
                processed_image, user_query, language_context
            )
            
            return {
                "success": True,
                "analysis": analysis_results,
                "metadata": {
                    "image_processed": True,
                    "language_detected": language_context.get('detected_language', 'en') if language_context else 'en',
                    "comprehensive_analysis": True,
                    "summarization_applied": False
                }
            }
            
        except Exception as e:
            print(f"Error in comprehensive image analysis: {e}")
            return {
                "success": False,
                "error": f"Analysis failed: {str(e)}",
                "analysis": {}
            }
    
    def _prepare_image(self, image_data: Union[bytes, str, Image.Image]) -> Optional[Image.Image]:
        """
        Prepare and optimize image for analysis
        
        Args:
            image_data: Raw image data in various formats
            
        Returns:
            Processed PIL Image or None if failed
        """
        try:
            # Convert to PIL Image based on input type
            if isinstance(image_data, Image.Image):
                image = image_data.copy()
            elif isinstance(image_data, bytes):
                image = Image.open(io.BytesIO(image_data))
            elif isinstance(image_data, str):
                # Assume base64 encoded
                if image_data.startswith('data:image'):
                    # Remove data URL prefix
                    image_data = image_data.split(',')[1]
                image_bytes = base64.b64decode(image_data)
                image = Image.open(io.BytesIO(image_bytes))
            else:
                print(f"Unsupported image data type: {type(image_data)}")
                return None
            
            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # Resize if too large
            if image.size[0] > self.max_image_size[0] or image.size[1] > self.max_image_size[1]:
                image.thumbnail(self.max_image_size, Image.Resampling.LANCZOS)
                print(f"Image resized to {image.size} for optimal processing")
            
            # Enhance image quality for better analysis
            image = self._enhance_image_quality(image)
            
            return image
            
        except Exception as e:
            print(f"Error preparing image: {e}")
            return None
    
    def _enhance_image_quality(self, image: Image.Image) -> Image.Image:
        """
        Enhance image quality for better analysis results
        
        Args:
            image: PIL Image to enhance
            
        Returns:
            Enhanced PIL Image
        """
        try:
            # Enhance contrast slightly
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(1.1)
            
            # Enhance sharpness slightly
            enhancer = ImageEnhance.Sharpness(image)
            image = enhancer.enhance(1.1)
            
            # Apply slight noise reduction
            image = image.filter(ImageFilter.MedianFilter(size=3))
            
            return image
            
        except Exception as e:
            print(f"Error enhancing image: {e}")
            return image  # Return original if enhancement fails
    
    def _perform_detailed_analysis(self, image: Image.Image, 
                                 user_query: str, 
                                 language_context: Optional[Dict]) -> Dict[str, Any]:
        """
        Perform detailed image analysis using Gemini Vision API
        
        Args:
            image: Processed PIL Image
            user_query: User's specific question
            language_context: Language context for response
            
        Returns:
            Comprehensive analysis results
        """
        try:
            # Create comprehensive analysis prompt
            analysis_prompt = self._create_comprehensive_prompt(user_query, language_context)
            
            # Convert image to format suitable for Gemini
            image_bytes = io.BytesIO()
            image.save(image_bytes, format='JPEG', quality=95)
            image_bytes.seek(0)
            
            # Perform analysis with Gemini Vision
            try:
                response = self.vision_model.generate_content([
                    analysis_prompt,
                    image
                ])

                # Check if response is valid
                if not response or not response.text:
                    raise Exception("Empty response from Gemini Vision API")

            except Exception as e:
                error_str = str(e)
                print(f"🔍 DEBUG: Image analysis error: {error_str}")
                print(f"🔍 DEBUG: Error type: {type(e)}")

                # Handle quota exceeded error with user-friendly message (any 429 error)
                if "429" in error_str or "ResourceExhausted" in str(type(e)):
                    print("🚫 DEBUG: Quota exceeded detected")
                    # Get basic image information without using Gemini
                    basic_info = self._get_basic_image_info(image)

                    return {
                        "comprehensive_analysis": self._generate_detailed_quota_message(basic_info),
                        "technical_analysis": {
                            "status": "quota_exceeded",
                            "message": "Gemini API daily quota of 50 requests exceeded",
                            "retry_suggestion": "Try again after quota reset or upgrade to paid tier",
                            "basic_info": basic_info
                        },
                        "quota_exceeded": True,
                        "error_type": "quota_limit"
                    }
                else:
                    print(f"✅ DEBUG: Non-quota error, re-raising: {error_str}")
                    # Re-raise other errors
                    raise e

            # Parse and structure the response
            analysis_text = response.text.strip()
            
            # Additional technical analysis
            technical_analysis = self._perform_technical_analysis(image)
            
            return {
                "comprehensive_analysis": analysis_text,
                "technical_analysis": technical_analysis,
                "user_query_response": user_query,
                "language_used": language_context.get('detected_language', 'en') if language_context else 'en',
                "analysis_timestamp": self._get_timestamp(),
                "detailed_sections": self._parse_analysis_sections(analysis_text),
                "summarization_applied": False
            }
            
        except Exception as e:
            print(f"Error in detailed analysis: {e}")
            import traceback
            traceback.print_exc()

            # Return error in a format that the main app can handle
            raise Exception(f"Image analysis failed: {str(e)}")
    
    def _create_comprehensive_prompt(self, user_query: str, 
                                   language_context: Optional[Dict]) -> str:
        """
        Create a comprehensive analysis prompt for Gemini Vision
        
        Args:
            user_query: User's specific question
            language_context: Language context for response
            
        Returns:
            Detailed analysis prompt
        """
        # Base comprehensive prompt
        base_prompt = """
COMPREHENSIVE IMAGE ANALYSIS - NO SUMMARIZATION

Provide a COMPLETE, DETAILED, and THOROUGH analysis of this image. DO NOT summarize or condense any information. Include ALL observable details and provide comprehensive insights.

ANALYSIS SECTIONS (Provide detailed information for each):

1. VISUAL DESCRIPTION (Complete Details):
   - Overall scene and composition
   - All objects, people, animals, or items visible
   - Spatial relationships and positioning
   - Lighting conditions and shadows
   - Perspective and viewpoint
   - Image quality and clarity

2. OBJECT AND ELEMENT ANALYSIS (Comprehensive):
   - Detailed description of every visible object
   - Materials, textures, and surfaces
   - Colors, patterns, and designs
   - Size relationships and proportions
   - Condition and state of objects
   - Brand names, labels, or text visible

3. OCR AND TEXT EXTRACTION (Complete):
   - ALL text visible in the image (exact transcription)
   - Language of text identified
   - Font styles, sizes, and formatting
   - Text placement and context
   - Signs, labels, captions, or written content
   - Handwritten vs. printed text distinction

4. COLOR AND COMPOSITION ANALYSIS (Detailed):
   - Dominant colors and color palette
   - Color harmony and contrast
   - Composition techniques used
   - Visual balance and symmetry
   - Depth of field and focus areas
   - Artistic or photographic techniques

5. CONTEXTUAL AND SCENE ANALYSIS (Comprehensive):
   - Location or setting identification
   - Time period or era indicators
   - Cultural or regional context
   - Activity or event taking place
   - Environmental conditions
   - Social or historical significance

6. TECHNICAL QUALITY ASSESSMENT (Complete):
   - Image resolution and clarity
   - Exposure and lighting quality
   - Focus and depth of field
   - Noise or grain levels
   - Color accuracy and saturation
   - Overall technical quality rating

7. INTERPRETATION AND MEANING (Detailed):
   - Possible purpose or intent of the image
   - Emotional tone or mood conveyed
   - Symbolic or metaphorical elements
   - Story or narrative suggested
   - Artistic or documentary value
   - Cultural or social implications

8. USER QUERY RESPONSE (Specific and Complete):
   - Direct answer to user's specific question
   - Additional relevant information
   - Context and background details
   - Comprehensive explanation of findings
"""
        
        # Add language-specific instructions if needed
        if language_context and language_context.get('detected_language') != 'en':
            language_name = language_context.get('detected_language_name', 'the detected language')
            language_prompt = f"""

LANGUAGE INSTRUCTIONS:
- Respond in {language_name}
- Use natural, fluent language appropriate for {language_name} speakers
- Maintain cultural context relevant to {language_name} speakers
- Provide the same level of detail and comprehensiveness as requested
"""
            base_prompt += language_prompt
        
        # Add user query if provided
        if user_query:
            base_prompt += f"""

USER'S SPECIFIC QUESTION: {user_query}

Ensure you provide a comprehensive answer to this specific question while maintaining all the detailed analysis sections above.
"""
        
        base_prompt += """

CRITICAL REQUIREMENTS:
- Provide COMPLETE and DETAILED responses for each section
- DO NOT summarize, condense, or truncate any information
- Include ALL observable details, no matter how small
- Write in full paragraphs with thorough explanations
- Provide specific examples and evidence from the image
- Cover all aspects comprehensively without omitting details
"""
        
        return base_prompt
    
    def _perform_technical_analysis(self, image: Image.Image) -> Dict[str, Any]:
        """
        Perform technical analysis of image properties
        
        Args:
            image: PIL Image to analyze
            
        Returns:
            Technical analysis results
        """
        try:
            # Basic image properties
            width, height = image.size
            mode = image.mode
            format_info = getattr(image, 'format', 'Unknown')
            
            # Color analysis
            colors = image.getcolors(maxcolors=256*256*256)
            dominant_colors = sorted(colors, key=lambda x: x[0], reverse=True)[:5] if colors else []
            
            # Calculate image statistics
            import numpy as np
            img_array = np.array(image)
            
            brightness = np.mean(img_array)
            contrast = np.std(img_array)
            
            return {
                "dimensions": {"width": width, "height": height},
                "mode": mode,
                "format": format_info,
                "file_size_estimate": f"{width * height * 3} bytes (RGB)",
                "aspect_ratio": round(width / height, 2),
                "brightness_level": round(brightness, 2),
                "contrast_level": round(contrast, 2),
                "dominant_colors": [{"count": count, "rgb": color} for count, color in dominant_colors[:3]],
                "color_diversity": len(colors) if colors else 0,
                "technical_quality": "High" if contrast > 50 and brightness > 50 else "Medium"
            }
            
        except Exception as e:
            print(f"Error in technical analysis: {e}")
            return {
                "error": f"Technical analysis failed: {str(e)}",
                "basic_info": f"Image size: {image.size}, Mode: {image.mode}"
            }
    
    def _parse_analysis_sections(self, analysis_text: str) -> Dict[str, str]:
        """
        Parse the analysis text into structured sections
        
        Args:
            analysis_text: Complete analysis text from Gemini
            
        Returns:
            Dictionary with parsed sections
        """
        sections = {}
        current_section = "general"
        current_content = []
        
        lines = analysis_text.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # Check if this is a section header
            if any(keyword in line.upper() for keyword in [
                'VISUAL DESCRIPTION', 'OBJECT AND ELEMENT', 'OCR AND TEXT', 
                'COLOR AND COMPOSITION', 'CONTEXTUAL AND SCENE', 'TECHNICAL QUALITY',
                'INTERPRETATION AND MEANING', 'USER QUERY RESPONSE'
            ]):
                # Save previous section
                if current_content:
                    sections[current_section] = '\n'.join(current_content)
                
                # Start new section
                current_section = line.lower().replace(':', '').strip()
                current_content = []
            else:
                current_content.append(line)
        
        # Save final section
        if current_content:
            sections[current_section] = '\n'.join(current_content)
        
        return sections
    
    def _get_timestamp(self) -> str:
        """Get current timestamp for analysis"""
        from datetime import datetime
        return datetime.now().isoformat()

    def analyze_image_batch(self, images: List[Union[bytes, str, Image.Image]],
                           user_query: str = "",
                           language_context: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Analyze multiple images in batch for comprehensive comparison

        Args:
            images: List of image data
            user_query: User's question about the images
            language_context: Language context for response

        Returns:
            Batch analysis results
        """
        try:
            if not images:
                return {
                    "success": False,
                    "error": "No images provided for batch analysis",
                    "results": []
                }

            batch_results = []
            comparative_analysis = {}

            # Analyze each image individually
            for i, image_data in enumerate(images):
                print(f"Analyzing image {i+1}/{len(images)}...")
                result = self.analyze_image_comprehensive(
                    image_data, user_query, language_context
                )
                result['image_index'] = i + 1
                batch_results.append(result)

            # Perform comparative analysis if multiple images
            if len(images) > 1:
                comparative_analysis = self._perform_comparative_analysis(
                    batch_results, user_query, language_context
                )

            return {
                "success": True,
                "batch_size": len(images),
                "individual_results": batch_results,
                "comparative_analysis": comparative_analysis,
                "comprehensive_data": True,
                "summarization_applied": False
            }

        except Exception as e:
            print(f"Error in batch image analysis: {e}")
            return {
                "success": False,
                "error": f"Batch analysis failed: {str(e)}",
                "results": []
            }

    def _perform_comparative_analysis(self, batch_results: List[Dict],
                                    user_query: str,
                                    language_context: Optional[Dict]) -> Dict[str, Any]:
        """
        Perform comparative analysis across multiple images

        Args:
            batch_results: Results from individual image analyses
            user_query: User's query
            language_context: Language context

        Returns:
            Comparative analysis results
        """
        try:
            if not self.text_model:
                return {"error": "Text model not available for comparative analysis"}

            # Extract key information from each analysis
            summaries = []
            for i, result in enumerate(batch_results):
                if result.get('success'):
                    analysis = result.get('analysis', {})
                    summary = f"Image {i+1}: {analysis.get('comprehensive_analysis', 'No analysis available')[:500]}..."
                    summaries.append(summary)

            # Create comparative analysis prompt
            comparative_prompt = f"""
COMPREHENSIVE COMPARATIVE IMAGE ANALYSIS - NO SUMMARIZATION

Analyze and compare the following images based on their individual analyses. Provide COMPLETE, DETAILED comparisons without summarization.

INDIVIDUAL IMAGE SUMMARIES:
{chr(10).join(summaries)}

USER QUERY: {user_query}

COMPARATIVE ANALYSIS SECTIONS (Provide detailed information for each):

1. VISUAL SIMILARITIES AND DIFFERENCES:
   - Common visual elements across images
   - Unique features in each image
   - Style and composition comparisons
   - Color palette similarities/differences

2. CONTENT AND SUBJECT COMPARISON:
   - Similar objects or subjects
   - Different contexts or settings
   - Thematic connections or contrasts
   - Narrative relationships between images

3. TECHNICAL QUALITY COMPARISON:
   - Image quality differences
   - Lighting and exposure comparisons
   - Composition technique variations
   - Technical strengths and weaknesses

4. CONTEXTUAL RELATIONSHIPS:
   - Temporal relationships (if any)
   - Spatial relationships (if any)
   - Thematic or conceptual connections
   - Cultural or historical context comparisons

5. USER QUERY SPECIFIC COMPARISON:
   - Direct comparison addressing user's question
   - Evidence from each image supporting conclusions
   - Comprehensive analysis of differences and similarities

REQUIREMENTS:
- Provide COMPLETE and DETAILED comparisons
- DO NOT summarize or condense information
- Include specific examples from each image
- Write comprehensive explanations for all observations
"""

            # Add language instructions if needed
            if language_context and language_context.get('detected_language') != 'en':
                language_name = language_context.get('detected_language_name', 'the detected language')
                comparative_prompt += f"""

LANGUAGE INSTRUCTIONS:
- Respond in {language_name}
- Maintain cultural context relevant to {language_name} speakers
- Provide the same level of detail and comprehensiveness as requested
"""

            # Generate comparative analysis
            response = self.text_model.generate_content(comparative_prompt)

            return {
                "comparative_analysis": response.text,
                "images_compared": len(batch_results),
                "analysis_timestamp": self._get_timestamp(),
                "language_used": language_context.get('detected_language', 'en') if language_context else 'en',
                "summarization_applied": False
            }

        except Exception as e:
            print(f"Error in comparative analysis: {e}")
            return {
                "error": f"Comparative analysis failed: {str(e)}",
                "comparative_analysis": "Comparison could not be completed"
            }

    def extract_text_from_image(self, image_data: Union[bytes, str, Image.Image],
                               language_hint: Optional[str] = None) -> Dict[str, Any]:
        """
        Extract and analyze all text from an image using OCR capabilities

        Args:
            image_data: Image data to process
            language_hint: Hint about the language of text in image

        Returns:
            Comprehensive text extraction results
        """
        try:
            if not self.vision_model:
                return {
                    "success": False,
                    "error": "Gemini Vision API not available",
                    "extracted_text": ""
                }

            # Prepare image
            processed_image = self._prepare_image(image_data)
            if not processed_image:
                return {
                    "success": False,
                    "error": "Failed to process image for OCR",
                    "extracted_text": ""
                }

            # Create OCR-focused prompt
            ocr_prompt = """
COMPREHENSIVE TEXT EXTRACTION AND ANALYSIS - NO SUMMARIZATION

Extract and analyze ALL text visible in this image. Provide COMPLETE and DETAILED results without summarization.

TEXT EXTRACTION SECTIONS:

1. COMPLETE TEXT TRANSCRIPTION:
   - Extract ALL visible text exactly as it appears
   - Maintain original formatting, line breaks, and spacing
   - Include punctuation, special characters, and symbols
   - Transcribe text in original language(s)

2. TEXT LOCATION AND CONTEXT:
   - Describe the location of each text element in the image
   - Context surrounding each text (signs, labels, documents, etc.)
   - Size and prominence of different text elements
   - Relationship between text and visual elements

3. LANGUAGE AND SCRIPT ANALYSIS:
   - Identify language(s) of extracted text
   - Script type (Latin, Devanagari, Arabic, etc.)
   - Mixed language content if present
   - Regional or dialect variations

4. TEXT FORMATTING AND STYLE:
   - Font styles, sizes, and formatting
   - Bold, italic, underlined text
   - Color of text and background
   - Handwritten vs. printed text distinction

5. CONTENT ANALYSIS:
   - Type of content (signs, documents, labels, etc.)
   - Purpose and function of the text
   - Important information or messages conveyed
   - Commercial, informational, or personal content

6. QUALITY AND READABILITY:
   - Text clarity and legibility
   - Factors affecting readability
   - Confidence level in extraction accuracy
   - Areas of uncertainty or unclear text

REQUIREMENTS:
- Extract ALL visible text without omission
- Maintain exact transcription accuracy
- Provide comprehensive context for each text element
- DO NOT summarize or condense any extracted content
"""

            if language_hint:
                ocr_prompt += f"""

LANGUAGE HINT: The text may be in {language_hint}. Pay special attention to characters and scripts common in this language.
"""

            # Perform OCR analysis
            response = self.vision_model.generate_content([ocr_prompt, processed_image])

            # Parse extracted text
            extracted_content = response.text

            return {
                "success": True,
                "extracted_text": extracted_content,
                "language_hint_used": language_hint,
                "extraction_timestamp": self._get_timestamp(),
                "comprehensive_extraction": True,
                "summarization_applied": False,
                "raw_response": extracted_content
            }

        except Exception as e:
            print(f"Error in text extraction: {e}")
            return {
                "success": False,
                "error": f"Text extraction failed: {str(e)}",
                "extracted_text": ""
            }

    def analyze_image_for_accessibility(self, image_data: Union[bytes, str, Image.Image],
                                      language_context: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Analyze image for accessibility purposes (alt text, descriptions for visually impaired)

        Args:
            image_data: Image data to analyze
            language_context: Language context for response

        Returns:
            Accessibility-focused analysis
        """
        try:
            if not self.vision_model:
                return {
                    "success": False,
                    "error": "Gemini Vision API not available",
                    "accessibility_description": ""
                }

            # Prepare image
            processed_image = self._prepare_image(image_data)
            if not processed_image:
                return {
                    "success": False,
                    "error": "Failed to process image for accessibility analysis",
                    "accessibility_description": ""
                }

            # Create accessibility-focused prompt
            accessibility_prompt = """
COMPREHENSIVE ACCESSIBILITY IMAGE DESCRIPTION - NO SUMMARIZATION

Create detailed descriptions for accessibility purposes. Provide COMPLETE information for visually impaired users.

ACCESSIBILITY DESCRIPTION SECTIONS:

1. CONCISE ALT TEXT (Brief but Complete):
   - Essential information in 1-2 sentences
   - Key objects, people, and actions
   - Important context for understanding

2. DETAILED DESCRIPTION (Comprehensive):
   - Complete visual description of all elements
   - Spatial relationships and positioning
   - Colors, textures, and visual details
   - All text content present in the image

3. CONTEXTUAL INFORMATION:
   - Setting and environment
   - Time of day or lighting conditions
   - Mood or atmosphere conveyed
   - Cultural or social context

4. FUNCTIONAL INFORMATION:
   - Purpose or function of objects shown
   - Actions taking place
   - Interactive elements if any
   - Navigation or wayfinding information

5. TEXT AND SIGNAGE:
   - All visible text transcribed exactly
   - Sign content and messaging
   - Labels and captions
   - Important textual information

6. EMOTIONAL AND AESTHETIC ELEMENTS:
   - Mood and emotional tone
   - Artistic or aesthetic qualities
   - Visual impact and composition
   - Symbolic or metaphorical content

REQUIREMENTS:
- Provide clear, descriptive language
- Include ALL visual information without omission
- Use objective, factual descriptions
- Maintain comprehensive detail for full understanding
- DO NOT summarize or condense information
"""

            # Add language instructions if needed
            if language_context and language_context.get('detected_language') != 'en':
                language_name = language_context.get('detected_language_name', 'the detected language')
                accessibility_prompt += f"""

LANGUAGE INSTRUCTIONS:
- Provide descriptions in {language_name}
- Use clear, accessible language appropriate for {language_name} speakers
- Maintain cultural context relevant to {language_name} speakers
"""

            # Generate accessibility description
            response = self.vision_model.generate_content([accessibility_prompt, processed_image])

            return {
                "success": True,
                "accessibility_description": response.text,
                "language_used": language_context.get('detected_language', 'en') if language_context else 'en',
                "analysis_timestamp": self._get_timestamp(),
                "comprehensive_accessibility": True,
                "summarization_applied": False
            }

        except Exception as e:
            print(f"Error in accessibility analysis: {e}")
            return {
                "success": False,
                "error": f"Accessibility analysis failed: {str(e)}",
                "accessibility_description": ""
            }

    def _get_basic_image_info(self, image):
        """Get comprehensive technical information about the image without AI analysis"""
        try:
            width, height = image.size
            format_name = image.format or "Unknown"
            mode = image.mode

            # Calculate estimated file size (rough approximation)
            channels = len(image.getbands())
            estimated_size = (width * height * channels) // 1024  # KB approximation

            # Determine aspect ratio
            gcd = self._gcd(width, height)
            aspect_w, aspect_h = width // gcd, height // gcd
            aspect_ratio = f"{aspect_w}:{aspect_h}"

            # Resolution category
            total_pixels = width * height
            if total_pixels < 500000:  # < 0.5MP
                resolution_category = "Low Resolution"
            elif total_pixels < 2000000:  # < 2MP
                resolution_category = "Standard Resolution"
            elif total_pixels < 8000000:  # < 8MP
                resolution_category = "High Resolution"
            else:
                resolution_category = "Very High Resolution"

            # Orientation
            orientation = "Landscape" if width > height else "Portrait" if height > width else "Square"

            # Mode description
            mode_descriptions = {
                "RGB": "Full Color (Red, Green, Blue)",
                "RGBA": "Full Color with Transparency",
                "L": "Grayscale",
                "P": "Palette Mode",
                "CMYK": "Print Colors (Cyan, Magenta, Yellow, Black)",
                "1": "Black and White"
            }
            mode_description = mode_descriptions.get(mode, f"Color Mode: {mode}")

            # Advanced analysis without AI
            advanced_info = self._analyze_image_properties(image)

            return {
                "width": width,
                "height": height,
                "format": format_name,
                "mode": mode,
                "mode_description": mode_description,
                "channels": channels,
                "estimated_size": estimated_size,
                "aspect_ratio": aspect_ratio,
                "resolution_category": resolution_category,
                "orientation": orientation,
                "total_pixels": total_pixels,
                "advanced_analysis": advanced_info
            }

        except Exception as e:
            return {
                "width": "Unknown",
                "height": "Unknown",
                "format": "Unknown",
                "mode": "Unknown",
                "mode_description": "Unable to determine",
                "channels": "Unknown",
                "estimated_size": "Unknown",
                "aspect_ratio": "Unknown",
                "resolution_category": "Unknown",
                "orientation": "Unknown",
                "error": str(e)
            }

    def _analyze_image_properties(self, image):
        """Perform advanced image analysis without AI"""
        try:
            import numpy as np

            # Convert to numpy array for analysis
            img_array = np.array(image)

            # Color analysis
            color_info = self._analyze_colors(img_array, image.mode)

            # Brightness and contrast analysis
            brightness_info = self._analyze_brightness_contrast(img_array)

            # Edge and texture analysis
            texture_info = self._analyze_texture(img_array)

            # Histogram analysis
            histogram_info = self._analyze_histogram(image)

            return {
                "color_analysis": color_info,
                "brightness_contrast": brightness_info,
                "texture_analysis": texture_info,
                "histogram_analysis": histogram_info,
                "image_quality": self._assess_image_quality(img_array, image.size)
            }

        except ImportError:
            # Fallback if numpy is not available
            return {
                "color_analysis": "Advanced color analysis requires numpy",
                "brightness_contrast": "Brightness analysis requires numpy",
                "texture_analysis": "Texture analysis requires numpy",
                "histogram_analysis": self._basic_histogram_analysis(image),
                "image_quality": "Basic quality assessment only"
            }
        except Exception as e:
            return {
                "error": f"Advanced analysis failed: {str(e)}",
                "basic_info": "Technical details available above"
            }

    def _analyze_colors(self, img_array, mode):
        """Analyze color properties of the image"""
        try:
            if mode == 'RGB':
                # Calculate dominant colors
                pixels = img_array.reshape(-1, 3)

                # Calculate average color
                avg_color = np.mean(pixels, axis=0)

                # Calculate color distribution
                red_avg, green_avg, blue_avg = avg_color

                # Determine dominant color channel
                dominant_channel = ['Red', 'Green', 'Blue'][np.argmax(avg_color)]

                # Calculate color variance (how colorful the image is)
                color_variance = np.var(pixels, axis=0)

                return {
                    "average_color": f"RGB({int(red_avg)}, {int(green_avg)}, {int(blue_avg)})",
                    "dominant_channel": dominant_channel,
                    "color_variance": f"R:{color_variance[0]:.1f}, G:{color_variance[1]:.1f}, B:{color_variance[2]:.1f}",
                    "colorfulness": "High" if np.mean(color_variance) > 1000 else "Medium" if np.mean(color_variance) > 500 else "Low"
                }
            else:
                return {"mode": mode, "analysis": "Color analysis optimized for RGB images"}

        except Exception as e:
            return {"error": f"Color analysis failed: {str(e)}"}

    def _analyze_brightness_contrast(self, img_array):
        """Analyze brightness and contrast"""
        try:
            # Convert to grayscale for analysis
            if len(img_array.shape) == 3:
                gray = np.mean(img_array, axis=2)
            else:
                gray = img_array

            # Calculate brightness (average pixel value)
            brightness = np.mean(gray)

            # Calculate contrast (standard deviation)
            contrast = np.std(gray)

            # Categorize brightness
            if brightness < 85:
                brightness_level = "Dark"
            elif brightness < 170:
                brightness_level = "Medium"
            else:
                brightness_level = "Bright"

            # Categorize contrast
            if contrast < 30:
                contrast_level = "Low Contrast"
            elif contrast < 60:
                contrast_level = "Medium Contrast"
            else:
                contrast_level = "High Contrast"

            return {
                "brightness_value": f"{brightness:.1f}/255",
                "brightness_level": brightness_level,
                "contrast_value": f"{contrast:.1f}",
                "contrast_level": contrast_level,
                "dynamic_range": "Good" if contrast > 40 else "Limited"
            }

        except Exception as e:
            return {"error": f"Brightness analysis failed: {str(e)}"}

    def _analyze_texture(self, img_array):
        """Analyze texture and edge information"""
        try:
            # Convert to grayscale
            if len(img_array.shape) == 3:
                gray = np.mean(img_array, axis=2)
            else:
                gray = img_array

            # Simple edge detection using gradient
            grad_x = np.abs(np.diff(gray, axis=1))
            grad_y = np.abs(np.diff(gray, axis=0))

            edge_strength = np.mean(grad_x) + np.mean(grad_y)

            if edge_strength > 20:
                texture_level = "High Detail/Sharp"
            elif edge_strength > 10:
                texture_level = "Medium Detail"
            else:
                texture_level = "Smooth/Soft"

            return {
                "edge_strength": f"{edge_strength:.1f}",
                "texture_level": texture_level,
                "detail_assessment": "Rich in details" if edge_strength > 15 else "Moderate details" if edge_strength > 8 else "Minimal details"
            }

        except Exception as e:
            return {"error": f"Texture analysis failed: {str(e)}"}

    def _analyze_histogram(self, image):
        """Analyze color histogram"""
        try:
            if image.mode == 'RGB':
                # Get histograms for each channel
                r_hist = image.split()[0].histogram()
                g_hist = image.split()[1].histogram()
                b_hist = image.split()[2].histogram()

                # Find peaks (most common values)
                r_peak = r_hist.index(max(r_hist))
                g_peak = g_hist.index(max(g_hist))
                b_peak = b_hist.index(max(b_hist))

                return {
                    "red_peak": r_peak,
                    "green_peak": g_peak,
                    "blue_peak": b_peak,
                    "color_distribution": f"R:{r_peak}, G:{g_peak}, B:{b_peak}",
                    "tonal_range": "Full range" if max(r_peak, g_peak, b_peak) > 200 and min(r_peak, g_peak, b_peak) < 50 else "Limited range"
                }
            else:
                return {"mode": image.mode, "analysis": "Histogram analysis optimized for RGB images"}

        except Exception as e:
            return {"error": f"Histogram analysis failed: {str(e)}"}

    def _basic_histogram_analysis(self, image):
        """Basic histogram analysis without numpy"""
        try:
            if image.mode == 'RGB':
                # Simple analysis without numpy
                return {
                    "analysis": "Basic histogram data available",
                    "mode": image.mode,
                    "channels": len(image.getbands())
                }
            else:
                return {"mode": image.mode, "channels": len(image.getbands())}
        except:
            return {"analysis": "Basic histogram analysis"}

    def _assess_image_quality(self, img_array, size):
        """Assess overall image quality"""
        try:
            width, height = size
            total_pixels = width * height

            # Quality based on resolution
            if total_pixels > 8000000:  # > 8MP
                resolution_quality = "Excellent"
            elif total_pixels > 2000000:  # > 2MP
                resolution_quality = "Good"
            elif total_pixels > 500000:  # > 0.5MP
                resolution_quality = "Fair"
            else:
                resolution_quality = "Low"

            # Assess sharpness based on pixel variance
            if len(img_array.shape) == 3:
                pixel_variance = np.var(img_array)
            else:
                pixel_variance = np.var(img_array)

            if pixel_variance > 2000:
                sharpness = "Sharp"
            elif pixel_variance > 1000:
                sharpness = "Moderate"
            else:
                sharpness = "Soft"

            return {
                "resolution_quality": resolution_quality,
                "sharpness": sharpness,
                "pixel_variance": f"{pixel_variance:.1f}",
                "overall_assessment": f"{resolution_quality} resolution, {sharpness.lower()} appearance"
            }

        except Exception as e:
            return {"error": f"Quality assessment failed: {str(e)}"}

    def _generate_detailed_quota_message(self, basic_info):
        """Generate detailed analysis message when quota is exceeded"""
        advanced = basic_info.get('advanced_analysis', {})
        color_info = advanced.get('color_analysis', {})
        brightness_info = advanced.get('brightness_contrast', {})
        texture_info = advanced.get('texture_analysis', {})
        quality_info = advanced.get('image_quality', {})

        return f"""📊 **Detailed Image Analysis** (Quota Exceeded - Technical Analysis Only)

I can see your image and provide comprehensive technical analysis, but AI-powered content recognition requires quota reset.

## 🖼️ **Image Properties**
- **Dimensions**: {basic_info['width']} × {basic_info['height']} pixels ({basic_info['total_pixels']:,} total pixels)
- **Format**: {basic_info['format']}
- **Color Mode**: {basic_info['mode_description']}
- **File Size**: ~{basic_info['estimated_size']} KB
- **Aspect Ratio**: {basic_info['aspect_ratio']} ({basic_info['orientation']})
- **Resolution**: {basic_info['resolution_category']}

## 🎨 **Color Analysis**
- **Average Color**: {color_info.get('average_color', 'RGB analysis available')}
- **Dominant Channel**: {color_info.get('dominant_channel', 'Color channel analysis')}
- **Colorfulness**: {color_info.get('colorfulness', 'Color variance analysis')}

## 💡 **Brightness & Contrast**
- **Brightness Level**: {brightness_info.get('brightness_level', 'Brightness analysis')} ({brightness_info.get('brightness_value', 'N/A')})
- **Contrast Level**: {brightness_info.get('contrast_level', 'Contrast analysis')} ({brightness_info.get('contrast_value', 'N/A')})
- **Dynamic Range**: {brightness_info.get('dynamic_range', 'Range analysis')}

## 🔍 **Detail & Texture**
- **Texture Level**: {texture_info.get('texture_level', 'Texture analysis')}
- **Detail Assessment**: {texture_info.get('detail_assessment', 'Detail analysis')}
- **Edge Strength**: {texture_info.get('edge_strength', 'Edge analysis')}

## ⭐ **Image Quality**
- **Resolution Quality**: {quality_info.get('resolution_quality', 'Quality analysis')}
- **Sharpness**: {quality_info.get('sharpness', 'Sharpness analysis')}
- **Overall Assessment**: {quality_info.get('overall_assessment', 'Technical assessment available')}

---

## 🚫 **AI Content Recognition Unavailable**
**Reason**: Gemini API daily quota of 50 requests exceeded

**What's Missing**: Object detection, scene recognition, text extraction, facial analysis, etc.

## 🔄 **Solutions for Full AI Analysis**
1. **Wait for quota reset** (resets daily at midnight UTC)
2. **Upgrade to paid Gemini API** for unlimited requests
3. **Try again tomorrow** for free tier reset

**This technical analysis shows your image is properly processed and ready for full AI analysis when quota resets!** 🎯"""

    def _gcd(self, a, b):
        """Calculate Greatest Common Divisor for aspect ratio"""
        while b:
            a, b = b, a % b
        return a

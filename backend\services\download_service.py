import os
import io
import uuid
from datetime import datetime
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from docx import Document as DocxDocument
from docx.shared import Inches
import openpyxl
from openpyxl.styles import Font, Alignment
import pandas as pd
import json
from flask import send_file
import tempfile

class DownloadService:
    def __init__(self):
        self.temp_dir = tempfile.gettempdir()
        
    def generate_conversation_pdf(self, messages, conversation_title="Conversation", user_info=None):
        """Generate PDF from conversation messages"""
        try:
            # Create temporary file
            temp_filename = f"conversation_{uuid.uuid4()}.pdf"
            temp_path = os.path.join(self.temp_dir, temp_filename)
            
            # Create PDF document
            doc = SimpleDocTemplate(temp_path, pagesize=letter)
            styles = getSampleStyleSheet()
            
            # Custom styles
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                alignment=1  # Center alignment
            )
            
            user_style = ParagraphStyle(
                'UserMessage',
                parent=styles['Normal'],
                fontSize=12,
                leftIndent=0,
                rightIndent=50,
                spaceAfter=12,
                textColor='blue'
            )
            
            assistant_style = ParagraphStyle(
                'AssistantMessage',
                parent=styles['Normal'],
                fontSize=12,
                leftIndent=50,
                rightIndent=0,
                spaceAfter=12,
                textColor='black'
            )
            
            # Build content
            content = []
            
            # Title
            content.append(Paragraph(conversation_title, title_style))
            content.append(Spacer(1, 12))
            
            # User info
            if user_info:
                content.append(Paragraph(f"User: {user_info.get('username', 'Unknown')}", styles['Normal']))
                content.append(Paragraph(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", styles['Normal']))
                content.append(Spacer(1, 20))
            
            # Messages
            for message in messages:
                role = message.get('role', 'unknown')
                content_text = message.get('content', '')
                timestamp = message.get('timestamp', '')
                
                if role == 'user':
                    content.append(Paragraph(f"<b>You ({timestamp}):</b>", user_style))
                    content.append(Paragraph(content_text, user_style))
                elif role == 'assistant':
                    content.append(Paragraph(f"<b>AI Assistant ({timestamp}):</b>", assistant_style))
                    content.append(Paragraph(content_text, assistant_style))
                
                content.append(Spacer(1, 12))
            
            # Build PDF
            doc.build(content)

            # Verify file was created and is not empty
            if not os.path.exists(temp_path):
                raise Exception("Failed to create PDF file")

            file_size = os.path.getsize(temp_path)
            if file_size == 0:
                raise Exception("Generated PDF file is empty")

            print(f"Generated PDF file: {temp_path}, size: {file_size} bytes")

            return temp_path
            
        except Exception as e:
            raise Exception(f"Error generating PDF: {str(e)}")
    
    def generate_conversation_docx(self, messages, conversation_title="Conversation", user_info=None):
        """Generate Word document from conversation messages with improved error handling"""
        temp_path = None
        try:
            # Create temporary file with more explicit naming
            temp_filename = f"conversation_{uuid.uuid4()}.docx"
            temp_path = os.path.join(self.temp_dir, temp_filename)

            print(f"Creating Word document at: {temp_path}")

            # Create Word document with explicit encoding handling
            doc = DocxDocument()

            # Add title with proper formatting
            title_paragraph = doc.add_heading(conversation_title, level=0)
            title_paragraph.alignment = 1  # Center alignment

            # Add metadata section with better formatting
            if user_info:
                # User info paragraph
                user_paragraph = doc.add_paragraph()
                user_run = user_paragraph.add_run("User: ")
                user_run.bold = True
                user_paragraph.add_run(str(user_info.get('username', 'Unknown')))

                # Generation date paragraph
                date_paragraph = doc.add_paragraph()
                date_run = date_paragraph.add_run("Generated: ")
                date_run.bold = True
                date_paragraph.add_run(datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

                # Add separator line
                doc.add_paragraph("-" * 60)

            # Process messages with improved text handling
            for i, message in enumerate(messages):
                role = message.get('role', 'unknown')
                content_text = str(message.get('content', ''))  # Ensure string conversion
                timestamp = str(message.get('timestamp', ''))

                # Clean content text to prevent encoding issues
                content_text = content_text.replace('\r\n', '\n').replace('\r', '\n')

                if role == 'user':
                    # Create header paragraph for user message
                    header_paragraph = doc.add_paragraph()
                    header_run = header_paragraph.add_run("USER")
                    header_run.bold = True
                    if timestamp:
                        header_paragraph.add_run(f" ({timestamp})")

                    # Add content paragraph
                    content_paragraph = doc.add_paragraph(content_text)

                elif role == 'assistant':
                    # Create header paragraph for assistant message
                    header_paragraph = doc.add_paragraph()
                    header_run = header_paragraph.add_run("AI ASSISTANT")
                    header_run.bold = True
                    if timestamp:
                        header_paragraph.add_run(f" ({timestamp})")

                    # Add content paragraph
                    content_paragraph = doc.add_paragraph(content_text)

                # Add spacing between messages (except after last message)
                if i < len(messages) - 1:
                    doc.add_paragraph()

            # Save document with explicit error handling
            print("Saving Word document...")
            try:
                doc.save(temp_path)
                print("Document saved successfully")
            except Exception as save_error:
                raise Exception(f"Failed to save document: {str(save_error)}")

            # Verify file integrity
            if not os.path.exists(temp_path):
                raise Exception("Document file was not created")

            file_size = os.path.getsize(temp_path)
            if file_size == 0:
                raise Exception("Generated document file is empty")

            # Additional integrity check - verify it's a valid ZIP file
            import zipfile
            try:
                with zipfile.ZipFile(temp_path, 'r') as zip_file:
                    # Test ZIP integrity
                    zip_file.testzip()

                    # Check for required DOCX files
                    file_list = zip_file.namelist()
                    required_files = ['word/document.xml', '[Content_Types].xml', '_rels/.rels']
                    missing_files = [f for f in required_files if f not in file_list]

                    if missing_files:
                        raise Exception(f"Document missing required files: {missing_files}")

                print(f"Document integrity verified: {file_size} bytes, {len(file_list)} internal files")

            except zipfile.BadZipFile:
                raise Exception("Generated document is not a valid DOCX file")

            print(f"Generated Word document: {temp_path}, size: {file_size} bytes")
            return temp_path

        except Exception as e:
            # Clean up on error
            if temp_path and os.path.exists(temp_path):
                try:
                    os.remove(temp_path)
                except:
                    pass
            raise Exception(f"Error generating Word document: {str(e)}")
    
    def generate_conversation_excel(self, messages, conversation_title="Conversation", user_info=None):
        """Generate Excel file from conversation messages"""
        try:
            # Create temporary file
            temp_filename = f"conversation_{uuid.uuid4()}.xlsx"
            temp_path = os.path.join(self.temp_dir, temp_filename)
            
            # Create workbook and worksheet
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "Conversation"
            
            # Headers
            headers = ['Timestamp', 'Role', 'Message']
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True)
                cell.alignment = Alignment(horizontal='center')
            
            # Add conversation info
            ws.cell(row=2, column=1, value="Conversation Title:")
            ws.cell(row=2, column=2, value=conversation_title)
            
            if user_info:
                ws.cell(row=3, column=1, value="User:")
                ws.cell(row=3, column=2, value=user_info.get('username', 'Unknown'))
                ws.cell(row=4, column=1, value="Generated:")
                ws.cell(row=4, column=2, value=datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            
            # Add messages starting from row 6
            start_row = 6
            ws.cell(row=start_row-1, column=1, value="Timestamp")
            ws.cell(row=start_row-1, column=2, value="Role")
            ws.cell(row=start_row-1, column=3, value="Message")
            
            for i, message in enumerate(messages, start_row):
                ws.cell(row=i, column=1, value=message.get('timestamp', ''))
                ws.cell(row=i, column=2, value=message.get('role', '').title())
                ws.cell(row=i, column=3, value=message.get('content', ''))
            
            # Adjust column widths
            ws.column_dimensions['A'].width = 20
            ws.column_dimensions['B'].width = 15
            ws.column_dimensions['C'].width = 80
            
            # Save workbook
            wb.save(temp_path)

            # Verify file was created and is not empty
            if not os.path.exists(temp_path):
                raise Exception("Failed to create Excel file")

            file_size = os.path.getsize(temp_path)
            if file_size == 0:
                raise Exception("Generated Excel file is empty")

            print(f"Generated Excel file: {temp_path}, size: {file_size} bytes")

            return temp_path
            
        except Exception as e:
            raise Exception(f"Error generating Excel file: {str(e)}")
    
    def generate_conversation_csv(self, messages, conversation_title="Conversation", user_info=None):
        """Generate CSV file from conversation messages"""
        try:
            # Create temporary file
            temp_filename = f"conversation_{uuid.uuid4()}.csv"
            temp_path = os.path.join(self.temp_dir, temp_filename)
            
            # Prepare data
            data = []
            
            # Add metadata
            data.append({
                'Timestamp': 'Metadata',
                'Role': 'Title',
                'Message': conversation_title
            })
            
            if user_info:
                data.append({
                    'Timestamp': 'Metadata',
                    'Role': 'User',
                    'Message': user_info.get('username', 'Unknown')
                })
                
                data.append({
                    'Timestamp': 'Metadata',
                    'Role': 'Generated',
                    'Message': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })
            
            # Add separator
            data.append({
                'Timestamp': '---',
                'Role': '---',
                'Message': '---'
            })
            
            # Add messages
            for message in messages:
                data.append({
                    'Timestamp': message.get('timestamp', ''),
                    'Role': message.get('role', '').title(),
                    'Message': message.get('content', '')
                })
            
            # Create DataFrame and save
            df = pd.DataFrame(data)
            df.to_csv(temp_path, index=False, encoding='utf-8')
            
            return temp_path
            
        except Exception as e:
            raise Exception(f"Error generating CSV file: {str(e)}")
    
    def generate_conversation_txt(self, messages, conversation_title="Conversation", user_info=None):
        """Generate text file from conversation messages"""
        try:
            # Create temporary file
            temp_filename = f"conversation_{uuid.uuid4()}.txt"
            temp_path = os.path.join(self.temp_dir, temp_filename)
            
            with open(temp_path, 'w', encoding='utf-8') as f:
                # Title
                f.write(f"{conversation_title}\n")
                f.write("=" * len(conversation_title) + "\n\n")
                
                # User info
                if user_info:
                    f.write(f"User: {user_info.get('username', 'Unknown')}\n")
                    f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                
                # Messages
                for message in messages:
                    role = message.get('role', 'unknown')
                    content_text = message.get('content', '')
                    timestamp = message.get('timestamp', '')
                    
                    if role == 'user':
                        f.write(f"You ({timestamp}):\n{content_text}\n\n")
                    elif role == 'assistant':
                        f.write(f"AI Assistant ({timestamp}):\n{content_text}\n\n")
                    
                    f.write("-" * 50 + "\n\n")
            
            return temp_path
            
        except Exception as e:
            raise Exception(f"Error generating text file: {str(e)}")
    
    def generate_conversation_json(self, messages, conversation_title="Conversation", user_info=None):
        """Generate JSON file from conversation messages"""
        try:
            # Create temporary file
            temp_filename = f"conversation_{uuid.uuid4()}.json"
            temp_path = os.path.join(self.temp_dir, temp_filename)
            
            # Prepare data
            data = {
                'conversation_title': conversation_title,
                'user_info': user_info,
                'generated_at': datetime.now().isoformat(),
                'messages': messages
            }
            
            with open(temp_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            return temp_path
            
        except Exception as e:
            raise Exception(f"Error generating JSON file: {str(e)}")
    
    def cleanup_temp_file(self, file_path):
        """Clean up temporary file"""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
        except Exception as e:
            print(f"Error cleaning up temp file: {str(e)}")
    
    def get_download_formats(self):
        """Get available download formats"""
        return {
            'pdf': {
                'name': 'PDF Document',
                'extension': '.pdf',
                'mime_type': 'application/pdf'
            },
            'docx': {
                'name': 'Word Document',
                'extension': '.docx',
                'mime_type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            },
            'xlsx': {
                'name': 'Excel Spreadsheet',
                'extension': '.xlsx',
                'mime_type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            },
            'csv': {
                'name': 'CSV File',
                'extension': '.csv',
                'mime_type': 'text/csv'
            },
            'txt': {
                'name': 'Text File',
                'extension': '.txt',
                'mime_type': 'text/plain'
            },
            'json': {
                'name': 'JSON File',
                'extension': '.json',
                'mime_type': 'application/json'
            }
        }

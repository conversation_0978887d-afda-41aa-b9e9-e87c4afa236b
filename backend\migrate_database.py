"""
Database migration script to add user preference columns
Run this script to update existing database schema
"""

import os
import psycopg2
from dotenv import load_dotenv

load_dotenv()

def migrate_database():
    """Add user preference columns to existing users table"""
    try:
        # Get database URL
        database_url = os.getenv('DATABASE_URL')
        if not database_url:
            print("ERROR: DATABASE_URL not found in environment variables")
            return False
        
        # Connect to database
        conn = psycopg2.connect(database_url)
        cursor = conn.cursor()
        
        print("Connected to database successfully")
        
        # Check if columns already exist
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'users' AND table_schema = 'public'
        """)
        
        existing_columns = [row[0] for row in cursor.fetchall()]
        print(f"Existing columns in users table: {existing_columns}")
        
        # Add missing columns
        columns_to_add = [
            ("conversation_tone", "VARCHAR(50) DEFAULT 'friendly'"),
            ("response_style", "VARCHAR(50) DEFAULT 'balanced'"),
            ("preferred_language", "VARCHAR(10) DEFAULT 'en'"),
            ("custom_instructions", "TEXT DEFAULT ''")
        ]
        
        for column_name, column_definition in columns_to_add:
            if column_name not in existing_columns:
                try:
                    cursor.execute(f"ALTER TABLE users ADD COLUMN {column_name} {column_definition}")
                    print(f"✅ Added column: {column_name}")
                except psycopg2.Error as e:
                    print(f"❌ Error adding column {column_name}: {e}")
            else:
                print(f"⏭️  Column {column_name} already exists")
        
        # Create user_preferences table if it doesn't exist
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS user_preferences (
                id VARCHAR(100) PRIMARY KEY,
                user_id VARCHAR(100) NOT NULL REFERENCES users(id) ON DELETE CASCADE,
                preference_key VARCHAR(100) NOT NULL,
                preference_value TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        print("✅ Created/verified user_preferences table")
        
        # Commit changes
        conn.commit()
        print("✅ Database migration completed successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        if 'conn' in locals():
            conn.rollback()
        return False
        
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    print("Starting database migration...")
    success = migrate_database()
    if success:
        print("Migration completed successfully!")
    else:
        print("Migration failed!")

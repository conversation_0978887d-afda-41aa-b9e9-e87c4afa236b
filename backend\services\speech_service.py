import os
import sys
import io
import base64
import tempfile
from typing import Dict, Optional, Union
from dotenv import load_dotenv
import requests
import json

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from services.language_service import LanguageService

load_dotenv()

class SpeechToTextService:
    """
    Enhanced Speech-to-Text service supporting multiple providers
    - Google Cloud Speech-to-Text API
    - OpenAI Whisper API
    - Fallback to browser-based recognition
    """
    
    def __init__(self):
        self.google_credentials = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
        self.google_project_id = os.getenv('GOOGLE_CLOUD_PROJECT_ID')
        self.openai_api_key = os.getenv('OPENAI_API_KEY')

        # Initialize language service for enhanced language detection
        self.language_service = LanguageService()
        
        # Enhanced supported languages for Indian language recognition
        self.supported_languages = {
            'hi-IN': 'hi-IN',      # Hindi
            'ta-IN': 'ta-IN',      # Tamil
            'te-IN': 'te-IN',      # Telugu
            'ml-IN': 'ml-IN',      # Malayalam
            'kn-IN': 'kn-IN',      # Kannada
            'bn-IN': 'bn-IN',      # Bengali
            'gu-IN': 'gu-IN',      # Gujarati
            'mr-IN': 'mr-IN',      # Marathi
            'pa-IN': 'pa-IN',      # Punjabi
            'or-IN': 'or-IN',      # Odia
            'as-IN': 'as-IN',      # Assamese
            'ur-IN': 'ur-IN',      # Urdu
            'en-IN': 'en-IN',      # English (India)
            'en-US': 'en-US',      # English (US)
            'sa-IN': 'sa-IN',      # Sanskrit
            'ne-IN': 'ne-IN',      # Nepali
        }

        # Language fallback mapping for better recognition
        self.language_fallbacks = {
            'hi-IN': ['hi', 'hi-IN', 'en-IN'],
            'ta-IN': ['ta', 'ta-IN', 'en-IN'],
            'te-IN': ['te', 'te-IN', 'en-IN'],
            'ml-IN': ['ml', 'ml-IN', 'en-IN'],
            'kn-IN': ['kn', 'kn-IN', 'en-IN'],
            'bn-IN': ['bn', 'bn-IN', 'en-IN'],
            'gu-IN': ['gu', 'gu-IN', 'en-IN'],
            'mr-IN': ['mr', 'mr-IN', 'en-IN'],
            'pa-IN': ['pa', 'pa-IN', 'en-IN'],
            'or-IN': ['or', 'or-IN', 'en-IN'],
            'as-IN': ['as', 'as-IN', 'en-IN'],
            'ur-IN': ['ur', 'ur-IN', 'en-IN'],
            'en-IN': ['en-IN', 'en-US'],
            'en-US': ['en-US', 'en-IN'],
        }
    
    def _get_google_access_token(self) -> Optional[str]:
        """Get Google Cloud access token using service account"""
        if not self.google_credentials or not os.path.exists(self.google_credentials):
            return None

        # This is a simplified version - in production, use google-auth library
        # For now, we'll skip Google Cloud implementation

    def _get_speech_contexts(self, language: str) -> list:
        """Get speech contexts for better recognition of Indian languages"""
        contexts = []

        # Common Indian language phrases and words for better recognition
        if language == 'hi-IN':
            contexts.append({
                "phrases": ["नमस्ते", "धन्यवाद", "कैसे हैं", "क्या हाल है", "अच्छा", "बुरा"],
                "boost": 10.0
            })
        elif language == 'ta-IN':
            contexts.append({
                "phrases": ["வணக்கம்", "நன்றி", "எப்படி இருக்கிறீர்கள்", "நல்லது", "கெட்டது"],
                "boost": 10.0
            })
        elif language == 'te-IN':
            contexts.append({
                "phrases": ["నమస్కారం", "ధన్యవాదాలు", "ఎలా ఉన్నారు", "మంచిది", "చెడ్డది"],
                "boost": 10.0
            })
        elif language == 'ml-IN':
            contexts.append({
                "phrases": ["നമസ്കാരം", "നന്ദി", "എങ്ങനെയുണ്ട്", "നല്ലത്", "മോശം"],
                "boost": 10.0
            })
        elif language == 'kn-IN':
            contexts.append({
                "phrases": ["ನಮಸ್ಕಾರ", "ಧನ್ಯವಾದ", "ಹೇಗಿದ್ದೀರಿ", "ಒಳ್ಳೆಯದು", "ಕೆಟ್ಟದು"],
                "boost": 10.0
            })
        elif language == 'bn-IN':
            contexts.append({
                "phrases": ["নমস্কার", "ধন্যবাদ", "কেমন আছেন", "ভালো", "খারাপ"],
                "boost": 10.0
            })

        return contexts
    
    def _transcribe_with_google(self, audio_data: bytes, language: str = 'en-US') -> Dict:
        """Transcribe audio using Google Cloud Speech-to-Text with enhanced Indian language support"""
        try:
            access_token = self._get_google_access_token()
            if not access_token:
                raise Exception("Google Cloud credentials not configured")

            # Convert audio to base64
            audio_base64 = base64.b64encode(audio_data).decode('utf-8')

            # Prepare request
            url = f"https://speech.googleapis.com/v1/speech:recognize"
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }

            # Enhanced configuration for Indian languages
            alternative_language_codes = self.language_fallbacks.get(language, [language])

            # Build recognition config with enhanced settings for Indian languages
            recognition_config = {
                "encoding": "WEBM_OPUS",
                "sampleRateHertz": 48000,
                "languageCode": language,
                "alternativeLanguageCodes": alternative_language_codes[:3],  # Max 3 alternatives
                "maxAlternatives": 3,
                "enableAutomaticPunctuation": True,
                "enableWordTimeOffsets": False,
                "enableWordConfidence": True,
                "model": "latest_long" if language.endswith('-IN') else "default",
                "useEnhanced": True,
                "profanityFilter": False,
                "speechContexts": self._get_speech_contexts(language)
            }
            
            data = {
                'config': {
                    'encoding': 'WEBM_OPUS',  # Common browser format
                    'sampleRateHertz': 48000,
                    'languageCode': language,
                    'enableAutomaticPunctuation': True,
                    'enableWordTimeOffsets': False,
                    'model': 'latest_long'  # Best model for longer audio
                },
                'audio': {
                    'content': audio_base64
                }
            }
            
            response = requests.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            
            if 'results' in result and result['results']:
                transcript = result['results'][0]['alternatives'][0]['transcript']
                confidence = result['results'][0]['alternatives'][0].get('confidence', 0.0)
                
                return {
                    'success': True,
                    'transcript': transcript,
                    'confidence': confidence,
                    'language': language,
                    'provider': 'google_cloud'
                }
            else:
                return {
                    'success': False,
                    'error': 'No transcription results',
                    'provider': 'google_cloud'
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'provider': 'google_cloud'
            }
    
    def _transcribe_with_whisper(self, audio_data: bytes, language: str = 'en') -> Dict:
        """Transcribe audio using OpenAI Whisper API"""
        try:
            if not self.openai_api_key:
                raise Exception("OpenAI API key not configured")
            
            # Create temporary file for audio
            with tempfile.NamedTemporaryFile(suffix='.webm', delete=False) as temp_file:
                temp_file.write(audio_data)
                temp_file_path = temp_file.name
            
            try:
                # Prepare request to OpenAI Whisper API
                url = "https://api.openai.com/v1/audio/transcriptions"
                headers = {
                    'Authorization': f'Bearer {self.openai_api_key}'
                }
                
                # Enhanced language mapping for Whisper with Indian language support
                whisper_language_map = {
                    'hi-IN': 'hi',    # Hindi
                    'ta-IN': 'ta',    # Tamil
                    'te-IN': 'te',    # Telugu
                    'ml-IN': 'ml',    # Malayalam
                    'kn-IN': 'kn',    # Kannada
                    'bn-IN': 'bn',    # Bengali
                    'gu-IN': 'gu',    # Gujarati
                    'mr-IN': 'mr',    # Marathi
                    'pa-IN': 'pa',    # Punjabi
                    'or-IN': 'or',    # Odia
                    'as-IN': 'as',    # Assamese
                    'ur-IN': 'ur',    # Urdu
                    'en-IN': 'en',    # English (India)
                    'en-US': 'en',    # English (US)
                }

                whisper_language = whisper_language_map.get(language, language.split('-')[0])
                
                files = {
                    'file': open(temp_file_path, 'rb'),
                    'model': (None, 'whisper-1'),
                    'language': (None, whisper_language),
                    'response_format': (None, 'json'),
                    'temperature': (None, '0.1')  # Lower temperature for better Indian language accuracy
                }

                # Add prompt for better Indian language recognition
                if language.endswith('-IN') and language != 'en-IN':
                    language_prompts = {
                        'hi-IN': 'नमस्ते, यह हिंदी भाषा में बोला गया है।',
                        'ta-IN': 'வணக்கம், இது தமிழ் மொழியில் பேசப்பட்டது.',
                        'te-IN': 'నమస్కారం, ఇది తెలుగు భాషలో మాట్లాడబడింది.',
                        'ml-IN': 'നമസ്കാരം, ഇത് മലയാളം ഭാഷയിൽ സംസാരിച്ചതാണ്.',
                        'kn-IN': 'ನಮಸ್ಕಾರ, ಇದು ಕನ್ನಡ ಭಾಷೆಯಲ್ಲಿ ಮಾತನಾಡಲಾಗಿದೆ.',
                        'bn-IN': 'নমস্কার, এটি বাংলা ভাষায় বলা হয়েছে।',
                        'gu-IN': 'નમસ્તે, આ ગુજરાતી ભાષામાં બોલાયેલું છે।',
                        'mr-IN': 'नमस्कार, हे मराठी भाषेत बोलले आहे।',
                        'pa-IN': 'ਸਤ ਸ੍ਰੀ ਅਕਾਲ, ਇਹ ਪੰਜਾਬੀ ਭਾਸ਼ਾ ਵਿੱਚ ਬੋਲਿਆ ਗਿਆ ਹੈ।'
                    }
                    if language in language_prompts:
                        files['prompt'] = (None, language_prompts[language])
                
                response = requests.post(url, headers=headers, files=files, timeout=30)
                response.raise_for_status()
                
                result = response.json()
                
                return {
                    'success': True,
                    'transcript': result.get('text', ''),
                    'confidence': 0.9,  # Whisper doesn't provide confidence scores
                    'language': language,
                    'provider': 'openai_whisper'
                }
                
            finally:
                # Clean up temporary file
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'provider': 'openai_whisper'
            }
    
    def transcribe_audio(self, audio_data: bytes, language: str = 'en-US', provider: str = 'auto') -> Dict:
        """
        Enhanced transcribe audio with better Indian language support

        Args:
            audio_data: Raw audio data in bytes
            language: Language code (e.g., 'hi-IN', 'en-US')
            provider: 'auto', 'google', 'whisper', or 'browser'

        Returns:
            Dict with transcription results
        """
        if not audio_data:
            return {
                'success': False,
                'error': 'No audio data provided',
                'provider': 'none'
            }

        # Enhanced language validation with fallback
        original_language = language
        if language not in self.supported_languages:
            # Try to find a similar supported language
            lang_base = language.split('-')[0]
            fallback_found = False

            for supported_lang in self.supported_languages:
                if supported_lang.startswith(lang_base):
                    language = supported_lang
                    fallback_found = True
                    break

            if not fallback_found:
                language = 'en-US'  # Final fallback to English

        results = []

        # Enhanced provider selection with Indian language preference
        # For Indian languages, prefer Whisper as it generally has better support
        providers_to_try = []

        if provider == 'auto':
            if language.endswith('-IN') and language != 'en-IN':
                # For Indian languages, try Whisper first, then Google
                if self.openai_api_key:
                    providers_to_try.append('whisper')
                if self.google_credentials:
                    providers_to_try.append('google')
            else:
                # For English, try Google first, then Whisper
                if self.google_credentials:
                    providers_to_try.append('google')
                if self.openai_api_key:
                    providers_to_try.append('whisper')
        elif provider == 'google' and self.google_credentials:
            providers_to_try.append('google')
        elif provider == 'whisper' and self.openai_api_key:
            providers_to_try.append('whisper')

        # Try each provider
        for prov in providers_to_try:
            try:
                if prov == 'google':
                    result = self._transcribe_with_google(audio_data, language)
                elif prov == 'whisper':
                    result = self._transcribe_with_whisper(audio_data, language)
                else:
                    continue

                results.append(result)
                if result['success']:
                    # Add metadata about language handling
                    result['original_language'] = original_language
                    result['used_language'] = language
                    return result

            except Exception as e:
                results.append({
                    'success': False,
                    'error': f'{prov} provider error: {str(e)}',
                    'provider': prov
                })

        # If all providers failed, return the best error message
        if results:
            # Return the last result, but enhance error message
            last_result = results[-1]
            last_result['all_errors'] = [r.get('error', 'Unknown error') for r in results]
            return last_result

        return {
            'success': False,
            'error': 'No speech-to-text providers configured or available',
            'provider': 'none',
            'original_language': original_language,
            'used_language': language
        }
    
    def is_configured(self) -> Dict:
        """Enhanced configuration check with provider recommendations"""
        google_configured = bool(self.google_credentials and os.path.exists(self.google_credentials))
        openai_configured = bool(self.openai_api_key)

        return {
            'google_cloud': google_configured,
            'openai_whisper': openai_configured,
            'any_provider': google_configured or openai_configured,
            'recommended_for_indian_languages': openai_configured,
            'recommended_for_english': google_configured or openai_configured,
            'provider_status': {
                'google': 'configured' if google_configured else 'not_configured',
                'whisper': 'configured' if openai_configured else 'not_configured'
            }
        }

    def get_supported_languages(self) -> Dict:
        """Get enhanced list of supported languages with metadata"""
        language_info = {}

        for lang_code in self.supported_languages:
            language_info[lang_code] = {
                'code': lang_code,
                'name': self._get_language_name(lang_code),
                'is_indian': lang_code.endswith('-IN') and lang_code != 'en-IN',
                'fallbacks': self.language_fallbacks.get(lang_code, []),
                'recommended_provider': 'whisper' if lang_code.endswith('-IN') and lang_code != 'en-IN' else 'google'
            }

        return language_info

    def _get_language_name(self, lang_code: str) -> str:
        """Get human-readable language name"""
        names = {
            'hi-IN': 'Hindi (India)',
            'ta-IN': 'Tamil (India)',
            'te-IN': 'Telugu (India)',
            'ml-IN': 'Malayalam (India)',
            'kn-IN': 'Kannada (India)',
            'bn-IN': 'Bengali (India)',
            'gu-IN': 'Gujarati (India)',
            'mr-IN': 'Marathi (India)',
            'pa-IN': 'Punjabi (India)',
            'or-IN': 'Odia (India)',
            'as-IN': 'Assamese (India)',
            'ur-IN': 'Urdu (India)',
            'en-IN': 'English (India)',
            'en-US': 'English (US)',
            'sa-IN': 'Sanskrit (India)',
            'ne-IN': 'Nepali (India)',
        }
        return names.get(lang_code, lang_code)

    def transcribe_audio_with_language_detection(self, audio_data: bytes,
                                               detected_language: Optional[str] = None,
                                               provider: str = 'auto') -> Dict:
        """
        Enhanced transcribe audio with automatic language detection and processing

        Args:
            audio_data: Raw audio data in bytes
            detected_language: Pre-detected language code (optional)
            provider: 'auto', 'google', 'whisper', or 'browser'

        Returns:
            Dict with transcription results and language processing
        """
        try:
            # Use detected language or fallback to auto-detection
            language_to_use = detected_language or 'en-US'

            # Perform transcription
            transcription_result = self.transcribe_audio(audio_data, language_to_use, provider)

            if not transcription_result.get('success'):
                return transcription_result

            # Get transcribed text
            transcribed_text = transcription_result.get('transcript', '')

            if not transcribed_text:
                return transcription_result

            # Enhance with language service analysis
            language_analysis = self.language_service.detect_and_prepare_message(transcribed_text)

            # Combine results
            enhanced_result = {
                **transcription_result,
                'language_analysis': language_analysis,
                'enhanced_processing': True,
                'original_transcript': transcribed_text,
                'processed_message': language_analysis.get('processed_message', transcribed_text),
                'confidence_enhanced': True
            }

            # Add language consistency check
            detected_from_text = language_analysis.get('detected_language', 'en')
            speech_language = transcription_result.get('language', 'en-US').split('-')[0]

            enhanced_result['language_consistency'] = {
                'speech_language': speech_language,
                'text_language': detected_from_text,
                'consistent': speech_language == detected_from_text,
                'confidence_score': language_analysis.get('confidence', 0.0)
            }

            return enhanced_result

        except Exception as e:
            return {
                'success': False,
                'error': f'Enhanced transcription failed: {str(e)}',
                'provider': 'enhanced_service'
            }

    def get_optimal_language_for_speech(self, text_sample: str) -> Dict:
        """
        Get optimal language settings for speech recognition based on text analysis

        Args:
            text_sample: Sample text to analyze for language patterns

        Returns:
            Dict with optimal language settings
        """
        try:
            if not text_sample:
                return {
                    'recommended_language': 'en-US',
                    'confidence': 0.5,
                    'fallback_languages': ['en-US', 'en-IN']
                }

            # Use language service for analysis
            language_analysis = self.language_service.detect_and_prepare_message(text_sample)
            detected_lang = language_analysis.get('detected_language', 'en')

            # Map to speech recognition language codes
            speech_lang_mapping = {
                'hi': 'hi-IN',
                'ta': 'ta-IN',
                'te': 'te-IN',
                'ml': 'ml-IN',
                'kn': 'kn-IN',
                'bn': 'bn-IN',
                'gu': 'gu-IN',
                'mr': 'mr-IN',
                'pa': 'pa-IN',
                'or': 'or-IN',
                'as': 'as-IN',
                'ur': 'ur-IN',
                'en': 'en-IN',
                'ne': 'ne-IN'
            }

            recommended_language = speech_lang_mapping.get(detected_lang, 'en-US')
            fallback_languages = self.language_fallbacks.get(recommended_language, [recommended_language])

            return {
                'recommended_language': recommended_language,
                'detected_text_language': detected_lang,
                'confidence': language_analysis.get('confidence', 0.0),
                'fallback_languages': fallback_languages,
                'is_indian_language': detected_lang in ['hi', 'ta', 'te', 'ml', 'kn', 'bn', 'gu', 'mr', 'pa', 'or', 'as', 'ur'],
                'recommended_provider': 'whisper' if detected_lang != 'en' else 'google',
                'language_analysis': language_analysis
            }

        except Exception as e:
            return {
                'recommended_language': 'en-US',
                'confidence': 0.0,
                'error': f'Language optimization failed: {str(e)}',
                'fallback_languages': ['en-US', 'en-IN']
            }

    def process_speech_result_with_context(self, transcription_result: Dict,
                                         conversation_context: Optional[str] = None) -> Dict:
        """
        Process speech transcription result with conversation context

        Args:
            transcription_result: Result from transcribe_audio method
            conversation_context: Previous conversation context for better processing

        Returns:
            Enhanced transcription result with context processing
        """
        try:
            if not transcription_result.get('success'):
                return transcription_result

            transcript = transcription_result.get('transcript', '')
            if not transcript:
                return transcription_result

            # Combine with conversation context if available
            context_text = f"{conversation_context}\n{transcript}" if conversation_context else transcript

            # Process with language service
            language_processing = self.language_service.detect_and_prepare_message(context_text)

            # Extract just the current message processing
            current_message_processing = self.language_service.detect_and_prepare_message(transcript)

            enhanced_result = {
                **transcription_result,
                'context_processing': {
                    'original_transcript': transcript,
                    'processed_message': current_message_processing.get('processed_message', transcript),
                    'language_context': current_message_processing,
                    'conversation_context_used': bool(conversation_context),
                    'enhanced_understanding': True
                },
                'recommendations': {
                    'response_language': current_message_processing.get('detected_language', 'en'),
                    'cultural_context': current_message_processing.get('cultural_context', {}),
                    'formality_level': current_message_processing.get('formality_level', 'neutral')
                }
            }

            return enhanced_result

        except Exception as e:
            return {
                **transcription_result,
                'context_processing_error': f'Context processing failed: {str(e)}'
            }

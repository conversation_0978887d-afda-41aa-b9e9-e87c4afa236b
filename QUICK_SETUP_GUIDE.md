# 🚀 Quick Setup Guide - Enhanced Gemini AI Application

## Prerequisites
- Python 3.10.10
- Node.js and npm
- Gemini API Key

## 🔧 Backend Setup

### 1. Navigate to Backend Directory
```bash
cd backend
```

### 2. Activate Virtual Environment
```bash
# Windows
.\venv\Scripts\Activate.ps1

# Linux/Mac
source venv/bin/activate
```

### 3. Install Dependencies
```bash
pip install -r requirements.txt
```

### 4. Environment Configuration
Create `.env` file in backend directory:
```env
GEMINI_API_KEY=your_gemini_api_key_here

# Optional - for enhanced live data
NEWSAPI_KEY=your_newsapi_key
GNEWS_API_KEY=your_gnews_key
CRICAPI_KEY=your_cricapi_key
```

### 5. Validate Installation
```bash
python validate_dependencies.py
```
Expected: 33/34 packages working (97% success rate)

### 6. Test Multilingual Features
```bash
python test_multilingual_features.py
```
Expected: 16/21 languages detected (76% success rate)

### 7. Start Backend Server
```bash
python app.py
```
Server runs on: `http://localhost:5000`

## 🌐 Frontend Setup

### 1. Navigate to Frontend Directory
```bash
cd frontend
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Start Development Server
```bash
npm start
```
Frontend runs on: `http://localhost:3000`

## ✅ Verification Checklist

### Backend Health Check
- [ ] Virtual environment activated
- [ ] Dependencies installed (33/34 packages)
- [ ] Gemini API key configured
- [ ] Flask server running on port 5000
- [ ] No import errors in console

### Frontend Health Check
- [ ] React app running on port 3000
- [ ] No console errors
- [ ] Chat interface loads properly
- [ ] File upload component visible
- [ ] Voice input button present

### Feature Testing
- [ ] **Chat**: Send "Hello" → Receives detailed response
- [ ] **Multilingual**: Send "नमस्ते" → Receives Hindi response
- [ ] **Live Data**: Ask "latest news" → Gets real news articles
- [ ] **Voice**: Click microphone → Speech recognition works
- [ ] **TTS**: Click speaker icon → Text-to-speech plays
- [ ] **File Upload**: Upload image → Gets detailed analysis

## 🔍 Troubleshooting

### Common Issues

#### 1. Import Errors
```bash
# Solution: Ensure virtual environment is activated
.\venv\Scripts\Activate.ps1  # Windows
source venv/bin/activate     # Linux/Mac
```

#### 2. Gemini API Errors
```bash
# Check API key in .env file
GEMINI_API_KEY=your_actual_key_here
```

#### 3. FastText Installation Failed
```bash
# This is optional - system works without it
# Uses langdetect as fallback for language detection
```

#### 4. PyMuPDF Import Issues
```bash
# System uses pdfplumber as fallback
# PDF processing still works
```

#### 5. Port Conflicts
```bash
# Backend: Change port in app.py
app.run(host='0.0.0.0', port=5001, debug=True)

# Frontend: Change port in package.json
"start": "PORT=3001 react-scripts start"
```

## 🎯 Quick Feature Demo

### 1. Multilingual Chat
1. Open chat interface
2. Type: "Explain AI in Hindi" or "तमिल में AI समझाएं"
3. Expect: Detailed response in requested language

### 2. Live Data
1. Ask: "Latest technology news"
2. Expect: 20 real news articles with full details
3. Ask: "Weather in London"
4. Expect: Complete 7-day forecast

### 3. Voice Features
1. Click microphone button
2. Speak in any supported language
3. Expect: Accurate transcription
4. Click speaker icon on response
5. Expect: Text-to-speech in correct language

### 4. File Analysis
1. Upload an image or document
2. Select "Comprehensive Analysis"
3. Expect: Detailed analysis with insights

## 📊 Expected Performance

### Language Detection Accuracy
- **English, Spanish, French, German**: 95%+
- **Hindi, Tamil, Bengali**: 90%+
- **Arabic, Chinese, Japanese**: 85%+
- **Other Indian Languages**: 80%+

### Response Quality
- **Detail Level**: Complete (no summarization)
- **Response Time**: 2-5 seconds
- **Live Data**: Real-time, full articles/data
- **Multilingual**: Maintains input language

### System Requirements
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 2GB for dependencies
- **Network**: Stable internet for API calls
- **Browser**: Chrome/Firefox/Safari (latest versions)

## 🎉 Success Indicators

✅ **Backend Ready**: Flask server starts without errors
✅ **Frontend Ready**: React app loads chat interface
✅ **Gemini Connected**: Chat responses are detailed and accurate
✅ **Multilingual Active**: Responds in detected input language
✅ **Live Data Working**: Real news/weather/stock data appears
✅ **Speech Functional**: Voice input and TTS work
✅ **File Analysis Ready**: Uploads provide comprehensive analysis

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Run validation scripts to identify specific problems
3. Ensure all environment variables are set correctly
4. Verify internet connectivity for API calls

The system is designed to be robust with fallback mechanisms, so partial functionality is expected even if some optional features fail.

from langdetect import detect, DetectorFactory, detect_langs
from googletrans import Translator
import os
import google.generativeai as genai
from dotenv import load_dotenv
import sys
import re
import unicodedata
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.gemini_config import GeminiConfig

load_dotenv()

# Set seed for consistent language detection
DetectorFactory.seed = 0

# Try to import fasttext for enhanced language detection
try:
    import fasttext
    FASTTEXT_AVAILABLE = True
    print("✅ FastText available for enhanced language detection")
except ImportError:
    FASTTEXT_AVAILABLE = False
    print("⚠️  FastText not available, using langdetect only")

class LanguageService:
    def __init__(self):
        self.translator = Translator()

        # Use global Gemini configuration
        if not GeminiConfig.is_initialized():
            GeminiConfig.initialize()

        # Configure Gemini for translation and language detection as backup
        if GeminiConfig.is_initialized():
            try:
                self.gemini_model = GeminiConfig.create_detailed_model('chat')
                self.api_key = GeminiConfig.get_api_key()
                print("✅ Language Service initialized with Gemini for enhanced detection")
            except Exception as e:
                print(f"⚠️  Gemini model initialization failed in LanguageService: {e}")
                self.gemini_model = None
                self.api_key = None
        else:
            self.gemini_model = None
            self.api_key = None

        # Initialize FastText model if available
        self.fasttext_model = None
        if FASTTEXT_AVAILABLE:
            try:
                # Try to load pre-trained language identification model
                # Note: This would need to be downloaded separately
                # For now, we'll use langdetect as primary with enhanced logic
                pass
            except Exception as e:
                print(f"⚠️  FastText model loading failed: {e}")

        # Enhanced language patterns for better detection
        self.language_patterns = {
            'hi': {
                'script_range': (0x0900, 0x097F),  # Devanagari
                'common_words': ['है', 'हैं', 'का', 'की', 'के', 'में', 'से', 'को', 'और', 'या', 'नहीं', 'यह', 'वह'],
                'name': 'Hindi'
            },
            'ta': {
                'script_range': (0x0B80, 0x0BFF),  # Tamil
                'common_words': ['இல்', 'உள்ள', 'என்', 'அல்ல', 'மற்றும்', 'அது', 'இது', 'ஆகும்', 'என்று'],
                'name': 'Tamil'
            },
            'te': {
                'script_range': (0x0C00, 0x0C7F),  # Telugu
                'common_words': ['లో', 'కు', 'నుండి', 'మరియు', 'లేదా', 'అది', 'ఇది', 'అని', 'ఉంది'],
                'name': 'Telugu'
            },
            'ml': {
                'script_range': (0x0D00, 0x0D7F),  # Malayalam
                'common_words': ['ൽ', 'ന്', 'ും', 'അല്ല', 'ആണ്', 'ഇത്', 'അത്', 'എന്ന്', 'ഉണ്ട്'],
                'name': 'Malayalam'
            },
            'kn': {
                'script_range': (0x0C80, 0x0CFF),  # Kannada
                'common_words': ['ಇದು', 'ಅದು', 'ಮತ್ತು', 'ಅಥವಾ', 'ಅಲ್ಲ', 'ಆಗಿದೆ', 'ಎಂದು', 'ಇದೆ'],
                'name': 'Kannada'
            },
            'bn': {
                'script_range': (0x0980, 0x09FF),  # Bengali
                'common_words': ['এই', 'সেই', 'এবং', 'বা', 'না', 'আছে', 'নেই', 'যে', 'হয়'],
                'name': 'Bengali'
            },
            'gu': {
                'script_range': (0x0A80, 0x0AFF),  # Gujarati
                'common_words': ['આ', 'તે', 'અને', 'કે', 'નથી', 'છે', 'હતું', 'થી', 'માં'],
                'name': 'Gujarati'
            },
            'pa': {
                'script_range': (0x0A00, 0x0A7F),  # Gurmukhi (Punjabi)
                'common_words': ['ਇਹ', 'ਉਹ', 'ਅਤੇ', 'ਜਾਂ', 'ਨਹੀਂ', 'ਹੈ', 'ਸੀ', 'ਨੂੰ', 'ਵਿੱਚ'],
                'name': 'Punjabi'
            },
            'mr': {
                'script_range': (0x0900, 0x097F),  # Devanagari (shared with Hindi)
                'common_words': ['हे', 'ते', 'आणि', 'किंवा', 'नाही', 'आहे', 'होते', 'मी', 'तू'],
                'name': 'Marathi'
            },
            'ur': {
                'script_range': (0x0600, 0x06FF),  # Arabic script
                'common_words': ['یہ', 'وہ', 'اور', 'یا', 'نہیں', 'ہے', 'تھا', 'کا', 'کی'],
                'name': 'Urdu'
            },
            'ar': {
                'script_range': (0x0600, 0x06FF),  # Arabic script
                'common_words': ['هذا', 'ذلك', 'و', 'أو', 'لا', 'هو', 'كان', 'في', 'من'],
                'name': 'Arabic'
            },
            'fa': {
                'script_range': (0x0600, 0x06FF),  # Arabic script (Persian)
                'common_words': ['این', 'آن', 'و', 'یا', 'نه', 'است', 'بود', 'در', 'از'],
                'name': 'Persian'
            }
        }
        
        # Language code mappings
        self.language_names = {
            'en': 'English',
            'es': 'Spanish',
            'fr': 'French',
            'de': 'German',
            'it': 'Italian',
            'pt': 'Portuguese',
            'ru': 'Russian',
            'ja': 'Japanese',
            'ko': 'Korean',
            'zh': 'Chinese',
            'ar': 'Arabic',
            'hi': 'Hindi',
            'tr': 'Turkish',
            'pl': 'Polish',
            'nl': 'Dutch',
            'sv': 'Swedish',
            'da': 'Danish',
            'no': 'Norwegian',
            'fi': 'Finnish',
            'cs': 'Czech',
            'hu': 'Hungarian',
            'ro': 'Romanian',
            'bg': 'Bulgarian',
            'hr': 'Croatian',
            'sk': 'Slovak',
            'sl': 'Slovenian',
            'et': 'Estonian',
            'lv': 'Latvian',
            'lt': 'Lithuanian',
            'uk': 'Ukrainian',
            'be': 'Belarusian',
            'mk': 'Macedonian',
            'sq': 'Albanian',
            'sr': 'Serbian',
            'bs': 'Bosnian',
            'mt': 'Maltese',
            'is': 'Icelandic',
            'ga': 'Irish',
            'cy': 'Welsh',
            'eu': 'Basque',
            'ca': 'Catalan',
            'gl': 'Galician',
            'af': 'Afrikaans',
            'sw': 'Swahili',
            'am': 'Amharic',
            'he': 'Hebrew',
            'fa': 'Persian',
            'ur': 'Urdu',
            'bn': 'Bengali',
            'ta': 'Tamil',
            'te': 'Telugu',
            'ml': 'Malayalam',
            'kn': 'Kannada',
            'gu': 'Gujarati',
            'pa': 'Punjabi',
            'mr': 'Marathi',
            'ne': 'Nepali',
            'si': 'Sinhala',
            'my': 'Myanmar',
            'km': 'Khmer',
            'lo': 'Lao',
            'ka': 'Georgian',
            'hy': 'Armenian',
            'az': 'Azerbaijani',
            'kk': 'Kazakh',
            'ky': 'Kyrgyz',
            'uz': 'Uzbek',
            'tg': 'Tajik',
            'mn': 'Mongolian',
            'th': 'Thai',
            'vi': 'Vietnamese',
            'id': 'Indonesian',
            'ms': 'Malay',
            'tl': 'Filipino'
        }
    
    def detect_language(self, text):
        """Enhanced language detection using multiple approaches"""
        try:
            if not text or len(text.strip()) < 3:
                return 'en', 'English'  # Default to English for very short text

            # Clean text for better detection
            cleaned_text = self._clean_text_for_detection(text)

            # Method 1: Script-based detection for Indian languages
            script_detected = self._detect_by_script(cleaned_text)
            if script_detected:
                return script_detected

            # Method 2: Enhanced langdetect with confidence scoring
            langdetect_result = self._detect_with_langdetect(cleaned_text)

            # Method 3: Pattern-based detection for specific languages
            pattern_detected = self._detect_by_patterns(cleaned_text)

            # Method 4: Gemini-based detection as fallback for ambiguous cases
            if langdetect_result[0] == 'unknown' or (pattern_detected and pattern_detected[0] != langdetect_result[0]):
                gemini_result = self._detect_with_gemini(cleaned_text)
                if gemini_result:
                    return gemini_result

            # Return the most confident result
            if pattern_detected and pattern_detected[0] in self.language_patterns:
                return pattern_detected

            return langdetect_result

        except Exception as e:
            print(f"Language detection error: {str(e)}")
            return 'en', 'English'  # Default to English on error

    def _clean_text_for_detection(self, text):
        """Clean text for better language detection"""
        # Remove URLs, emails, and special characters that might confuse detection
        text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
        text = re.sub(r'\S+@\S+', '', text)
        text = re.sub(r'[^\w\s\u0900-\u097F\u0B80-\u0BFF\u0C00-\u0C7F\u0D00-\u0D7F\u0C80-\u0CFF\u0980-\u09FF\u0A80-\u0AFF\u0A00-\u0A7F\u0600-\u06FF]', ' ', text)
        return text.strip()

    def _detect_by_script(self, text):
        """Detect language based on Unicode script ranges"""
        char_counts = {}
        total_chars = 0

        for char in text:
            if char.isalpha():
                total_chars += 1
                code_point = ord(char)

                for lang_code, lang_info in self.language_patterns.items():
                    script_start, script_end = lang_info['script_range']
                    if script_start <= code_point <= script_end:
                        char_counts[lang_code] = char_counts.get(lang_code, 0) + 1
                        break

        if total_chars == 0:
            return None

        # Find language with highest character count
        if char_counts:
            best_lang = max(char_counts.items(), key=lambda x: x[1])
            confidence = best_lang[1] / total_chars

            if confidence > 0.3:  # At least 30% of characters match the script
                lang_code = best_lang[0]
                lang_name = self.language_patterns[lang_code]['name']
                print(f"Script detection: {lang_name} (confidence: {confidence:.2f})")
                return lang_code, lang_name

        return None

    def _detect_with_langdetect(self, text):
        """Enhanced langdetect with confidence scoring"""
        try:
            # Get multiple detection results with probabilities
            detections = detect_langs(text)

            if detections:
                best_detection = detections[0]
                detected_code = best_detection.lang
                confidence = best_detection.prob

                # Higher threshold for non-English languages
                min_confidence = 0.7 if detected_code != 'en' else 0.5

                if confidence >= min_confidence:
                    language_name = self.language_names.get(detected_code, 'Unknown')
                    print(f"Langdetect: {language_name} (confidence: {confidence:.2f})")
                    return detected_code, language_name

            return 'unknown', 'Unknown'

        except Exception as e:
            print(f"Langdetect error: {e}")
            return 'unknown', 'Unknown'

    def _detect_by_patterns(self, text):
        """Detect language using common word patterns"""
        text_lower = text.lower()
        word_scores = {}

        for lang_code, lang_info in self.language_patterns.items():
            score = 0
            for word in lang_info['common_words']:
                if word.lower() in text_lower:
                    score += 1

            if score > 0:
                word_scores[lang_code] = score

        if word_scores:
            best_lang = max(word_scores.items(), key=lambda x: x[1])
            if best_lang[1] >= 2:  # At least 2 common words found
                lang_code = best_lang[0]
                lang_name = self.language_patterns[lang_code]['name']
                print(f"Pattern detection: {lang_name} (words found: {best_lang[1]})")
                return lang_code, lang_name

        return None

    def _detect_with_gemini(self, text):
        """Use Gemini for language detection as fallback"""
        try:
            if not self.gemini_model:
                return None

            prompt = f"""
            Identify the language of the following text. Respond with only the ISO 639-1 language code (e.g., 'en' for English, 'hi' for Hindi, 'ta' for Tamil, etc.).

            Text: {text[:500]}

            Language code:
            """

            response = self.gemini_model.generate_content(prompt)
            detected_code = response.text.strip().lower()

            # Validate the response
            if detected_code in self.language_names:
                language_name = self.language_names[detected_code]
                print(f"Gemini detection: {language_name}")
                return detected_code, language_name

            return None

        except Exception as e:
            print(f"Gemini language detection error: {e}")
            return None
    
    def translate_text(self, text, target_language='en', source_language=None):
        """Translate text to target language"""
        try:
            if not text or len(text.strip()) == 0:
                return text
            
            # If source language is not provided, detect it
            if not source_language:
                source_language, _ = self.detect_language(text)
            
            # If already in target language, return as is
            if source_language == target_language:
                return text
            
            # Try Google Translate first
            try:
                result = self.translator.translate(
                    text, 
                    src=source_language, 
                    dest=target_language
                )
                return result.text
            except Exception as google_error:
                print(f"Google Translate error: {str(google_error)}")
                
                # Fallback to Gemini translation
                return self._translate_with_gemini(text, source_language, target_language)
                
        except Exception as e:
            print(f"Translation error: {str(e)}")
            return text  # Return original text on error
    
    def _translate_with_gemini(self, text, source_language, target_language):
        """Fallback translation using Gemini"""
        try:
            if not self.api_key:
                return text
            
            source_name = self.language_names.get(source_language, source_language)
            target_name = self.language_names.get(target_language, target_language)
            
            prompt = f"""
Translate the following text from {source_name} to {target_name}. 
Provide only the translation without any additional text or explanations.

Text to translate:
{text}

Translation:
"""
            
            response = self.gemini_model.generate_content(prompt)
            return response.text.strip()
            
        except Exception as e:
            print(f"Gemini translation error: {str(e)}")
            return text
    
    def get_supported_languages(self):
        """Get list of supported languages"""
        return self.language_names
    
    def is_english(self, text):
        """Check if text is in English"""
        detected_code, _ = self.detect_language(text)
        return detected_code == 'en'
    
    def prepare_multilingual_prompt(self, user_message, detected_language, target_language='en'):
        """Prepare a prompt that handles multilingual input"""
        if detected_language == target_language:
            return user_message
        
        # Create a prompt that instructs the LLM to respond in the original language
        language_name = self.language_names.get(detected_language, detected_language)
        
        prompt = f"""
The user has written their message in {language_name}. Please understand their message and respond in the same language ({language_name}).

User message: {user_message}

Please provide a helpful response in {language_name}.
"""
        return prompt
    
    def create_language_aware_system_prompt(self, original_system_prompt, user_language):
        """Create a comprehensive system prompt that's aware of the user's language"""
        if user_language == 'en':
            return original_system_prompt

        language_name = self.language_names.get(user_language, user_language)

        # Get cultural context for better responses
        cultural_context = self._get_cultural_context(user_language)

        enhanced_prompt = f"""
{original_system_prompt}

CRITICAL LANGUAGE INSTRUCTIONS:
- The user is communicating in {language_name}
- You MUST respond in {language_name} unless specifically asked to use a different language
- Maintain natural fluency, proper grammar, and cultural appropriateness
- Use appropriate honorifics, formal/informal registers as culturally expected
- Provide detailed, comprehensive responses without summarization
- Include cultural context and references relevant to {language_name} speakers

{cultural_context}

RESPONSE REQUIREMENTS:
- Write complete, detailed responses in {language_name}
- Do not translate from English - think and respond directly in {language_name}
- Use native expressions, idioms, and cultural references when appropriate
- Maintain the same level of detail and comprehensiveness as requested
"""
        return enhanced_prompt

    def _get_cultural_context(self, language_code):
        """Get cultural context for specific languages"""
        cultural_contexts = {
            'hi': "Cultural Context: Use appropriate Hindi honorifics (जी, साहब, मैडम). Be respectful of Indian cultural values and traditions.",
            'ta': "Cultural Context: Use appropriate Tamil honorifics and respectful language. Consider Tamil cultural values and traditions.",
            'te': "Cultural Context: Use appropriate Telugu honorifics and respectful language. Consider Telugu cultural values and traditions.",
            'ml': "Cultural Context: Use appropriate Malayalam honorifics and respectful language. Consider Malayalam cultural values and traditions.",
            'kn': "Cultural Context: Use appropriate Kannada honorifics and respectful language. Consider Kannada cultural values and traditions.",
            'bn': "Cultural Context: Use appropriate Bengali honorifics and respectful language. Consider Bengali cultural values and traditions.",
            'gu': "Cultural Context: Use appropriate Gujarati honorifics and respectful language. Consider Gujarati cultural values and traditions.",
            'pa': "Cultural Context: Use appropriate Punjabi honorifics and respectful language. Consider Punjabi cultural values and traditions.",
            'mr': "Cultural Context: Use appropriate Marathi honorifics and respectful language. Consider Marathi cultural values and traditions.",
            'ur': "Cultural Context: Use appropriate Urdu honorifics (آپ، جناب، صاحب). Be respectful of cultural values and traditions.",
            'ar': "Cultural Context: Use appropriate Arabic honorifics and respectful language. Consider Arabic cultural values and traditions.",
            'fa': "Cultural Context: Use appropriate Persian honorifics and respectful language. Consider Persian cultural values and traditions."
        }

        return cultural_contexts.get(language_code, f"Cultural Context: Be respectful of the cultural values and traditions associated with {self.language_names.get(language_code, language_code)} speakers.")
    
    def detect_and_prepare_message(self, message, system_prompt=None):
        """Enhanced language detection and message preparation for comprehensive multilingual support"""
        try:
            # Enhanced language detection using multiple methods
            detected_code, detected_name = self.detect_language(message)

            # Get comprehensive language information
            language_info = self.get_language_info(message)

            # Prepare the system prompt to be language-aware with detailed instructions
            base_system_prompt = system_prompt or """You are a comprehensive AI assistant that provides detailed, thorough, and complete responses.

CRITICAL INSTRUCTIONS:
- Provide COMPLETE, DETAILED, and COMPREHENSIVE answers
- DO NOT summarize or condense information
- Include ALL relevant details, context, and background information
- Write in full paragraphs with thorough explanations
- Provide specific examples, data, and evidence when available
- Cover all aspects of the topic comprehensively"""

            enhanced_system_prompt = self.create_language_aware_system_prompt(
                base_system_prompt, detected_code
            )

            # Additional language-specific instructions
            language_specific_instructions = self._get_language_specific_instructions(detected_code)

            return {
                'original_message': message,
                'detected_language': detected_code,
                'detected_language_name': detected_name,
                'enhanced_system_prompt': enhanced_system_prompt,
                'language_specific_instructions': language_specific_instructions,
                'needs_translation': detected_code != 'en',
                'is_multilingual': True,
                'confidence_score': language_info.get('confidence', 0.8),
                'detection_method': language_info.get('method', 'enhanced'),
                'cultural_context': self._get_cultural_context(detected_code)
            }

        except Exception as e:
            print(f"Error in detect_and_prepare_message: {str(e)}")
            return {
                'original_message': message,
                'detected_language': 'en',
                'detected_language_name': 'English',
                'enhanced_system_prompt': system_prompt or "You are a helpful AI assistant.",
                'language_specific_instructions': '',
                'needs_translation': False,
                'is_multilingual': False,
                'confidence_score': 1.0,
                'detection_method': 'fallback',
                'cultural_context': ''
            }

    def _get_language_specific_instructions(self, language_code):
        """Get specific instructions for different languages"""
        instructions = {
            'hi': "हिंदी में उत्तर देते समय उचित सम्मानजनक भाषा का प्रयोग करें। विस्तृत और संपूर्ण जानकारी प्रदान करें।",
            'ta': "தமிழில் பதிலளிக்கும் போது மரியாதையான மொழியைப் பயன்படுத்தவும். விரிவான மற்றும் முழுமையான தகவல்களை வழங்கவும்.",
            'te': "తెలుగులో సమాధానం ఇచ్చేటప్పుడు గౌరవప్రదమైన భాషను ఉపయోగించండి. వివరణాత్మక మరియు సంపూర్ణ సమాచారం అందించండి।",
            'ml': "മലയാളത്തിൽ ഉത്തരം നൽകുമ്പോൾ മാന്യമായ ഭാഷ ഉപയോഗിക്കുക. വിശദവും സമ്പൂർണ്ണവുമായ വിവരങ്ങൾ നൽകുക.",
            'kn': "ಕನ್ನಡದಲ್ಲಿ ಉತ್ತರಿಸುವಾಗ ಗೌರವಾನ್ವಿತ ಭಾಷೆಯನ್ನು ಬಳಸಿ. ವಿವರವಾದ ಮತ್ತು ಸಂಪೂರ್ಣ ಮಾಹಿತಿಯನ್ನು ಒದಗಿಸಿ।",
            'bn': "বাংলায় উত্তর দেওয়ার সময় সম্মানজনক ভাষা ব্যবহার করুন। বিস্তারিত এবং সম্পূর্ণ তথ্য প্রদান করুন।",
            'gu': "ગુજરાતીમાં જવાબ આપતી વખતે આદરણીય ભાષાનો ઉપયોગ કરો. વિગતવાર અને સંપૂર્ણ માહિતી પ્રદાન કરો।",
            'pa': "ਪੰਜਾਬੀ ਵਿੱਚ ਜਵਾਬ ਦਿੰਦੇ ਸਮੇਂ ਸਤਿਕਾਰਯੋਗ ਭਾਸ਼ਾ ਦੀ ਵਰਤੋਂ ਕਰੋ। ਵਿਸਤ੍ਰਿਤ ਅਤੇ ਸੰਪੂਰਨ ਜਾਣਕਾਰੀ ਪ੍ਰਦਾਨ ਕਰੋ।",
            'mr': "मराठीत उत्तर देताना आदरणीय भाषेचा वापर करा. तपशीलवार आणि संपूर्ण माहिती प्रदान करा।",
            'ur': "اردو میں جواب دیتے وقت احترام کی زبان استعمال کریں۔ تفصیلی اور مکمل معلومات فراہم کریں۔"
        }

        return instructions.get(language_code, "Provide detailed and comprehensive responses in the detected language.")
    
    def get_language_info(self, text):
        """Get comprehensive language information for text with enhanced details"""
        detected_code, detected_name = self.detect_language(text)

        # Additional analysis
        script_info = self._analyze_script(text)
        complexity_score = self._calculate_text_complexity(text)

        return {
            'code': detected_code,
            'name': detected_name,
            'is_english': detected_code == 'en',
            'is_supported': detected_code in self.language_names,
            'is_indian_language': detected_code in ['hi', 'ta', 'te', 'ml', 'kn', 'bn', 'gu', 'pa', 'mr', 'ur'],
            'script_info': script_info,
            'complexity_score': complexity_score,
            'confidence': 0.8,  # Default confidence
            'method': 'enhanced_detection',
            'cultural_context_available': detected_code in self.language_patterns
        }

    def _analyze_script(self, text):
        """Analyze the script characteristics of the text"""
        scripts = {
            'latin': 0,
            'devanagari': 0,
            'tamil': 0,
            'telugu': 0,
            'malayalam': 0,
            'kannada': 0,
            'bengali': 0,
            'gujarati': 0,
            'gurmukhi': 0,
            'arabic': 0
        }

        for char in text:
            if char.isalpha():
                code_point = ord(char)
                if 0x0041 <= code_point <= 0x007A or 0x0041 <= code_point <= 0x005A:
                    scripts['latin'] += 1
                elif 0x0900 <= code_point <= 0x097F:
                    scripts['devanagari'] += 1
                elif 0x0B80 <= code_point <= 0x0BFF:
                    scripts['tamil'] += 1
                elif 0x0C00 <= code_point <= 0x0C7F:
                    scripts['telugu'] += 1
                elif 0x0D00 <= code_point <= 0x0D7F:
                    scripts['malayalam'] += 1
                elif 0x0C80 <= code_point <= 0x0CFF:
                    scripts['kannada'] += 1
                elif 0x0980 <= code_point <= 0x09FF:
                    scripts['bengali'] += 1
                elif 0x0A80 <= code_point <= 0x0AFF:
                    scripts['gujarati'] += 1
                elif 0x0A00 <= code_point <= 0x0A7F:
                    scripts['gurmukhi'] += 1
                elif 0x0600 <= code_point <= 0x06FF:
                    scripts['arabic'] += 1

        return scripts

    def _calculate_text_complexity(self, text):
        """Calculate text complexity score"""
        if not text:
            return 0

        # Simple complexity metrics
        word_count = len(text.split())
        char_count = len(text)
        unique_chars = len(set(text.lower()))

        # Normalize scores
        complexity = min(1.0, (word_count * 0.1 + unique_chars * 0.01 + char_count * 0.001) / 3)
        return complexity

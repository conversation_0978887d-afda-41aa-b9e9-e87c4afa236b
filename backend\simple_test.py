#!/usr/bin/env python3
"""
Simple test to get SocketIO working
"""

from flask import Flask
from flask_socketio import SocketIO

print("Creating Flask app...")
app = Flask(__name__)

print("Testing SocketIO configurations...")

# Test 1: Minimal configuration
try:
    socketio = SocketIO(app)
    print("✓ Minimal SocketIO works")
    
    # Test running the server
    print("Testing server startup...")
    # Don't actually run, just test if it would work
    print("✓ SocketIO setup successful")
    
except Exception as e:
    print(f"✗ SocketIO failed: {e}")
    import traceback
    traceback.print_exc()

print("Test completed!")

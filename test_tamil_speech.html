<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tamil Speech Recognition Test with Server Fallback</title>
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.browser { background: #e3f2fd; color: #1976d2; }
        .status.server { background: #fff3e0; color: #f57c00; }
        .status.error { background: #ffebee; color: #c62828; }
        .status.success { background: #e8f5e8; color: #2e7d32; }
        
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        .btn {
            padding: 15px 30px;
            margin: 10px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .btn.primary { background: #4CAF50; color: white; }
        .btn.primary:hover { background: #45a049; }
        .btn.secondary { background: #ff9800; color: white; }
        .btn.secondary:hover { background: #f57c00; }
        .btn.danger { background: #f44336; color: white; }
        .btn.danger:hover { background: #da190b; }
        .btn:disabled { opacity: 0.5; cursor: not-allowed; }
        
        .transcript {
            background: #f9f9f9;
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 5px;
            min-height: 100px;
            margin: 20px 0;
            white-space: pre-wrap;
        }
        .interim { color: #666; font-style: italic; }
        .final { color: #333; font-weight: bold; }
        
        .test-phrases {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .test-phrases h3 { margin-top: 0; color: #2e7d32; }
        .phrase {
            background: white;
            padding: 10px;
            margin: 5px 0;
            border-radius: 3px;
            border-left: 4px solid #4CAF50;
            cursor: pointer;
        }
        .phrase:hover { background: #f0f0f0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Tamil Speech Recognition Test</h1>
        <p>This test page demonstrates both browser-native and server-side streaming speech recognition for Tamil language.</p>
        
        <div id="status" class="status browser">Ready to test</div>
        
        <div class="controls">
            <button id="testBrowser" class="btn primary">Test Browser Recognition (Tamil)</button>
            <button id="testServer" class="btn secondary">Test Server Streaming (Tamil)</button>
            <button id="stopAll" class="btn danger" disabled>Stop All</button>
        </div>
        
        <div class="transcript">
            <div id="finalTranscript" class="final"></div>
            <div id="interimTranscript" class="interim"></div>
        </div>
        
        <div class="test-phrases">
            <h3>Tamil Test Phrases (Click to copy):</h3>
            <div class="phrase" onclick="copyToClipboard(this)">வணக்கம், என் பெயர் ராம்</div>
            <div class="phrase" onclick="copyToClipboard(this)">நீங்கள் எப்படி இருக்கிறீர்கள்?</div>
            <div class="phrase" onclick="copyToClipboard(this)">இன்று வானிலை எப்படி இருக்கிறது?</div>
            <div class="phrase" onclick="copyToClipboard(this)">நான் தமிழ் பேசுகிறேன்</div>
            <div class="phrase" onclick="copyToClipboard(this)">உங்களுக்கு தமிழ் தெரியுமா?</div>
        </div>
        
        <div id="logs" style="background: #f0f0f0; padding: 10px; border-radius: 5px; max-height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px;"></div>
    </div>

    <script>
        let recognition = null;
        let socket = null;
        let isRecording = false;
        let isServerStreaming = false;
        
        const statusEl = document.getElementById('status');
        const finalTranscriptEl = document.getElementById('finalTranscript');
        const interimTranscriptEl = document.getElementById('interimTranscript');
        const logsEl = document.getElementById('logs');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logsEl.innerHTML += `[${timestamp}] ${message}\n`;
            logsEl.scrollTop = logsEl.scrollHeight;
            console.log(message);
        }
        
        function updateStatus(message, type = 'browser') {
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }
        
        function updateTranscript(interim = '', final = '') {
            if (final) {
                finalTranscriptEl.textContent += final + ' ';
            }
            interimTranscriptEl.textContent = interim;
        }
        
        function copyToClipboard(element) {
            navigator.clipboard.writeText(element.textContent);
            updateStatus('Phrase copied to clipboard', 'success');
        }
        
        // Browser-based speech recognition
        function testBrowserRecognition() {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            
            if (!SpeechRecognition) {
                updateStatus('Browser speech recognition not supported', 'error');
                log('ERROR: Browser speech recognition not supported');
                return;
            }
            
            recognition = new SpeechRecognition();
            recognition.continuous = true;
            recognition.interimResults = true;
            recognition.lang = 'ta-IN';
            recognition.maxAlternatives = 3;
            
            recognition.onstart = () => {
                isRecording = true;
                updateStatus('Browser recognition started (Tamil)', 'browser');
                log('Browser recognition started for Tamil');
                document.getElementById('testBrowser').disabled = true;
                document.getElementById('stopAll').disabled = false;
            };
            
            recognition.onresult = (event) => {
                let interim = '';
                let final = '';
                
                for (let i = event.resultIndex; i < event.results.length; i++) {
                    const result = event.results[i];
                    const transcript = result[0].transcript;
                    
                    if (result.isFinal) {
                        final += transcript;
                        log(`Browser final: ${transcript}`);
                    } else {
                        interim += transcript;
                        log(`Browser interim: ${transcript}`);
                    }
                }
                
                updateTranscript(interim, final);
            };
            
            recognition.onerror = (event) => {
                log(`Browser error: ${event.error}`);
                if (event.error === 'language-not-supported') {
                    updateStatus('Tamil not supported by browser, try server streaming', 'error');
                } else {
                    updateStatus(`Browser error: ${event.error}`, 'error');
                }
                stopBrowserRecognition();
            };
            
            recognition.onend = () => {
                if (isRecording) {
                    try {
                        recognition.start();
                    } catch (e) {
                        log('Failed to restart browser recognition');
                        stopBrowserRecognition();
                    }
                }
            };
            
            try {
                recognition.start();
            } catch (error) {
                log(`Failed to start browser recognition: ${error}`);
                updateStatus('Failed to start browser recognition', 'error');
            }
        }
        
        function stopBrowserRecognition() {
            if (recognition && isRecording) {
                isRecording = false;
                recognition.stop();
                updateStatus('Browser recognition stopped', 'browser');
                log('Browser recognition stopped');
                document.getElementById('testBrowser').disabled = false;
                document.getElementById('stopAll').disabled = true;
            }
        }
        
        // Server-side streaming recognition
        function testServerStreaming() {
            if (isServerStreaming) {
                log('Server streaming already active');
                return;
            }
            
            updateStatus('Connecting to server...', 'server');
            log('Connecting to server streaming service...');
            
            socket = io('http://localhost:5000');
            
            socket.on('connect', () => {
                log('Connected to server');
                updateStatus('Connected, starting Tamil streaming...', 'server');
                socket.emit('start_streaming', { language: 'ta-IN' });
            });
            
            socket.on('session_started', (data) => {
                log(`Server session started: ${JSON.stringify(data)}`);
                updateStatus('Server streaming active (Tamil)', 'server');
                isServerStreaming = true;
                document.getElementById('testServer').disabled = true;
                document.getElementById('stopAll').disabled = false;
                startAudioCapture();
            });
            
            socket.on('interim_transcript', (data) => {
                log(`Server interim: ${data.text}`);
                updateTranscript(data.text, '');
            });
            
            socket.on('final_transcript', (data) => {
                log(`Server final: ${data.text}`);
                updateTranscript('', data.text);
            });
            
            socket.on('error', (data) => {
                log(`Server error: ${data.message}`);
                updateStatus(`Server error: ${data.message}`, 'error');
                stopServerStreaming();
            });
            
            socket.on('disconnect', () => {
                log('Disconnected from server');
                updateStatus('Disconnected from server', 'error');
                stopServerStreaming();
            });
        }
        
        async function startAudioCapture() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ 
                    audio: {
                        sampleRate: 16000,
                        channelCount: 1,
                        echoCancellation: true,
                        noiseSuppression: true
                    }
                });
                
                const mediaRecorder = new MediaRecorder(stream, {
                    mimeType: 'audio/webm;codecs=opus'
                });
                
                mediaRecorder.ondataavailable = (event) => {
                    if (event.data.size > 0 && socket) {
                        const reader = new FileReader();
                        reader.onload = () => {
                            socket.emit('audio_chunk', reader.result);
                        };
                        reader.readAsArrayBuffer(event.data);
                    }
                };
                
                mediaRecorder.start(250); // 250ms chunks
                log('Audio capture started');
                
                // Store for cleanup
                window.currentStream = stream;
                window.currentRecorder = mediaRecorder;
                
            } catch (error) {
                log(`Audio capture error: ${error}`);
                updateStatus('Failed to access microphone', 'error');
                stopServerStreaming();
            }
        }
        
        function stopServerStreaming() {
            if (window.currentRecorder) {
                window.currentRecorder.stop();
                window.currentRecorder = null;
            }
            
            if (window.currentStream) {
                window.currentStream.getTracks().forEach(track => track.stop());
                window.currentStream = null;
            }
            
            if (socket) {
                socket.emit('stop_streaming');
                socket.disconnect();
                socket = null;
            }
            
            isServerStreaming = false;
            updateStatus('Server streaming stopped', 'server');
            log('Server streaming stopped');
            document.getElementById('testServer').disabled = false;
            document.getElementById('stopAll').disabled = true;
        }
        
        function stopAll() {
            stopBrowserRecognition();
            stopServerStreaming();
            updateStatus('All recognition stopped', 'success');
        }
        
        // Event listeners
        document.getElementById('testBrowser').addEventListener('click', testBrowserRecognition);
        document.getElementById('testServer').addEventListener('click', testServerStreaming);
        document.getElementById('stopAll').addEventListener('click', stopAll);
        
        // Initialize
        log('Tamil Speech Recognition Test initialized');
        updateStatus('Ready to test - Choose browser or server recognition', 'success');
    </script>
</body>
</html>

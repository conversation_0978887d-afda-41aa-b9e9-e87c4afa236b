#!/usr/bin/env python3
"""
Robust error handling and retry utilities for Sozhaa Tech AI
"""

import time
import requests
import os
from typing import Callable, Any, Dict, Optional
from functools import wraps
import google.generativeai as genai

class APIErrorHandler:
    """Centralized error handling for API calls with retry logic"""
    
    MAX_RETRIES = 3
    BASE_DELAY = 2  # Base delay in seconds
    
    @staticmethod
    def is_quota_error(error_msg: str) -> bool:
        """Check if error is related to quota/rate limiting"""
        quota_indicators = [
            "429", "quota", "rate limit", "too many requests",
            "exceeded", "limit", "throttle"
        ]
        return any(indicator in error_msg.lower() for indicator in quota_indicators)
    
    @staticmethod
    def is_network_error(error_msg: str) -> bool:
        """Check if error is network-related"""
        network_indicators = [
            "connection", "timeout", "network", "dns", "unreachable",
            "502", "503", "504", "bad gateway", "service unavailable"
        ]
        return any(indicator in error_msg.lower() for indicator in network_indicators)
    
    @staticmethod
    def get_retry_delay(attempt: int, error_type: str = "default") -> float:
        """Calculate exponential backoff delay"""
        if error_type == "quota":
            # Longer delays for quota errors
            return APIErrorHandler.BASE_DELAY * (3 ** attempt)
        else:
            # Standard exponential backoff
            return APIErrorHandler.BASE_DELAY * (2 ** attempt)
    
    @classmethod
    def with_retry(cls, max_retries: int = None):
        """Decorator for automatic retry with exponential backoff"""
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            def wrapper(*args, **kwargs) -> Any:
                retries = max_retries or cls.MAX_RETRIES
                last_error = None
                
                for attempt in range(retries + 1):
                    try:
                        return func(*args, **kwargs)
                    except Exception as e:
                        last_error = e
                        error_msg = str(e)
                        
                        # Don't retry on final attempt
                        if attempt == retries:
                            break
                        
                        # Determine error type and delay
                        if cls.is_quota_error(error_msg):
                            delay = cls.get_retry_delay(attempt, "quota")
                            print(f"Quota error detected, retrying in {delay}s... (attempt {attempt + 1}/{retries + 1})")
                        elif cls.is_network_error(error_msg):
                            delay = cls.get_retry_delay(attempt, "network")
                            print(f"Network error detected, retrying in {delay}s... (attempt {attempt + 1}/{retries + 1})")
                        else:
                            # Don't retry for other types of errors
                            break
                        
                        time.sleep(delay)
                
                # If we get here, all retries failed
                raise last_error
            
            return wrapper
        return decorator

class GeminiErrorHandler:
    """Specialized error handler for Gemini API"""
    
    @staticmethod
    @APIErrorHandler.with_retry(max_retries=3)
    def safe_generate_content(model: genai.GenerativeModel, prompt: str) -> str:
        """Safely generate content with Gemini API"""
        try:
            response = model.generate_content(prompt)
            if not response or not response.text:
                raise Exception("Empty response from Gemini API")
            return response.text.strip()
        except Exception as e:
            error_msg = str(e)
            if "429" in error_msg or "quota" in error_msg.lower():
                raise Exception(f"Gemini quota exceeded: {error_msg}")
            elif "400" in error_msg:
                raise Exception(f"Gemini bad request: {error_msg}")
            elif "403" in error_msg:
                raise Exception(f"Gemini access denied: {error_msg}")
            else:
                raise Exception(f"Gemini API error: {error_msg}")
    
    @staticmethod
    @APIErrorHandler.with_retry(max_retries=3)
    def safe_embed_content(embeddings_model, texts: list) -> list:
        """Safely generate embeddings with retry logic"""
        try:
            if hasattr(embeddings_model, 'embed_documents'):
                return embeddings_model.embed_documents(texts)
            elif hasattr(embeddings_model, 'encode'):
                return embeddings_model.encode(texts).tolist()
            else:
                raise Exception("Unsupported embedding model")
        except Exception as e:
            error_msg = str(e)
            if "429" in error_msg or "quota" in error_msg.lower():
                raise Exception(f"Embedding quota exceeded: {error_msg}")
            else:
                raise Exception(f"Embedding error: {error_msg}")

class WebSearchErrorHandler:
    """Specialized error handler for web search APIs"""
    
    @staticmethod
    @APIErrorHandler.with_retry(max_retries=2)
    def safe_serper_search(api_key: str, query: str, num_results: int = 5) -> Dict:
        """Safely perform Serper API search"""
        url = "https://google.serper.dev/search"
        headers = {
            "X-API-KEY": api_key,
            "Content-Type": "application/json"
        }
        payload = {
            "q": query,
            "num": min(num_results, 10),
            "hl": "en",
            "gl": "us"
        }
        
        try:
            response = requests.post(url, headers=headers, json=payload, timeout=15)

            if response.status_code == 429:
                raise Exception("Serper API quota exceeded")
            elif response.status_code == 400:
                raise Exception(f"Serper API bad request: {response.text}")
            elif response.status_code == 403:
                raise Exception("Serper API access denied - check API key")

            response.raise_for_status()
            return response.json()

        except requests.exceptions.ConnectionError as e:
            raise Exception(f"Serper API connection failed - DNS or network issue: {str(e)}")
        except requests.exceptions.Timeout as e:
            raise Exception(f"Serper API timeout: {str(e)}")
        except requests.exceptions.RequestException as e:
            raise Exception(f"Serper API request failed: {str(e)}")
    
    @staticmethod
    def fallback_duckduckgo_search(query: str, num_results: int = 3) -> Dict:
        """Fallback search using DuckDuckGo"""
        try:
            # Simple DuckDuckGo instant answers API
            url = f"https://api.duckduckgo.com/"
            params = {
                "q": query,
                "format": "json",
                "no_html": "1",
                "skip_disambig": "1"
            }
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            
            # Extract relevant information
            results = []
            
            # Try to get abstract
            if data.get("Abstract"):
                results.append({
                    "title": data.get("AbstractText", "DuckDuckGo Result"),
                    "snippet": data.get("Abstract", ""),
                    "link": data.get("AbstractURL", ""),
                    "source": "DuckDuckGo"
                })
            
            # Try to get related topics
            for topic in data.get("RelatedTopics", [])[:num_results-len(results)]:
                if isinstance(topic, dict) and topic.get("Text"):
                    results.append({
                        "title": topic.get("Text", "")[:100] + "...",
                        "snippet": topic.get("Text", ""),
                        "link": topic.get("FirstURL", ""),
                        "source": "DuckDuckGo"
                    })
            
            return {
                "organic": results,
                "searchInformation": {
                    "totalResults": len(results)
                }
            }
            
        except Exception as e:
            print(f"DuckDuckGo fallback failed: {e}")
            return {
                "organic": [],
                "searchInformation": {"totalResults": 0}
            }

def create_fallback_response(error_type: str, query: str = "") -> Dict:
    """Create standardized fallback responses"""
    fallback_responses = {
        "gemini_quota": {
            "source": "fallback",
            "data": "⚠️ I'm currently experiencing high demand. Please try again in a few moments, or ask a different question.",
            "error_type": "quota_exceeded"
        },
        "gemini_error": {
            "source": "fallback", 
            "data": "⚠️ I'm having trouble processing your request right now. Please try rephrasing your question or try again later.",
            "error_type": "api_error"
        },
        "web_search_failed": {
            "source": "fallback",
            "data": "⚠️ I couldn't retrieve the latest information from the web. I can still help with general questions using my existing knowledge.",
            "error_type": "search_failed"
        },
        "network_error": {
            "source": "fallback",
            "data": "⚠️ Network connectivity issue detected. Please check your connection and try again.",
            "error_type": "network_error"
        },
        "general_error": {
            "source": "fallback",
            "data": "⚠️ Something went wrong. Please try again in a moment.",
            "error_type": "general_error"
        }
    }
    
    return fallback_responses.get(error_type, fallback_responses["general_error"])

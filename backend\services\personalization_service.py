from models.database import db, User, UserPreference
import uuid
import json

class PersonalizationService:
    def __init__(self):
        self.tone_styles = {
            'professional': {
                'description': 'Formal, business-appropriate language',
                'prompt_modifier': 'Respond in a professional, formal tone suitable for business communication. Use clear, precise language and maintain a respectful demeanor.'
            },
            'casual': {
                'description': 'Relaxed, conversational style',
                'prompt_modifier': 'Respond in a casual, conversational tone. Use everyday language and feel free to be more relaxed and informal in your communication style.'
            },
            'friendly': {
                'description': 'Warm, approachable communication',
                'prompt_modifier': 'Respond in a friendly, warm tone. Be approachable and personable while maintaining helpfulness and clarity.'
            },
            'technical': {
                'description': 'Detailed, precise technical language',
                'prompt_modifier': 'Respond with technical precision and detail. Use appropriate technical terminology and provide comprehensive explanations when relevant.'
            }
        }
        
        self.response_styles = {
            'concise': {
                'description': 'Brief, to-the-point responses',
                'prompt_modifier': 'Keep responses concise and to the point. Provide essential information without unnecessary elaboration.'
            },
            'detailed': {
                'description': 'Comprehensive, thorough explanations',
                'prompt_modifier': 'Provide detailed, comprehensive responses with thorough explanations and examples when helpful.'
            },
            'balanced': {
                'description': 'Moderate detail level',
                'prompt_modifier': 'Provide balanced responses with appropriate detail - not too brief, not overly verbose.'
            }
        }
    
    def get_user_preferences(self, user_id):
        """Get user preferences from database - handles missing columns gracefully"""
        try:
            user = User.query.get(user_id)
            if not user:
                return self._get_default_preferences()

            # Safely get preferences with fallback to defaults
            preferences = {}
            try:
                preferences['conversation_tone'] = getattr(user, 'conversation_tone', None) or 'friendly'
                preferences['response_style'] = getattr(user, 'response_style', None) or 'balanced'
                preferences['preferred_language'] = getattr(user, 'preferred_language', None) or 'en'
                preferences['custom_instructions'] = getattr(user, 'custom_instructions', None) or ''
            except Exception as attr_error:
                print(f"Warning: User preference columns not found, using defaults: {attr_error}")
                preferences = self._get_default_preferences()

            # Get additional preferences from UserPreference table if it exists
            try:
                user_prefs = UserPreference.query.filter_by(user_id=user_id).all()
                for pref in user_prefs:
                    try:
                        # Try to parse JSON values
                        preferences[pref.preference_key] = json.loads(pref.preference_value)
                    except json.JSONDecodeError:
                        # Store as string if not JSON
                        preferences[pref.preference_key] = pref.preference_value
            except Exception as pref_error:
                print(f"Warning: UserPreference table not accessible: {pref_error}")

            return preferences

        except Exception as e:
            print(f"Error getting user preferences: {str(e)}")
            return self._get_default_preferences()
    
    def _get_default_preferences(self):
        """Get default preferences for new users"""
        return {
            'conversation_tone': 'friendly',
            'response_style': 'balanced',
            'preferred_language': 'en',
            'custom_instructions': ''
        }
    
    def update_user_preferences(self, user_id, preferences):
        """Update user preferences in database - handles missing columns gracefully"""
        try:
            user = User.query.get(user_id)
            if not user:
                raise ValueError("User not found")

            # Update main user preferences if columns exist
            try:
                if 'conversation_tone' in preferences and hasattr(user, 'conversation_tone'):
                    user.conversation_tone = preferences['conversation_tone']
                if 'response_style' in preferences and hasattr(user, 'response_style'):
                    user.response_style = preferences['response_style']
                if 'preferred_language' in preferences and hasattr(user, 'preferred_language'):
                    user.preferred_language = preferences['preferred_language']
                if 'custom_instructions' in preferences and hasattr(user, 'custom_instructions'):
                    user.custom_instructions = preferences['custom_instructions']
            except Exception as attr_error:
                print(f"Warning: Could not update user preference columns: {attr_error}")
                # Store in UserPreference table instead
                for key in ['conversation_tone', 'response_style', 'preferred_language', 'custom_instructions']:
                    if key in preferences:
                        self._store_preference_in_table(user_id, key, preferences[key])
            
            # Update additional preferences in UserPreference table
            for key, value in preferences.items():
                if key not in ['conversation_tone', 'response_style', 'preferred_language', 'custom_instructions']:
                    # Check if preference exists
                    existing_pref = UserPreference.query.filter_by(
                        user_id=user_id, 
                        preference_key=key
                    ).first()
                    
                    if existing_pref:
                        # Update existing preference
                        existing_pref.preference_value = json.dumps(value) if isinstance(value, (dict, list)) else str(value)
                    else:
                        # Create new preference
                        new_pref = UserPreference(
                            id=str(uuid.uuid4()),
                            user_id=user_id,
                            preference_key=key,
                            preference_value=json.dumps(value) if isinstance(value, (dict, list)) else str(value)
                        )
                        db.session.add(new_pref)
            
            db.session.commit()
            return True
            
        except Exception as e:
            db.session.rollback()
            print(f"Error updating user preferences: {str(e)}")
            return False
    
    def create_personalized_system_prompt(self, user_id, base_prompt=None):
        """Create a personalized system prompt based on user preferences"""
        try:
            preferences = self.get_user_preferences(user_id)
            
            if not base_prompt:
                base_prompt = "You are a helpful AI assistant."
            
            # Get tone and style modifiers
            tone = preferences.get('conversation_tone', 'friendly')
            style = preferences.get('response_style', 'balanced')
            custom_instructions = preferences.get('custom_instructions', '')
            
            tone_modifier = self.tone_styles.get(tone, {}).get('prompt_modifier', '')
            style_modifier = self.response_styles.get(style, {}).get('prompt_modifier', '')
            
            # Build personalized prompt
            personalized_prompt = base_prompt
            
            if tone_modifier:
                personalized_prompt += f"\n\nTone: {tone_modifier}"
            
            if style_modifier:
                personalized_prompt += f"\n\nResponse Style: {style_modifier}"
            
            if custom_instructions:
                personalized_prompt += f"\n\nCustom Instructions: {custom_instructions}"
            
            return personalized_prompt
            
        except Exception as e:
            print(f"Error creating personalized prompt: {str(e)}")
            return base_prompt or "You are a helpful AI assistant."
    
    def get_available_tones(self):
        """Get available conversation tones"""
        return {
            tone: {
                'name': tone.title(),
                'description': info['description']
            }
            for tone, info in self.tone_styles.items()
        }
    
    def get_available_styles(self):
        """Get available response styles"""
        return {
            style: {
                'name': style.title(),
                'description': info['description']
            }
            for style, info in self.response_styles.items()
        }
    
    def validate_preferences(self, preferences):
        """Validate user preferences"""
        errors = []
        
        if 'conversation_tone' in preferences:
            if preferences['conversation_tone'] not in self.tone_styles:
                errors.append(f"Invalid conversation tone: {preferences['conversation_tone']}")
        
        if 'response_style' in preferences:
            if preferences['response_style'] not in self.response_styles:
                errors.append(f"Invalid response style: {preferences['response_style']}")
        
        if 'custom_instructions' in preferences:
            if len(preferences['custom_instructions']) > 1000:
                errors.append("Custom instructions too long (max 1000 characters)")
        
        return errors
    
    def get_user_profile(self, user_id):
        """Get complete user profile including preferences"""
        try:
            user = User.query.get(user_id)
            if not user:
                return None
            
            preferences = self.get_user_preferences(user_id)
            
            return {
                'user_info': {
                    'id': user.id,
                    'username': user.username,
                    'email': user.email,
                    'created_at': user.created_at.isoformat() if user.created_at else None
                },
                'preferences': preferences,
                'available_tones': self.get_available_tones(),
                'available_styles': self.get_available_styles()
            }
            
        except Exception as e:
            print(f"Error getting user profile: {str(e)}")
            return None
    
    def _store_preference_in_table(self, user_id, key, value):
        """Store preference in UserPreference table"""
        try:
            existing_pref = UserPreference.query.filter_by(
                user_id=user_id,
                preference_key=key
            ).first()

            if existing_pref:
                existing_pref.preference_value = json.dumps(value) if isinstance(value, (dict, list)) else str(value)
            else:
                new_pref = UserPreference(
                    id=str(uuid.uuid4()),
                    user_id=user_id,
                    preference_key=key,
                    preference_value=json.dumps(value) if isinstance(value, (dict, list)) else str(value)
                )
                db.session.add(new_pref)
        except Exception as e:
            print(f"Error storing preference in table: {e}")

    def reset_preferences(self, user_id):
        """Reset user preferences to defaults - handles missing columns gracefully"""
        try:
            user = User.query.get(user_id)
            if not user:
                raise ValueError("User not found")

            # Reset main preferences if columns exist
            try:
                if hasattr(user, 'conversation_tone'):
                    user.conversation_tone = 'friendly'
                if hasattr(user, 'response_style'):
                    user.response_style = 'balanced'
                if hasattr(user, 'preferred_language'):
                    user.preferred_language = 'en'
                if hasattr(user, 'custom_instructions'):
                    user.custom_instructions = ''
            except Exception as attr_error:
                print(f"Warning: Could not reset user preference columns: {attr_error}")

            # Delete additional preferences
            try:
                UserPreference.query.filter_by(user_id=user_id).delete()
            except Exception as pref_error:
                print(f"Warning: Could not delete UserPreference records: {pref_error}")

            db.session.commit()
            return True

        except Exception as e:
            db.session.rollback()
            print(f"Error resetting preferences: {str(e)}")
            return False

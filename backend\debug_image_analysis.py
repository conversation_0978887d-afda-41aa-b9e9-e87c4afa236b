#!/usr/bin/env python3
"""
Debug image analysis to see what's happening
"""

import sys
import os
sys.path.append('.')

def debug_image_analysis():
    """Debug the image analysis process"""
    print("🔍 Debugging Image Analysis...")
    
    try:
        from services.image_analyzer import ImageAnalyzer
        from PIL import Image
        import io
        
        print("✅ Services imported successfully")
        
        # Initialize image analyzer
        image_analyzer = ImageAnalyzer()
        print("✅ ImageAnalyzer initialized")
        
        # Create a simple test image
        test_image = Image.new('RGB', (800, 600), color=(255, 100, 50))
        
        # Convert to bytes (simulate file upload)
        img_bytes = io.BytesIO()
        test_image.save(img_bytes, format='PNG')
        img_bytes.seek(0)
        
        print("✅ Test image created")
        
        # Test the analysis
        print("🔄 Starting image analysis...")
        result = image_analyzer.analyze_image_comprehensive(
            image_data=img_bytes.getvalue(),
            user_query="Analyze this image and tell me what you see"
        )
        
        print("✅ Analysis completed!")
        print(f"📊 Result keys: {list(result.keys())}")
        
        if 'quota_exceeded' in result:
            print("🚫 Quota handling was triggered")
            print(f"📝 Error type: {result.get('error_type', 'Unknown')}")
        else:
            print("✅ Normal analysis completed")
            analysis = result.get('comprehensive_analysis', '')
            print(f"📝 Analysis preview: {analysis[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run debug analysis"""
    print("🔍 Image Analysis Debug")
    print("=" * 40)
    
    success = debug_image_analysis()
    
    print("\n" + "=" * 40)
    if success:
        print("✅ Debug completed - check output above")
    else:
        print("❌ Debug failed")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

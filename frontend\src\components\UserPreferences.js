import React, { useState, useEffect } from 'react';
import { Settings, Save, RotateCcw, X, User } from 'lucide-react';
import axios from 'axios';

const UserPreferences = ({ user, onClose, onPreferencesUpdate }) => {
  const [preferences, setPreferences] = useState({
    conversation_tone: 'friendly',
    response_style: 'balanced',
    preferred_language: 'en',
    custom_instructions: ''
  });
  const [availableTones, setAvailableTones] = useState({});
  const [availableStyles, setAvailableStyles] = useState({});
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    loadPreferences();
  }, [user]);

  const loadPreferences = async () => {
    try {
      setIsLoading(true);
      const response = await axios.get(`http://localhost:5000/api/user/preferences?user_id=${user.id}`);
      
      setPreferences(response.data.preferences);
      setAvailableTones(response.data.available_tones);
      setAvailableStyles(response.data.available_styles);
      setError(null);
    } catch (error) {
      console.error('Error loading preferences:', error);
      setError('Failed to load preferences');
    } finally {
      setIsLoading(false);
    }
  };

  const savePreferences = async () => {
    try {
      setIsSaving(true);
      setError(null);
      
      const response = await axios.post('http://localhost:5000/api/user/preferences', {
        user_id: user.id,
        preferences: preferences
      });

      if (response.data.success) {
        setSuccess(true);
        setTimeout(() => setSuccess(false), 3000);
        
        if (onPreferencesUpdate) {
          onPreferencesUpdate(response.data.preferences);
        }
      }
    } catch (error) {
      console.error('Error saving preferences:', error);
      setError(error.response?.data?.error || 'Failed to save preferences');
    } finally {
      setIsSaving(false);
    }
  };

  const resetPreferences = async () => {
    if (!window.confirm('Are you sure you want to reset all preferences to defaults?')) {
      return;
    }

    try {
      setIsSaving(true);
      setError(null);
      
      const response = await axios.post('http://localhost:5000/api/user/preferences/reset', {
        user_id: user.id
      });

      if (response.data.success) {
        setPreferences(response.data.preferences);
        setSuccess(true);
        setTimeout(() => setSuccess(false), 3000);
        
        if (onPreferencesUpdate) {
          onPreferencesUpdate(response.data.preferences);
        }
      }
    } catch (error) {
      console.error('Error resetting preferences:', error);
      setError(error.response?.data?.error || 'Failed to reset preferences');
    } finally {
      setIsSaving(false);
    }
  };

  const handleInputChange = (key, value) => {
    setPreferences(prev => ({
      ...prev,
      [key]: value
    }));
  };

  if (isLoading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 w-full max-w-md">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
            <span className="ml-3 text-gray-600">Loading preferences...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <Settings className="text-primary-600" size={24} />
            <h2 className="text-xl font-bold text-gray-800">User Preferences</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* User Info */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center gap-3 mb-2">
              <User className="text-gray-600" size={20} />
              <h3 className="font-medium text-gray-800">User Information</h3>
            </div>
            <div className="text-sm text-gray-600">
              <p><strong>Username:</strong> {user.username}</p>
              <p><strong>Email:</strong> {user.email}</p>
            </div>
          </div>

          {/* Conversation Tone */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Conversation Tone
            </label>
            <select
              value={preferences.conversation_tone}
              onChange={(e) => handleInputChange('conversation_tone', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              {Object.entries(availableTones).map(([key, tone]) => (
                <option key={key} value={key}>
                  {tone.name} - {tone.description}
                </option>
              ))}
            </select>
          </div>

          {/* Response Style */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Response Style
            </label>
            <select
              value={preferences.response_style}
              onChange={(e) => handleInputChange('response_style', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              {Object.entries(availableStyles).map(([key, style]) => (
                <option key={key} value={key}>
                  {style.name} - {style.description}
                </option>
              ))}
            </select>
          </div>

          {/* Preferred Language */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Preferred Language
            </label>
            <select
              value={preferences.preferred_language}
              onChange={(e) => handleInputChange('preferred_language', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="en">English</option>
              <option value="es">Spanish</option>
              <option value="fr">French</option>
              <option value="de">German</option>
              <option value="it">Italian</option>
              <option value="pt">Portuguese</option>
              <option value="ru">Russian</option>
              <option value="ja">Japanese</option>
              <option value="ko">Korean</option>
              <option value="zh">Chinese</option>
              <option value="ar">Arabic</option>
              <option value="hi">Hindi</option>
            </select>
          </div>

          {/* Custom Instructions */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Custom Instructions
            </label>
            <textarea
              value={preferences.custom_instructions}
              onChange={(e) => handleInputChange('custom_instructions', e.target.value)}
              placeholder="Add any specific instructions for how the AI should respond to you..."
              rows={4}
              maxLength={1000}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
            />
            <div className="text-xs text-gray-500 mt-1">
              {preferences.custom_instructions.length}/1000 characters
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <p className="text-red-800 text-sm">{error}</p>
            </div>
          )}

          {/* Success Message */}
          {success && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-3">
              <p className="text-green-800 text-sm">Preferences saved successfully!</p>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
          <button
            onClick={resetPreferences}
            disabled={isSaving}
            className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-200 rounded-lg transition-colors disabled:opacity-50"
          >
            <RotateCcw size={16} />
            Reset to Defaults
          </button>
          
          <div className="flex gap-3">
            <button
              onClick={onClose}
              disabled={isSaving}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-200 rounded-lg transition-colors disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              onClick={savePreferences}
              disabled={isSaving}
              className="flex items-center gap-2 px-4 py-2 bg-primary-500 hover:bg-primary-600 text-white rounded-lg transition-colors disabled:opacity-50"
            >
              {isSaving ? (
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
              ) : (
                <Save size={16} />
              )}
              {isSaving ? 'Saving...' : 'Save Preferences'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserPreferences;

"""
Comprehensive File Analysis Service using Gemini API
Provides deep analysis of uploaded files including tone, structure, and content insights
"""

import os
import sys
import io
import json
import csv
import mimetypes
from typing import Dict, List, Optional, Any, Union
from pathlib import Path

# Document processing imports
try:
    import fitz  # PyMuPDF for PDF processing
    PYMUPDF_AVAILABLE = True
except ImportError:
    print("⚠️  PyMuPDF not available, using pdfplumber for PDF processing")
    PYMUPDF_AVAILABLE = False

from docx import Document  # python-docx for Word documents
import openpyxl  # For Excel files
import pandas as pd  # For CSV and data analysis
from bs4 import BeautifulSoup  # For HTML processing
import pdfplumber  # Alternative PDF processing

# AI and language processing
import google.generativeai as genai
from dotenv import load_dotenv

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.gemini_config import GeminiConfig
from services.language_service import LanguageService

# Load environment variables
load_dotenv()

class FileAnalyzer:
    """
    Advanced File Analysis Service using Gemini API
    Provides comprehensive analysis of various file types without summarization
    """
    
    def __init__(self):
        """Initialize the File Analyzer with Gemini API"""
        # Initialize Gemini configuration
        if not GeminiConfig.is_initialized():
            GeminiConfig.initialize()
        
        # Get models for analysis
        try:
            self.analysis_model = GeminiConfig.create_analysis_model('chat')
            self.detailed_model = GeminiConfig.create_detailed_model('chat')
            print("✅ File Analyzer initialized with Gemini API")
        except Exception as e:
            print(f"⚠️  Failed to initialize Gemini models: {e}")
            self.analysis_model = None
            self.detailed_model = None
        
        # Initialize language service for multilingual support
        self.language_service = LanguageService()
        
        # Supported file types and their processors
        self.supported_types = {
            # Text documents
            '.txt': self._extract_text_file,
            '.md': self._extract_text_file,
            '.rtf': self._extract_text_file,
            
            # PDF documents
            '.pdf': self._extract_pdf_content,
            
            # Microsoft Office documents
            '.docx': self._extract_docx_content,
            '.doc': self._extract_doc_content,
            '.xlsx': self._extract_xlsx_content,
            '.xls': self._extract_xls_content,
            '.pptx': self._extract_pptx_content,
            
            # Data files
            '.csv': self._extract_csv_content,
            '.json': self._extract_json_content,
            '.xml': self._extract_xml_content,
            
            # Web files
            '.html': self._extract_html_content,
            '.htm': self._extract_html_content,
            
            # Code files
            '.py': self._extract_code_file,
            '.js': self._extract_code_file,
            '.java': self._extract_code_file,
            '.cpp': self._extract_code_file,
            '.c': self._extract_code_file,
            '.php': self._extract_code_file,
            '.rb': self._extract_code_file,
            '.go': self._extract_code_file,
        }
        
        # File size limits (in bytes)
        self.max_file_size = 50 * 1024 * 1024  # 50MB
        self.max_text_length = 1000000  # 1M characters for analysis
    
    def analyze_file_comprehensive(self, file_path: str, 
                                 user_query: str = "", 
                                 language_context: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Perform comprehensive file analysis with detailed results
        
        Args:
            file_path: Path to the file to analyze
            user_query: Specific user question about the file
            language_context: Language context from language service
            
        Returns:
            Comprehensive analysis results
        """
        try:
            if not self.analysis_model:
                return {
                    "success": False,
                    "error": "Gemini API not available",
                    "analysis": {}
                }
            
            # Validate file
            file_info = self._validate_file(file_path)
            if not file_info["valid"]:
                return {
                    "success": False,
                    "error": file_info["error"],
                    "analysis": {}
                }
            
            # Extract content from file
            content_result = self._extract_file_content(file_path, file_info)
            if not content_result["success"]:
                return {
                    "success": False,
                    "error": content_result["error"],
                    "analysis": {}
                }
            
            # Detect language from content if not provided
            extracted_text = content_result["content"]["text"]
            if extracted_text and not language_context:
                language_context = self.language_service.detect_and_prepare_message(
                    extracted_text[:1000]  # Use first 1000 chars for detection
                )
            
            # Perform comprehensive analysis
            analysis_results = self._perform_detailed_file_analysis(
                content_result["content"], file_info, user_query, language_context
            )
            
            return {
                "success": True,
                "file_info": file_info,
                "content_extraction": content_result,
                "analysis": analysis_results,
                "metadata": {
                    "file_processed": True,
                    "language_detected": language_context.get('detected_language', 'en') if language_context else 'en',
                    "comprehensive_analysis": True,
                    "summarization_applied": False
                }
            }
            
        except Exception as e:
            print(f"Error in comprehensive file analysis: {e}")
            return {
                "success": False,
                "error": f"Analysis failed: {str(e)}",
                "analysis": {}
            }
    
    def _validate_file(self, file_path: str) -> Dict[str, Any]:
        """
        Validate file for processing
        
        Args:
            file_path: Path to the file
            
        Returns:
            Validation results
        """
        try:
            if not os.path.exists(file_path):
                return {"valid": False, "error": "File does not exist"}
            
            file_size = os.path.getsize(file_path)
            if file_size > self.max_file_size:
                return {"valid": False, "error": f"File too large: {file_size} bytes (max: {self.max_file_size})"}
            
            if file_size == 0:
                return {"valid": False, "error": "File is empty"}
            
            # Get file extension and MIME type
            file_ext = Path(file_path).suffix.lower()
            mime_type, _ = mimetypes.guess_type(file_path)
            
            if file_ext not in self.supported_types:
                return {"valid": False, "error": f"Unsupported file type: {file_ext}"}
            
            return {
                "valid": True,
                "file_path": file_path,
                "file_name": os.path.basename(file_path),
                "file_extension": file_ext,
                "file_size": file_size,
                "mime_type": mime_type,
                "processor": self.supported_types[file_ext]
            }
            
        except Exception as e:
            return {"valid": False, "error": f"Validation failed: {str(e)}"}
    
    def _extract_file_content(self, file_path: str, file_info: Dict) -> Dict[str, Any]:
        """
        Extract content from file based on its type
        
        Args:
            file_path: Path to the file
            file_info: File information from validation
            
        Returns:
            Extracted content
        """
        try:
            processor = file_info["processor"]
            content = processor(file_path)
            
            return {
                "success": True,
                "content": content,
                "extraction_method": processor.__name__
            }
            
        except Exception as e:
            print(f"Error extracting content from {file_path}: {e}")
            return {
                "success": False,
                "error": f"Content extraction failed: {str(e)}",
                "content": {}
            }
    
    def _extract_text_file(self, file_path: str) -> Dict[str, Any]:
        """Extract content from plain text files"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
                text = file.read()
            
            return {
                "text": text,
                "type": "plain_text",
                "word_count": len(text.split()),
                "character_count": len(text),
                "line_count": len(text.split('\n'))
            }
            
        except Exception as e:
            print(f"Error reading text file: {e}")
            return {"text": "", "type": "plain_text", "error": str(e)}
    
    def _extract_pdf_content(self, file_path: str) -> Dict[str, Any]:
        """Extract content from PDF files using multiple methods"""
        try:
            content = {
                "text": "",
                "type": "pdf",
                "pages": 0,
                "metadata": {},
                "extraction_methods": []
            }
            
            # Method 1: PyMuPDF (fitz) - if available
            if PYMUPDF_AVAILABLE:
                try:
                    doc = fitz.open(file_path)
                    text_parts = []

                    for page_num in range(len(doc)):
                        page = doc.load_page(page_num)
                        text_parts.append(page.get_text())

                    content["text"] = "\n".join(text_parts)
                    content["pages"] = len(doc)
                    content["metadata"] = doc.metadata
                    content["extraction_methods"].append("PyMuPDF")
                    doc.close()

                except Exception as e:
                    print(f"PyMuPDF extraction failed: {e}")
            else:
                print("PyMuPDF not available, skipping to pdfplumber")
            
            # Method 2: pdfplumber (if PyMuPDF failed or as backup)
            if not content["text"]:
                try:
                    import pdfplumber
                    with pdfplumber.open(file_path) as pdf:
                        text_parts = []
                        for page in pdf.pages:
                            text_parts.append(page.extract_text() or "")
                        
                        content["text"] = "\n".join(text_parts)
                        content["pages"] = len(pdf.pages)
                        content["extraction_methods"].append("pdfplumber")
                        
                except Exception as e:
                    print(f"pdfplumber extraction failed: {e}")
            
            # Add text statistics
            if content["text"]:
                content["word_count"] = len(content["text"].split())
                content["character_count"] = len(content["text"])
            
            return content
            
        except Exception as e:
            print(f"Error extracting PDF content: {e}")
            return {"text": "", "type": "pdf", "error": str(e)}
    
    def _extract_docx_content(self, file_path: str) -> Dict[str, Any]:
        """Extract content from DOCX files"""
        try:
            doc = Document(file_path)
            
            # Extract text from paragraphs
            paragraphs = [para.text for para in doc.paragraphs if para.text.strip()]
            text = "\n".join(paragraphs)
            
            # Extract text from tables
            table_text = []
            for table in doc.tables:
                for row in table.rows:
                    row_text = [cell.text.strip() for cell in row.cells]
                    table_text.append("\t".join(row_text))
            
            if table_text:
                text += "\n\nTables:\n" + "\n".join(table_text)
            
            return {
                "text": text,
                "type": "docx",
                "paragraphs": len(paragraphs),
                "tables": len(doc.tables),
                "word_count": len(text.split()),
                "character_count": len(text)
            }
            
        except Exception as e:
            print(f"Error extracting DOCX content: {e}")
            return {"text": "", "type": "docx", "error": str(e)}
    
    def _extract_doc_content(self, file_path: str) -> Dict[str, Any]:
        """Extract content from DOC files (legacy format)"""
        # For DOC files, we'll try to use python-docx or fall back to text extraction
        try:
            # Try treating as DOCX first
            return self._extract_docx_content(file_path)
        except:
            # Fall back to basic text extraction
            return {"text": "DOC format requires additional processing", "type": "doc", "error": "Legacy DOC format not fully supported"}
    
    def _extract_xlsx_content(self, file_path: str) -> Dict[str, Any]:
        """Extract content from Excel files"""
        try:
            workbook = openpyxl.load_workbook(file_path, data_only=True)
            
            content = {
                "text": "",
                "type": "xlsx",
                "sheets": [],
                "total_rows": 0,
                "total_columns": 0
            }
            
            all_text = []
            
            for sheet_name in workbook.sheetnames:
                sheet = workbook[sheet_name]
                sheet_data = []
                
                for row in sheet.iter_rows(values_only=True):
                    row_data = [str(cell) if cell is not None else "" for cell in row]
                    if any(cell.strip() for cell in row_data):  # Skip empty rows
                        sheet_data.append("\t".join(row_data))
                
                if sheet_data:
                    sheet_text = f"Sheet: {sheet_name}\n" + "\n".join(sheet_data)
                    all_text.append(sheet_text)
                    
                    content["sheets"].append({
                        "name": sheet_name,
                        "rows": len(sheet_data),
                        "columns": sheet.max_column
                    })
                    
                    content["total_rows"] += len(sheet_data)
                    content["total_columns"] = max(content["total_columns"], sheet.max_column)
            
            content["text"] = "\n\n".join(all_text)
            content["word_count"] = len(content["text"].split())
            content["character_count"] = len(content["text"])
            
            return content
            
        except Exception as e:
            print(f"Error extracting XLSX content: {e}")
            return {"text": "", "type": "xlsx", "error": str(e)}
    
    def _extract_xls_content(self, file_path: str) -> Dict[str, Any]:
        """Extract content from XLS files (legacy Excel format)"""
        try:
            # Use pandas to read XLS files
            excel_data = pd.read_excel(file_path, sheet_name=None)
            
            content = {
                "text": "",
                "type": "xls",
                "sheets": [],
                "total_rows": 0,
                "total_columns": 0
            }
            
            all_text = []
            
            for sheet_name, df in excel_data.items():
                if not df.empty:
                    sheet_text = f"Sheet: {sheet_name}\n" + df.to_string(index=False)
                    all_text.append(sheet_text)
                    
                    content["sheets"].append({
                        "name": sheet_name,
                        "rows": len(df),
                        "columns": len(df.columns)
                    })
                    
                    content["total_rows"] += len(df)
                    content["total_columns"] = max(content["total_columns"], len(df.columns))
            
            content["text"] = "\n\n".join(all_text)
            content["word_count"] = len(content["text"].split())
            content["character_count"] = len(content["text"])
            
            return content
            
        except Exception as e:
            print(f"Error extracting XLS content: {e}")
            return {"text": "", "type": "xls", "error": str(e)}

    def _extract_pptx_content(self, file_path: str) -> Dict[str, Any]:
        """Extract content from PowerPoint files"""
        try:
            from pptx import Presentation

            prs = Presentation(file_path)

            content = {
                "text": "",
                "type": "pptx",
                "slides": 0,
                "slide_contents": []
            }

            all_text = []

            for i, slide in enumerate(prs.slides):
                slide_text = []

                # Extract text from shapes
                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text.strip():
                        slide_text.append(shape.text.strip())

                if slide_text:
                    slide_content = f"Slide {i+1}:\n" + "\n".join(slide_text)
                    all_text.append(slide_content)
                    content["slide_contents"].append({
                        "slide_number": i+1,
                        "text_elements": len(slide_text),
                        "content": "\n".join(slide_text)
                    })

            content["text"] = "\n\n".join(all_text)
            content["slides"] = len(prs.slides)
            content["word_count"] = len(content["text"].split())
            content["character_count"] = len(content["text"])

            return content

        except Exception as e:
            print(f"Error extracting PPTX content: {e}")
            return {"text": "", "type": "pptx", "error": str(e)}

    def _extract_csv_content(self, file_path: str) -> Dict[str, Any]:
        """Extract content from CSV files"""
        try:
            # Try different encodings
            encodings = ['utf-8', 'latin-1', 'cp1252']
            df = None

            for encoding in encodings:
                try:
                    df = pd.read_csv(file_path, encoding=encoding)
                    break
                except UnicodeDecodeError:
                    continue

            if df is None:
                return {"text": "", "type": "csv", "error": "Could not decode CSV file"}

            content = {
                "text": df.to_string(index=False),
                "type": "csv",
                "rows": len(df),
                "columns": len(df.columns),
                "column_names": df.columns.tolist(),
                "data_types": df.dtypes.to_dict(),
                "sample_data": df.head().to_dict('records') if not df.empty else []
            }

            content["word_count"] = len(content["text"].split())
            content["character_count"] = len(content["text"])

            return content

        except Exception as e:
            print(f"Error extracting CSV content: {e}")
            return {"text": "", "type": "csv", "error": str(e)}

    def _extract_json_content(self, file_path: str) -> Dict[str, Any]:
        """Extract content from JSON files"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                json_data = json.load(file)

            # Convert JSON to readable text
            formatted_json = json.dumps(json_data, indent=2, ensure_ascii=False)

            content = {
                "text": formatted_json,
                "type": "json",
                "structure": type(json_data).__name__,
                "raw_data": json_data,
                "keys": list(json_data.keys()) if isinstance(json_data, dict) else None,
                "length": len(json_data) if isinstance(json_data, (list, dict)) else None
            }

            content["word_count"] = len(content["text"].split())
            content["character_count"] = len(content["text"])

            return content

        except Exception as e:
            print(f"Error extracting JSON content: {e}")
            return {"text": "", "type": "json", "error": str(e)}

    def _extract_xml_content(self, file_path: str) -> Dict[str, Any]:
        """Extract content from XML files"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                xml_content = file.read()

            # Parse with BeautifulSoup for better text extraction
            soup = BeautifulSoup(xml_content, 'xml')
            text_content = soup.get_text(separator='\n', strip=True)

            content = {
                "text": text_content,
                "type": "xml",
                "raw_xml": xml_content,
                "tags": [tag.name for tag in soup.find_all()] if soup else [],
                "structure_depth": len(xml_content.split('<')) // 2
            }

            content["word_count"] = len(content["text"].split())
            content["character_count"] = len(content["text"])

            return content

        except Exception as e:
            print(f"Error extracting XML content: {e}")
            return {"text": "", "type": "xml", "error": str(e)}

    def _extract_html_content(self, file_path: str) -> Dict[str, Any]:
        """Extract content from HTML files"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                html_content = file.read()

            # Parse with BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')

            # Extract text content
            text_content = soup.get_text(separator='\n', strip=True)

            # Extract metadata
            title = soup.find('title')
            meta_tags = soup.find_all('meta')

            content = {
                "text": text_content,
                "type": "html",
                "title": title.get_text() if title else "No title",
                "meta_tags": [{"name": tag.get('name'), "content": tag.get('content')}
                             for tag in meta_tags if tag.get('name')],
                "links": [{"text": a.get_text(), "href": a.get('href')}
                         for a in soup.find_all('a', href=True)],
                "images": [{"alt": img.get('alt'), "src": img.get('src')}
                          for img in soup.find_all('img')],
                "raw_html": html_content
            }

            content["word_count"] = len(content["text"].split())
            content["character_count"] = len(content["text"])

            return content

        except Exception as e:
            print(f"Error extracting HTML content: {e}")
            return {"text": "", "type": "html", "error": str(e)}

    def _extract_code_file(self, file_path: str) -> Dict[str, Any]:
        """Extract content from code files"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
                code_content = file.read()

            # Basic code analysis
            lines = code_content.split('\n')
            non_empty_lines = [line for line in lines if line.strip()]
            comment_lines = [line for line in lines if line.strip().startswith(('#', '//', '/*', '*', '--'))]

            file_ext = Path(file_path).suffix.lower()

            content = {
                "text": code_content,
                "type": "code",
                "language": file_ext[1:] if file_ext else "unknown",
                "total_lines": len(lines),
                "code_lines": len(non_empty_lines),
                "comment_lines": len(comment_lines),
                "blank_lines": len(lines) - len(non_empty_lines)
            }

            content["word_count"] = len(content["text"].split())
            content["character_count"] = len(content["text"])

            return content

        except Exception as e:
            print(f"Error extracting code file content: {e}")
            return {"text": "", "type": "code", "error": str(e)}

    def _perform_detailed_file_analysis(self, content: Dict, file_info: Dict,
                                      user_query: str,
                                      language_context: Optional[Dict]) -> Dict[str, Any]:
        """
        Perform detailed file analysis using Gemini API

        Args:
            content: Extracted file content
            file_info: File information
            user_query: User's specific question
            language_context: Language context for response

        Returns:
            Comprehensive analysis results
        """
        try:
            if not self.analysis_model:
                return {"error": "Gemini API not available for analysis"}

            # Create comprehensive analysis prompt
            analysis_prompt = self._create_comprehensive_file_prompt(
                content, file_info, user_query, language_context
            )

            # Truncate content if too long for analysis
            text_content = content.get("text", "")
            if len(text_content) > self.max_text_length:
                text_content = text_content[:self.max_text_length] + "\n\n[Content truncated for analysis...]"

            # Combine prompt with content
            full_prompt = f"{analysis_prompt}\n\nFILE CONTENT TO ANALYZE:\n{text_content}"

            # Perform analysis with Gemini
            try:
                response = self.analysis_model.generate_content(full_prompt)

                # Check if response is valid
                if not response or not response.text:
                    raise Exception("Empty response from Gemini API")

            except Exception as e:
                error_str = str(e)
                # Handle quota exceeded error with user-friendly message (any 429 error)
                if "429" in error_str or "ResourceExhausted" in str(type(e)):
                    return {
                        "comprehensive_analysis": f"""🚫 **Gemini API Quota Exceeded**

I can see your file "{file_info.get('filename', 'uploaded file')}", but I've reached the daily limit of 50 requests for the Gemini API free tier.

**What I can tell you about your file:**
- ✅ File uploaded successfully: {file_info.get('filename', 'Unknown')}
- ✅ File size: {file_info.get('size', 'Unknown')} bytes
- ✅ File type: {file_info.get('extension', 'Unknown')}
- ✅ Content extracted and ready for analysis

**Basic File Information:**
- **File Format**: {file_info.get('extension', 'Unknown').upper()}
- **Content Length**: {len(content) if content else 0} characters
- **Processing Status**: Ready for analysis when quota resets

**Solutions:**
1. **Wait for quota reset** (resets daily at midnight UTC)
2. **Upgrade to paid Gemini API** for unlimited requests
3. **Try again tomorrow** for free tier reset

**Your file is ready for comprehensive analysis once the quota limit resets!** 🔄""",
                        "structural_analysis": {
                            "status": "quota_exceeded",
                            "message": "Gemini API daily quota of 50 requests exceeded",
                            "retry_suggestion": "Try again after quota reset or upgrade to paid tier",
                            "file_info": file_info
                        },
                        "content_statistics": self._calculate_content_statistics(content) if content else {},
                        "file_metadata": file_info,
                        "quota_exceeded": True,
                        "error_type": "quota_limit"
                    }
                else:
                    # Re-raise other errors
                    raise e

            # Parse and structure the response
            analysis_text = response.text.strip()

            # Additional structural analysis
            structural_analysis = self._perform_structural_analysis(content, file_info)

            return {
                "comprehensive_analysis": analysis_text,
                "structural_analysis": structural_analysis,
                "content_statistics": self._calculate_content_statistics(content),
                "file_metadata": file_info,
                "user_query_response": user_query,
                "language_used": language_context.get('detected_language', 'en') if language_context else 'en',
                "analysis_timestamp": self._get_timestamp(),
                "detailed_sections": self._parse_analysis_sections(analysis_text),
                "summarization_applied": False,
                "content_truncated": len(content.get("text", "")) > self.max_text_length
            }

        except Exception as e:
            print(f"Error in detailed file analysis: {e}")
            import traceback
            traceback.print_exc()

            # Return error in a format that the main app can handle
            raise Exception(f"File analysis failed: {str(e)}")

    def _create_comprehensive_file_prompt(self, content: Dict, file_info: Dict,
                                        user_query: str,
                                        language_context: Optional[Dict]) -> str:
        """
        Create a comprehensive analysis prompt for file analysis

        Args:
            content: Extracted file content
            file_info: File information
            user_query: User's specific question
            language_context: Language context for response

        Returns:
            Detailed analysis prompt
        """
        file_type = content.get("type", "unknown")
        file_name = file_info.get("file_name", "unknown")

        # Base comprehensive prompt
        base_prompt = f"""
COMPREHENSIVE FILE ANALYSIS - NO SUMMARIZATION

Analyze the following {file_type.upper()} file: {file_name}

Provide a COMPLETE, DETAILED, and THOROUGH analysis. DO NOT summarize or condense any information. Include ALL observable details and provide comprehensive insights.

ANALYSIS SECTIONS (Provide detailed information for each):

1. DOCUMENT OVERVIEW AND STRUCTURE (Complete Details):
   - File type and format analysis
   - Overall document structure and organization
   - Length, size, and complexity assessment
   - Document purpose and intended audience
   - Professional or personal nature of content

2. CONTENT ANALYSIS (Comprehensive):
   - Main topics and themes covered
   - Key information and important details
   - Factual content and data presented
   - Arguments, conclusions, or findings
   - Supporting evidence and examples
   - All significant points without omission

3. TONE AND STYLE ANALYSIS (Detailed):
   - Writing style and voice
   - Formal vs. informal tone
   - Emotional undertones and mood
   - Target audience indicators
   - Professional level and expertise shown
   - Cultural or regional language patterns

4. STRUCTURAL AND FORMATTING ANALYSIS (Complete):
   - Document organization and hierarchy
   - Headings, sections, and subsections
   - Lists, tables, and data presentation
   - Visual elements and formatting
   - Navigation and accessibility features
   - Technical formatting quality

5. LANGUAGE AND LINGUISTIC ANALYSIS (Comprehensive):
   - Primary language(s) used
   - Vocabulary level and complexity
   - Technical terminology and jargon
   - Grammar and syntax patterns
   - Regional or dialectal variations
   - Multilingual content if present

6. CONTEXTUAL AND DOMAIN ANALYSIS (Detailed):
   - Subject matter domain or field
   - Industry or academic context
   - Historical or temporal context
   - Geographic or cultural context
   - Specialized knowledge requirements
   - Professional or educational level

7. QUALITY AND ACCURACY ASSESSMENT (Complete):
   - Information accuracy and reliability
   - Completeness of coverage
   - Logical consistency and coherence
   - Potential biases or limitations
   - Source credibility indicators
   - Overall quality rating

8. PRACTICAL IMPLICATIONS AND USAGE (Comprehensive):
   - Intended use cases and applications
   - Actionable information provided
   - Decision-making support value
   - Educational or informational value
   - Commercial or business relevance
   - Long-term utility and relevance

9. USER QUERY RESPONSE (Specific and Complete):
   - Direct answer to user's specific question
   - Additional relevant information
   - Context and background details
   - Comprehensive explanation of findings
   - Supporting evidence from the document
"""

        # Add file-type specific analysis
        if file_type == "pdf":
            base_prompt += """

10. PDF-SPECIFIC ANALYSIS:
    - Document metadata and properties
    - Page structure and layout quality
    - Text extraction quality and completeness
    - Embedded elements (images, forms, etc.)
    - Security settings and restrictions
    - Accessibility features and compliance
"""
        elif file_type in ["docx", "doc"]:
            base_prompt += """

10. DOCUMENT-SPECIFIC ANALYSIS:
    - Document template and styling
    - Revision history and collaboration indicators
    - Embedded objects and media
    - Table and list formatting
    - Header/footer content
    - Document properties and metadata
"""
        elif file_type in ["xlsx", "xls", "csv"]:
            base_prompt += """

10. DATA-SPECIFIC ANALYSIS:
    - Data structure and organization
    - Column types and data quality
    - Statistical patterns and trends
    - Missing or incomplete data
    - Data relationships and correlations
    - Potential data analysis opportunities
"""
        elif file_type == "code":
            base_prompt += """

10. CODE-SPECIFIC ANALYSIS:
    - Programming language and version
    - Code structure and organization
    - Coding style and conventions
    - Complexity and maintainability
    - Documentation and comments quality
    - Potential improvements and best practices
"""

        # Add language-specific instructions if needed
        if language_context and language_context.get('detected_language') != 'en':
            language_name = language_context.get('detected_language_name', 'the detected language')
            language_prompt = f"""

LANGUAGE INSTRUCTIONS:
- Respond in {language_name}
- Use natural, fluent language appropriate for {language_name} speakers
- Maintain cultural context relevant to {language_name} speakers
- Provide the same level of detail and comprehensiveness as requested
"""
            base_prompt += language_prompt

        # Add user query if provided
        if user_query:
            base_prompt += f"""

USER'S SPECIFIC QUESTION: {user_query}

Ensure you provide a comprehensive answer to this specific question while maintaining all the detailed analysis sections above.
"""

        base_prompt += """

CRITICAL REQUIREMENTS:
- Provide COMPLETE and DETAILED responses for each section
- DO NOT summarize, condense, or truncate any information
- Include ALL observable details and patterns
- Write in full paragraphs with thorough explanations
- Provide specific examples and evidence from the file content
- Cover all aspects comprehensively without omitting details
- Maintain objectivity while providing insightful analysis
"""

        return base_prompt

    def _perform_structural_analysis(self, content: Dict, file_info: Dict) -> Dict[str, Any]:
        """
        Perform structural analysis of the file content

        Args:
            content: Extracted file content
            file_info: File information

        Returns:
            Structural analysis results
        """
        try:
            text = content.get("text", "")
            file_type = content.get("type", "unknown")

            analysis = {
                "file_type": file_type,
                "content_length": len(text),
                "word_count": content.get("word_count", 0),
                "character_count": content.get("character_count", 0),
                "line_count": content.get("line_count", len(text.split('\n'))),
                "paragraph_count": len([p for p in text.split('\n\n') if p.strip()]),
                "average_words_per_line": 0,
                "readability_indicators": {}
            }

            # Calculate averages
            if analysis["line_count"] > 0:
                analysis["average_words_per_line"] = analysis["word_count"] / analysis["line_count"]

            # Type-specific analysis
            if file_type == "pdf":
                analysis.update({
                    "pages": content.get("pages", 0),
                    "metadata": content.get("metadata", {}),
                    "extraction_methods": content.get("extraction_methods", [])
                })
            elif file_type in ["xlsx", "xls"]:
                analysis.update({
                    "sheets": content.get("sheets", []),
                    "total_rows": content.get("total_rows", 0),
                    "total_columns": content.get("total_columns", 0)
                })
            elif file_type == "csv":
                analysis.update({
                    "rows": content.get("rows", 0),
                    "columns": content.get("columns", 0),
                    "column_names": content.get("column_names", []),
                    "data_types": content.get("data_types", {})
                })
            elif file_type == "docx":
                analysis.update({
                    "paragraphs": content.get("paragraphs", 0),
                    "tables": content.get("tables", 0)
                })
            elif file_type == "code":
                analysis.update({
                    "language": content.get("language", "unknown"),
                    "total_lines": content.get("total_lines", 0),
                    "code_lines": content.get("code_lines", 0),
                    "comment_lines": content.get("comment_lines", 0),
                    "blank_lines": content.get("blank_lines", 0)
                })

            # Basic readability analysis
            if text:
                sentences = len([s for s in text.split('.') if s.strip()])
                words = analysis["word_count"]

                if sentences > 0 and words > 0:
                    analysis["readability_indicators"] = {
                        "average_words_per_sentence": words / sentences,
                        "sentence_count": sentences,
                        "complexity_score": min(10, (words / sentences) / 2)  # Simple complexity metric
                    }

            return analysis

        except Exception as e:
            print(f"Error in structural analysis: {e}")
            return {"error": f"Structural analysis failed: {str(e)}"}

    def _calculate_content_statistics(self, content: Dict) -> Dict[str, Any]:
        """
        Calculate detailed content statistics

        Args:
            content: Extracted file content

        Returns:
            Content statistics
        """
        try:
            text = content.get("text", "")

            if not text:
                return {"error": "No text content available for statistics"}

            words = text.split()
            unique_words = set(word.lower().strip('.,!?;:"()[]{}') for word in words)

            # Character analysis
            chars = list(text)
            letters = [c for c in chars if c.isalpha()]
            digits = [c for c in chars if c.isdigit()]
            spaces = [c for c in chars if c.isspace()]
            punctuation = [c for c in chars if c in '.,!?;:"()[]{}']

            # Word length analysis
            word_lengths = [len(word) for word in words]
            avg_word_length = sum(word_lengths) / len(word_lengths) if word_lengths else 0

            # Sentence analysis
            sentences = [s.strip() for s in text.split('.') if s.strip()]
            avg_sentence_length = len(words) / len(sentences) if sentences else 0

            return {
                "total_characters": len(text),
                "total_words": len(words),
                "unique_words": len(unique_words),
                "vocabulary_diversity": len(unique_words) / len(words) if words else 0,
                "average_word_length": round(avg_word_length, 2),
                "longest_word_length": max(word_lengths) if word_lengths else 0,
                "shortest_word_length": min(word_lengths) if word_lengths else 0,
                "total_sentences": len(sentences),
                "average_sentence_length": round(avg_sentence_length, 2),
                "character_distribution": {
                    "letters": len(letters),
                    "digits": len(digits),
                    "spaces": len(spaces),
                    "punctuation": len(punctuation),
                    "other": len(chars) - len(letters) - len(digits) - len(spaces) - len(punctuation)
                },
                "text_density": {
                    "letters_per_word": len(letters) / len(words) if words else 0,
                    "punctuation_per_sentence": len(punctuation) / len(sentences) if sentences else 0
                }
            }

        except Exception as e:
            print(f"Error calculating content statistics: {e}")
            return {"error": f"Statistics calculation failed: {str(e)}"}

    def _parse_analysis_sections(self, analysis_text: str) -> Dict[str, str]:
        """
        Parse the analysis text into structured sections

        Args:
            analysis_text: Complete analysis text from Gemini

        Returns:
            Dictionary with parsed sections
        """
        sections = {}
        current_section = "general"
        current_content = []

        lines = analysis_text.split('\n')

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Check if this is a section header
            if any(keyword in line.upper() for keyword in [
                'DOCUMENT OVERVIEW', 'CONTENT ANALYSIS', 'TONE AND STYLE',
                'STRUCTURAL AND FORMATTING', 'LANGUAGE AND LINGUISTIC', 'CONTEXTUAL AND DOMAIN',
                'QUALITY AND ACCURACY', 'PRACTICAL IMPLICATIONS', 'USER QUERY RESPONSE',
                'PDF-SPECIFIC', 'DOCUMENT-SPECIFIC', 'DATA-SPECIFIC', 'CODE-SPECIFIC'
            ]):
                # Save previous section
                if current_content:
                    sections[current_section] = '\n'.join(current_content)

                # Start new section
                current_section = line.lower().replace(':', '').strip()
                current_content = []
            else:
                current_content.append(line)

        # Save final section
        if current_content:
            sections[current_section] = '\n'.join(current_content)

        return sections

    def _get_timestamp(self) -> str:
        """Get current timestamp for analysis"""
        from datetime import datetime
        return datetime.now().isoformat()

    def analyze_file_batch(self, file_paths: List[str],
                          user_query: str = "",
                          language_context: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Analyze multiple files in batch for comprehensive comparison

        Args:
            file_paths: List of file paths to analyze
            user_query: User's question about the files
            language_context: Language context for response

        Returns:
            Batch analysis results
        """
        try:
            if not file_paths:
                return {
                    "success": False,
                    "error": "No files provided for batch analysis",
                    "results": []
                }

            batch_results = []

            # Analyze each file individually
            for i, file_path in enumerate(file_paths):
                print(f"Analyzing file {i+1}/{len(file_paths)}: {os.path.basename(file_path)}")
                result = self.analyze_file_comprehensive(
                    file_path, user_query, language_context
                )
                result['file_index'] = i + 1
                batch_results.append(result)

            # Perform comparative analysis if multiple files
            comparative_analysis = {}
            if len(file_paths) > 1:
                comparative_analysis = self._perform_comparative_file_analysis(
                    batch_results, user_query, language_context
                )

            return {
                "success": True,
                "batch_size": len(file_paths),
                "individual_results": batch_results,
                "comparative_analysis": comparative_analysis,
                "comprehensive_data": True,
                "summarization_applied": False
            }

        except Exception as e:
            print(f"Error in batch file analysis: {e}")
            return {
                "success": False,
                "error": f"Batch analysis failed: {str(e)}",
                "results": []
            }

    def _perform_comparative_file_analysis(self, batch_results: List[Dict],
                                         user_query: str,
                                         language_context: Optional[Dict]) -> Dict[str, Any]:
        """
        Perform comparative analysis across multiple files

        Args:
            batch_results: Results from individual file analyses
            user_query: User's query
            language_context: Language context

        Returns:
            Comparative analysis results
        """
        try:
            if not self.detailed_model:
                return {"error": "Text model not available for comparative analysis"}

            # Extract key information from each analysis
            file_summaries = []
            for i, result in enumerate(batch_results):
                if result.get('success'):
                    file_info = result.get('file_info', {})
                    analysis = result.get('analysis', {})
                    summary = f"File {i+1} ({file_info.get('file_name', 'Unknown')}): {analysis.get('comprehensive_analysis', 'No analysis available')[:500]}..."
                    file_summaries.append(summary)

            # Create comparative analysis prompt
            comparative_prompt = f"""
COMPREHENSIVE COMPARATIVE FILE ANALYSIS - NO SUMMARIZATION

Analyze and compare the following files based on their individual analyses. Provide COMPLETE, DETAILED comparisons without summarization.

INDIVIDUAL FILE SUMMARIES:
{chr(10).join(file_summaries)}

USER QUERY: {user_query}

COMPARATIVE ANALYSIS SECTIONS (Provide detailed information for each):

1. CONTENT SIMILARITIES AND DIFFERENCES:
   - Common themes and topics across files
   - Unique content in each file
   - Overlapping information and redundancies
   - Complementary information between files

2. STRUCTURAL AND FORMAT COMPARISON:
   - File format differences and similarities
   - Document structure comparisons
   - Organization and presentation styles
   - Technical quality variations

3. LANGUAGE AND STYLE COMPARISON:
   - Writing style similarities and differences
   - Tone and formality level comparisons
   - Vocabulary and terminology usage
   - Target audience alignment

4. QUALITY AND COMPLETENESS COMPARISON:
   - Information depth and detail levels
   - Accuracy and reliability comparisons
   - Completeness of coverage
   - Professional quality assessment

5. CONTEXTUAL RELATIONSHIPS:
   - Temporal relationships between files
   - Thematic connections and contrasts
   - Complementary or conflicting information
   - Sequential or hierarchical relationships

6. USER QUERY SPECIFIC COMPARISON:
   - Direct comparison addressing user's question
   - Evidence from each file supporting conclusions
   - Comprehensive analysis of how files relate to the query

REQUIREMENTS:
- Provide COMPLETE and DETAILED comparisons
- DO NOT summarize or condense information
- Include specific examples from each file
- Write comprehensive explanations for all observations
"""

            # Add language instructions if needed
            if language_context and language_context.get('detected_language') != 'en':
                language_name = language_context.get('detected_language_name', 'the detected language')
                comparative_prompt += f"""

LANGUAGE INSTRUCTIONS:
- Respond in {language_name}
- Maintain cultural context relevant to {language_name} speakers
- Provide the same level of detail and comprehensiveness as requested
"""

            # Generate comparative analysis
            response = self.detailed_model.generate_content(comparative_prompt)

            return {
                "comparative_analysis": response.text,
                "files_compared": len(batch_results),
                "analysis_timestamp": self._get_timestamp(),
                "language_used": language_context.get('detected_language', 'en') if language_context else 'en',
                "summarization_applied": False
            }

        except Exception as e:
            print(f"Error in comparative file analysis: {e}")
            return {
                "error": f"Comparative analysis failed: {str(e)}",
                "comparative_analysis": "Comparison could not be completed"
            }

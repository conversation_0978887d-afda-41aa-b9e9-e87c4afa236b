import chromadb
import uuid
import os
import google.generativeai as genai
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.document_loaders import <PERSON><PERSON><PERSON><PERSON>, PyPDFLoader, WebBaseLoader
from langchain_chroma import Chroma
from langchain_google_genai import GoogleGenerativeAIEmbeddings
from langchain.schema import Document
from dotenv import load_dotenv
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, APIErrorHandler, create_fallback_response
from config.gemini_config import GeminiConfig

# Import SentenceTransformer conditionally to avoid network issues
try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    print("⚠️  Warning: sentence-transformers not available")
    SentenceTransformer = None
    SENTENCE_TRANSFORMERS_AVAILABLE = False

load_dotenv()

class RAGService:
    def __init__(self):
        try:
            print("🔄 Initializing RAG service with ChromaDB...")

            # Initialize ChromaDB client
            self.client = chromadb.PersistentClient(path="./chroma_db")

            # Initialize embeddings with global config and fallback
            self.embeddings = None
            self.local_embeddings = None
            self.embedding_mode = "none"

            # Ensure global Gemini config is initialized
            if not GeminiConfig.is_initialized():
                GeminiConfig.initialize()

            # Try Gemini embeddings first
            if GeminiConfig.is_initialized():
                try:
                    self.embeddings = GoogleGenerativeAIEmbeddings(
                        model=GeminiConfig.get_embedding_model_name(),
                        google_api_key=GeminiConfig.get_api_key()
                    )
                    self.embedding_mode = "gemini"
                    print("✅ Gemini embeddings configured successfully (text-embedding-004)")
                except Exception as gemini_error:
                    print(f"⚠️  Gemini embeddings configuration failed: {gemini_error}")
                    self._initialize_local_embeddings()
            else:
                print("⚠️  Gemini API not available, trying local embeddings")
                self._initialize_local_embeddings()

            # Initialize text splitter
            self.text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=1000,
                chunk_overlap=200,
                length_function=len,
            )

            # Get or create collection with proper schema
            try:
                # Try to get existing collection
                self.collection = self.client.get_collection(name="documents")
                print("✅ Found existing ChromaDB collection")
            except Exception as collection_error:
                print(f"Creating new ChromaDB collection: {collection_error}")
                # Create new collection if it doesn't exist
                self.collection = self.client.create_collection(
                    name="documents",
                    metadata={"hnsw:space": "cosine"}
                )
                print("✅ Created new ChromaDB collection")

            # Initialize vector store based on embedding mode
            self.vector_store = None
            if self.embedding_mode == "gemini" and self.embeddings:
                try:
                    self.vector_store = Chroma(
                        client=self.client,
                        collection_name="documents",
                        embedding_function=self.embeddings,
                    )
                    print("✅ LangChain vector store initialized with Gemini embeddings")
                except Exception as vector_error:
                    print(f"⚠️  Vector store initialization failed: {vector_error}")
                    self.vector_store = None
            elif self.embedding_mode == "local" and self.local_embeddings:
                # For local embeddings, we'll handle this differently in get_context method
                print("✅ Local embedding mode active - will use direct ChromaDB operations")
            else:
                print("⚠️  Vector store disabled - no embeddings available")

            print("✅ RAG service initialized successfully")
            print(f"   Using Gemini embeddings: {'Yes' if self.embedding_mode == 'gemini' else 'No'}")
            print(f"   Using local embeddings: {'Yes' if self.embedding_mode == 'local' else 'No'}")
            print(f"   ChromaDB collection: {'Active' if self.collection else 'Disabled'}")

        except Exception as e:
            print(f"❌ RAG service initialization failed: {str(e)}")
            print("   Falling back to disabled state")
            self.client = None
            self.embedding_model = None
            self.embeddings = None
            self.text_splitter = None
            self.collection = None
            self.vector_store = None

    def _initialize_local_embeddings(self):
        """Initialize local SentenceTransformer embeddings as fallback"""
        if SENTENCE_TRANSFORMERS_AVAILABLE:
            try:
                print("🔄 Initializing local embedding fallback...")
                self.local_embeddings = SentenceTransformer('all-MiniLM-L6-v2')
                self.embedding_mode = "local"
                print("✅ Local embedding fallback initialized (all-MiniLM-L6-v2)")
            except Exception as local_error:
                print(f"⚠️  Local embedding fallback failed: {local_error}")
                print("   Continuing with ChromaDB-only mode")
                self.embedding_mode = "none"
        else:
            print("   sentence-transformers not available, continuing with ChromaDB-only mode")
            self.embedding_mode = "none"

    def is_initialized(self):
        """Check if RAG service is properly initialized"""
        return (self.client is not None and
                self.embeddings is not None and
                self.collection is not None and
                self.vector_store is not None)

    def get_status(self):
        """Get RAG service status"""
        if self.is_initialized():
            try:
                # Try to get collection count
                count = self.collection.count()
                return {
                    'status': 'active',
                    'message': f'✅ RAG service active with {count} documents',
                    'document_count': count,
                    'collection_name': 'documents'
                }
            except Exception as e:
                return {
                    'status': 'error',
                    'message': f'❌ RAG service error: {str(e)}',
                    'document_count': 0
                }
        else:
            return {
                'status': 'disabled',
                'message': '⚠️ RAG service disabled - ChromaDB not initialized',
                'document_count': 0
            }

    def load_documents(self, file_paths):
        """Load documents using LangChain document loaders"""
        documents = []
        for file_path in file_paths:
            if file_path.endswith('.pdf'):
                loader = PyPDFLoader(file_path)
            elif file_path.startswith('http'):
                loader = WebBaseLoader(file_path)
            else:
                loader = TextLoader(file_path)
            loaded_docs = loader.load()
            documents.extend(loaded_docs)
        
        return documents
    
    def add_documents(self, documents, metadatas=None, ids=None):
        """Add documents to the knowledge base using LangChain with error handling"""
        if not self.is_initialized():
            print("⚠️  RAG service not initialized - cannot add documents")
            return

        if not ids:
            ids = [str(uuid.uuid4()) for _ in documents]

        try:
            # Split documents
            split_docs = self.text_splitter.split_documents(documents)

            # Add to LangChain vector store if Gemini embeddings are available
            if self.embedding_mode == "gemini" and self.vector_store:
                try:
                    self.vector_store.add_documents(split_docs)
                    print("✅ Documents added to Gemini vector store")
                except Exception as langchain_error:
                    error_msg = str(langchain_error)
                    if APIErrorHandler.is_quota_error(error_msg):
                        print(f"⚠️  Gemini quota exceeded — using local embedding fallback")
                        # Switch to local embedding mode for this operation
                        self._add_with_local_embeddings(split_docs, metadatas, ids)
                        return
                    else:
                        print(f"LangChain vector store error: {error_msg}")
                        # Continue with local embeddings
                        self._add_with_local_embeddings(split_docs, metadatas, ids)
                        return

            # Use local embeddings if that's the current mode
            elif self.embedding_mode == "local":
                self._add_with_local_embeddings(split_docs, metadatas, ids)
                return

            # Store without embeddings if no embedding method available
            else:
                texts = [doc.page_content for doc in split_docs]
                self.collection.add(
                    documents=texts,
                    metadatas=metadatas or [{} for _ in texts],
                    ids=ids[:len(texts)]
                )
                print("⚠️  Documents stored without embeddings")

        except Exception as e:
            print(f"Error adding documents: {str(e)}")
            raise e

    def _add_with_local_embeddings(self, split_docs, metadatas=None, ids=None):
        """Add documents using local SentenceTransformer embeddings"""
        if not self.local_embeddings:
            print("⚠️  Local embeddings not available, storing without embeddings")
            texts = [doc.page_content for doc in split_docs]
            self.collection.add(
                documents=texts,
                metadatas=metadatas or [{} for _ in texts],
                ids=ids[:len(texts)] if ids else [str(uuid.uuid4()) for _ in texts]
            )
            return

        try:
            texts = [doc.page_content for doc in split_docs]
            # Generate embeddings using local model
            embeddings = self.local_embeddings.encode(texts).tolist()

            self.collection.add(
                embeddings=embeddings,
                documents=texts,
                metadatas=metadatas or [{} for _ in texts],
                ids=ids[:len(texts)] if ids else [str(uuid.uuid4()) for _ in texts]
            )
            print(f"✅ Documents added using local embeddings ({len(texts)} chunks)")
        except Exception as local_error:
            print(f"⚠️  Local embedding failed: {local_error}")
            # Store without embeddings as final fallback
            texts = [doc.page_content for doc in split_docs]
            self.collection.add(
                documents=texts,
                metadatas=metadatas or [{} for _ in texts],
                ids=ids[:len(texts)] if ids else [str(uuid.uuid4()) for _ in texts]
            )
    
    def add_texts(self, texts, metadatas=None):
        """Add raw texts to knowledge base"""
        documents = [Document(page_content=text) for text in texts]
        self.add_documents(documents, metadatas)
    
    def search_similar(self, query, n_results=3):
        """Search for similar documents using LangChain with error handling and local fallback"""
        if not self.is_initialized():
            print("⚠️  RAG service not initialized - returning empty results")
            return {'documents': [[]], 'metadatas': [[]]}

        # Try Gemini embeddings first if available
        if self.embedding_mode == "gemini" and self.vector_store:
            try:
                results = self.vector_store.similarity_search(query, k=n_results)
                return {
                    'documents': [[doc.page_content for doc in results]],
                    'metadatas': [[doc.metadata for doc in results]]
                }
            except Exception as search_error:
                error_msg = str(search_error)
                if APIErrorHandler.is_quota_error(error_msg):
                    print(f"⚠️  Gemini quota exceeded — using local embedding fallback")
                    # Fall back to local embeddings
                    return self._search_with_local_embeddings(query, n_results)
                else:
                    print(f"Gemini search error: {error_msg}")
                    return self._search_with_local_embeddings(query, n_results)

        # Use local embeddings if that's the current mode
        elif self.embedding_mode == "local":
            return self._search_with_local_embeddings(query, n_results)

        # Fallback to basic text search if no embeddings available
        else:
            return self._basic_text_search(query, n_results)

    def _search_with_local_embeddings(self, query, n_results=3):
        """Search using local SentenceTransformer embeddings"""
        if not self.local_embeddings:
            print("⚠️  Local embeddings not available, falling back to text search")
            return self._basic_text_search(query, n_results)

        try:
            # Generate query embedding using local model
            query_embedding = self.local_embeddings.encode([query])[0].tolist()

            # Search in ChromaDB using local embedding
            results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=n_results
            )

            return {
                'documents': [results['documents'][0] if results['documents'] else []],
                'metadatas': [results['metadatas'][0] if results['metadatas'] else []]
            }
        except Exception as local_error:
            print(f"Local embedding search error: {local_error}")
            return self._basic_text_search(query, n_results)

    def _basic_text_search(self, query, n_results=3):
        """Basic text-based search fallback"""
        try:
            # Simple text-based search as last resort
            all_docs = self.collection.get()
            if all_docs and all_docs.get('documents'):
                # Basic keyword matching
                query_lower = query.lower()
                matching_docs = []
                matching_metadata = []

                for i, doc in enumerate(all_docs['documents']):
                    if any(word in doc.lower() for word in query_lower.split()):
                        matching_docs.append(doc)
                        metadata = all_docs.get('metadatas', [{}])[i] if i < len(all_docs.get('metadatas', [])) else {}
                        matching_metadata.append(metadata)

                        if len(matching_docs) >= n_results:
                            break

                return {
                    'documents': [matching_docs],
                    'metadatas': [matching_metadata]
                }
        except Exception as fallback_error:
            print(f"Fallback search failed: {fallback_error}")

        # Return empty results if all searches fail
        return {
            'documents': [[]],
            'metadatas': [[]]
        }
    
    def get_context(self, query, n_results=3):
        """Get relevant context for RAG using LangChain"""
        similar_docs = self.search_similar(query, n_results)
        
        if similar_docs['documents']:
            context = "\n\n".join(similar_docs['documents'][0])
            return context
        return ""
    
    def create_retrieval_chain(self, llm):
        """Create a retrieval chain for RAG"""
        from langchain.chains import RetrievalQA
        
        retriever = self.vector_store.as_retriever(
            search_type="similarity",
            search_kwargs={"k": 3}
        )
        
        return RetrievalQA.from_chain_type(
            llm=llm,
            chain_type="stuff",
            retriever=retriever,
            return_source_documents=True
        )
    
    def rag_query(self, query, llm):
        """Execute RAG query using LangChain"""
        qa_chain = self.create_retrieval_chain(llm)
        result = qa_chain({"query": query})
        return result
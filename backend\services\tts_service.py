"""
Enhanced Text-to-Speech Service for Multilingual Support
Handles TTS generation for various languages including Indian languages
Integrated with LanguageService for better language detection and processing
"""

import os
import sys
import io
import tempfile
from typing import Dict, Optional, Tuple, Any, List
from gtts import gTTS
from langdetect import detect
import logging

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from services.language_service import LanguageService

logger = logging.getLogger(__name__)

class TTSService:
    def __init__(self):
        """Initialize TTS service with language mappings and enhanced language detection"""

        # Initialize language service for enhanced language detection
        self.language_service = LanguageService()
        
        # Mapping of language codes to gTTS supported languages
        self.supported_languages = {
            # English
            'en': 'en',
            
            # Indian Languages supported by gTTS
            'hi': 'hi',      # Hindi
            'bn': 'bn',      # Bengali
            'gu': 'gu',      # Gujarati
            'kn': 'kn',      # Kannada
            'ml': 'ml',      # Malayalam
            'mr': 'mr',      # Marathi
            'ta': 'ta',      # Tamil
            'te': 'te',      # Telugu
            'ur': 'ur',      # Urdu
            'pa': 'pa',      # Punjabi
            'ne': 'ne',      # Nepali
            'si': 'si',      # Sinhala
            
            # Other commonly detected languages
            'fr': 'fr',      # French
            'es': 'es',      # Spanish
            'de': 'de',      # German
            'it': 'it',      # Italian
            'pt': 'pt',      # Portuguese
            'ru': 'ru',      # Russian
            'ja': 'ja',      # Japanese
            'ko': 'ko',      # Korean
            'zh': 'zh',      # Chinese
            'ar': 'ar',      # Arabic
        }
        
        # Language names for better user experience
        self.language_names = {
            'en': 'English',
            'hi': 'Hindi',
            'bn': 'Bengali', 
            'gu': 'Gujarati',
            'kn': 'Kannada',
            'ml': 'Malayalam',
            'mr': 'Marathi',
            'ta': 'Tamil',
            'te': 'Telugu',
            'ur': 'Urdu',
            'pa': 'Punjabi',
            'ne': 'Nepali',
            'si': 'Sinhala',
            'fr': 'French',
            'es': 'Spanish',
            'de': 'German',
            'it': 'Italian',
            'pt': 'Portuguese',
            'ru': 'Russian',
            'ja': 'Japanese',
            'ko': 'Korean',
            'zh': 'Chinese',
            'ar': 'Arabic'
        }
        
        # Indian language codes for special handling
        self.indian_languages = {
            'hi', 'bn', 'gu', 'kn', 'ml', 'mr', 'ta', 'te', 'ur', 'pa', 'ne', 'si'
        }
    
    def detect_language(self, text: str) -> Tuple[str, str]:
        """
        Enhanced language detection using LanguageService with fallback to langdetect

        Args:
            text: Text to analyze

        Returns:
            Tuple of (language_code, language_name)
        """
        try:
            if not text or len(text.strip()) < 3:
                return 'en', 'English'

            # Use enhanced language service for detection
            language_analysis = self.language_service.detect_and_prepare_message(text)
            detected_code = language_analysis.get('detected_language', 'en')

            # Map to TTS-compatible language codes
            tts_language_mapping = {
                'hi': 'hi',  # Hindi
                'ta': 'ta',  # Tamil
                'te': 'te',  # Telugu
                'ml': 'ml',  # Malayalam
                'kn': 'kn',  # Kannada
                'bn': 'bn',  # Bengali
                'gu': 'gu',  # Gujarati
                'mr': 'mr',  # Marathi
                'pa': 'pa',  # Punjabi
                'or': 'or',  # Odia
                'as': 'as',  # Assamese
                'ur': 'ur',  # Urdu
                'en': 'en',  # English
                'ne': 'ne',  # Nepali
                'si': 'si',  # Sinhala
                'fr': 'fr',  # French
                'es': 'es',  # Spanish
                'de': 'de',  # German
                'it': 'it',  # Italian
                'pt': 'pt',  # Portuguese
                'ru': 'ru',  # Russian
                'ja': 'ja',  # Japanese
                'ko': 'ko',  # Korean
                'zh': 'zh',  # Chinese
                'ar': 'ar'   # Arabic
            }

            tts_code = tts_language_mapping.get(detected_code, 'en')
            language_name = self.language_names.get(tts_code, 'English')

            return tts_code, language_name

        except Exception as e:
            logger.warning(f"Enhanced language detection failed, using fallback: {str(e)}")
            # Fallback to original langdetect method
            try:
                detected_code = detect(text)
                if detected_code in ['so', 'nl', 'af', 'cy'] and self._is_likely_english(text):
                    detected_code = 'en'
                language_name = self.language_names.get(detected_code, 'Unknown')
                return detected_code, language_name
            except:
                return 'en', 'English'

    def _is_likely_english(self, text: str) -> bool:
        """
        Check if text is likely English based on common English words

        Args:
            text: Text to check

        Returns:
            bool: True if likely English
        """
        english_words = {
            'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
            'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before',
            'after', 'above', 'below', 'between', 'among', 'is', 'are', 'was',
            'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does',
            'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must',
            'can', 'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she',
            'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them', 'my', 'your',
            'his', 'her', 'its', 'our', 'their', 'hello', 'hi', 'how', 'what',
            'when', 'where', 'why', 'who', 'which', 'today', 'tomorrow',
            'yesterday', 'good', 'bad', 'great', 'nice', 'please', 'thank',
            'thanks', 'welcome', 'sorry', 'excuse', 'world', 'time', 'day',
            'year', 'way', 'new', 'first', 'last', 'long', 'great', 'little',
            'own', 'other', 'old', 'right', 'big', 'high', 'different', 'small',
            'large', 'next', 'early', 'young', 'important', 'few', 'public',
            'same', 'able'
        }

        # Convert to lowercase and split into words
        words = text.lower().split()

        # Count English words
        english_count = sum(1 for word in words if word.strip('.,!?;:"()[]{}') in english_words)

        # If more than 30% of words are common English words, consider it English
        if len(words) > 0:
            return (english_count / len(words)) >= 0.3

        return False
    
    def is_language_supported(self, language_code: str) -> bool:
        """Check if language is supported for TTS"""
        return language_code in self.supported_languages
    
    def is_indian_language(self, language_code: str) -> bool:
        """Check if language is an Indian language"""
        return language_code in self.indian_languages
    
    def generate_speech(self, text: str, language_code: Optional[str] = None) -> Dict:
        """
        Generate speech audio for the given text
        
        Args:
            text: Text to convert to speech
            language_code: Optional language code. If not provided, will auto-detect
            
        Returns:
            Dict containing success status, audio data, and metadata
        """
        try:
            if not text or len(text.strip()) == 0:
                return {
                    'success': False,
                    'error': 'No text provided',
                    'language_code': None,
                    'language_name': None
                }
            
            # Detect language if not provided
            if not language_code:
                language_code, language_name = self.detect_language(text)
            else:
                language_name = self.language_names.get(language_code, 'Unknown')
            
            # Check if language is supported
            if not self.is_language_supported(language_code):
                return {
                    'success': False,
                    'error': f'Language {language_name} ({language_code}) is not supported for TTS',
                    'language_code': language_code,
                    'language_name': language_name,
                    'supported_languages': list(self.supported_languages.keys())
                }
            
            # Get gTTS language code
            gtts_lang = self.supported_languages[language_code]
            
            # Generate speech using gTTS
            tts = gTTS(text=text, lang=gtts_lang, slow=False)
            
            # Save to memory buffer
            audio_buffer = io.BytesIO()
            tts.write_to_fp(audio_buffer)
            audio_buffer.seek(0)
            
            return {
                'success': True,
                'audio_data': audio_buffer.getvalue(),
                'language_code': language_code,
                'language_name': language_name,
                'is_indian_language': self.is_indian_language(language_code),
                'content_type': 'audio/mpeg'
            }
            
        except Exception as e:
            logger.error(f"TTS generation failed: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'language_code': language_code if 'language_code' in locals() else None,
                'language_name': language_name if 'language_name' in locals() else None
            }
    
    def get_supported_languages(self) -> Dict:
        """Get list of supported languages"""
        return {
            'languages': self.supported_languages,
            'language_names': self.language_names,
            'indian_languages': list(self.indian_languages),
            'total_supported': len(self.supported_languages)
        }
    
    def get_language_info(self, text: str) -> Dict:
        """Get comprehensive language information for text"""
        language_code, language_name = self.detect_language(text)
        
        return {
            'detected_language': language_code,
            'language_name': language_name,
            'is_supported': self.is_language_supported(language_code),
            'is_indian_language': self.is_indian_language(language_code),
            'can_generate_tts': self.is_language_supported(language_code)
        }

    def generate_speech_with_context(self, text: str,
                                   language_context: Optional[Dict] = None,
                                   voice_settings: Optional[Dict] = None) -> Dict:
        """
        Generate speech with enhanced language context and voice settings

        Args:
            text: Text to convert to speech
            language_context: Language context from LanguageService
            voice_settings: Optional voice settings (speed, pitch, etc.)

        Returns:
            Dict containing success status, audio data, and enhanced metadata
        """
        try:
            if not text or len(text.strip()) == 0:
                return {
                    'success': False,
                    'error': 'No text provided',
                    'language_code': None,
                    'language_name': None
                }

            # Use language context if provided, otherwise detect
            if language_context and language_context.get('detected_language'):
                language_code = language_context['detected_language']
                language_name = language_context.get('detected_language_name', 'Unknown')
                confidence = language_context.get('confidence', 0.0)
            else:
                language_code, language_name = self.detect_language(text)
                confidence = 0.8  # Default confidence for fallback detection

            # Check if language is supported
            if not self.is_language_supported(language_code):
                return {
                    'success': False,
                    'error': f'Language {language_name} ({language_code}) is not supported for TTS',
                    'language_code': language_code,
                    'language_name': language_name,
                    'supported_languages': list(self.supported_languages.keys()),
                    'language_context': language_context
                }

            # Get gTTS language code
            gtts_lang = self.supported_languages[language_code]

            # Apply voice settings
            slow_speech = False
            if voice_settings:
                slow_speech = voice_settings.get('slow', False)

            # Generate speech using gTTS
            tts = gTTS(text=text, lang=gtts_lang, slow=slow_speech)

            # Save to memory buffer
            audio_buffer = io.BytesIO()
            tts.write_to_fp(audio_buffer)
            audio_buffer.seek(0)

            return {
                'success': True,
                'audio_data': audio_buffer.getvalue(),
                'language_code': language_code,
                'language_name': language_name,
                'is_indian_language': self.is_indian_language(language_code),
                'content_type': 'audio/mpeg',
                'language_context': language_context,
                'confidence': confidence,
                'voice_settings_applied': voice_settings,
                'enhanced_processing': True,
                'cultural_context': language_context.get('cultural_context', {}) if language_context else {}
            }

        except Exception as e:
            logger.error(f"Enhanced TTS generation failed: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'language_code': language_code if 'language_code' in locals() else None,
                'language_name': language_name if 'language_name' in locals() else None,
                'language_context': language_context
            }

    def generate_multilingual_speech_batch(self, text_segments: List[Dict]) -> Dict:
        """
        Generate speech for multiple text segments with different languages

        Args:
            text_segments: List of dicts with 'text' and optional 'language_context'

        Returns:
            Dict with batch processing results
        """
        try:
            if not text_segments:
                return {
                    'success': False,
                    'error': 'No text segments provided',
                    'results': []
                }

            batch_results = []
            total_audio_data = []

            for i, segment in enumerate(text_segments):
                text = segment.get('text', '')
                language_context = segment.get('language_context')
                voice_settings = segment.get('voice_settings')

                if not text.strip():
                    continue

                # Generate speech for this segment
                result = self.generate_speech_with_context(
                    text, language_context, voice_settings
                )

                result['segment_index'] = i
                result['original_text'] = text
                batch_results.append(result)

                # Collect audio data if successful
                if result.get('success') and result.get('audio_data'):
                    total_audio_data.append(result['audio_data'])

            return {
                'success': len(batch_results) > 0,
                'batch_size': len(text_segments),
                'successful_segments': len([r for r in batch_results if r.get('success')]),
                'failed_segments': len([r for r in batch_results if not r.get('success')]),
                'individual_results': batch_results,
                'combined_audio_available': len(total_audio_data) > 0,
                'total_audio_segments': len(total_audio_data),
                'multilingual_processing': True
            }

        except Exception as e:
            logger.error(f"Batch TTS generation failed: {str(e)}")
            return {
                'success': False,
                'error': f'Batch processing failed: {str(e)}',
                'results': []
            }

    def get_optimal_voice_settings(self, text: str, language_context: Optional[Dict] = None) -> Dict:
        """
        Get optimal voice settings based on text content and language

        Args:
            text: Text to analyze
            language_context: Language context from LanguageService

        Returns:
            Dict with recommended voice settings
        """
        try:
            # Get language information
            if language_context:
                language_code = language_context.get('detected_language', 'en')
                is_indian = language_code in self.indian_languages
            else:
                language_code, _ = self.detect_language(text)
                is_indian = self.is_indian_language(language_code)

            # Analyze text characteristics
            word_count = len(text.split())
            has_punctuation = any(p in text for p in '.!?;:')
            has_numbers = any(c.isdigit() for c in text)

            # Base settings
            settings = {
                'slow': False,
                'language_optimized': True,
                'recommended_speed': 'normal'
            }

            # Adjust for Indian languages (generally benefit from slightly slower speech)
            if is_indian:
                settings.update({
                    'slow': word_count > 50,  # Use slow for longer Indian language text
                    'recommended_speed': 'slightly_slow' if word_count > 30 else 'normal',
                    'cultural_optimization': True
                })

            # Adjust for complex content
            if has_numbers or word_count > 100:
                settings.update({
                    'slow': True,
                    'recommended_speed': 'slow',
                    'complexity_adjusted': True
                })

            # Add language-specific recommendations
            language_recommendations = {
                'hi': {'emphasis': 'clear_pronunciation', 'pace': 'moderate'},
                'ta': {'emphasis': 'tonal_accuracy', 'pace': 'moderate'},
                'te': {'emphasis': 'clear_pronunciation', 'pace': 'moderate'},
                'ml': {'emphasis': 'tonal_accuracy', 'pace': 'moderate'},
                'kn': {'emphasis': 'clear_pronunciation', 'pace': 'moderate'},
                'bn': {'emphasis': 'tonal_accuracy', 'pace': 'moderate'},
                'en': {'emphasis': 'natural_flow', 'pace': 'normal'}
            }

            if language_code in language_recommendations:
                settings.update(language_recommendations[language_code])

            return {
                'voice_settings': settings,
                'language_code': language_code,
                'is_indian_language': is_indian,
                'text_analysis': {
                    'word_count': word_count,
                    'has_punctuation': has_punctuation,
                    'has_numbers': has_numbers,
                    'complexity_level': 'high' if word_count > 100 else 'medium' if word_count > 30 else 'low'
                },
                'optimization_applied': True
            }

        except Exception as e:
            logger.error(f"Voice settings optimization failed: {str(e)}")
            return {
                'voice_settings': {'slow': False, 'recommended_speed': 'normal'},
                'error': str(e),
                'optimization_applied': False
            }

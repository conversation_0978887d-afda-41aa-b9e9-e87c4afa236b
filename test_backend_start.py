#!/usr/bin/env python3
"""
Simple test to verify the backend starts correctly with Socket.IO and eventlet.
"""

import sys
import os

# Add backend to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

def test_backend_import():
    """Test that the backend can be imported without errors"""
    print("Testing backend import...")
    
    try:
        # Import the main app module
        import app
        print("✓ Backend app imported successfully")
        
        # Check if SocketIO is initialized
        if hasattr(app, 'socketio'):
            print("✓ SocketIO instance found")
            print(f"  - Async mode: {app.socketio.async_mode}")
        else:
            print("✗ SocketIO instance not found")
            return False
            
        # Check if Flask app is initialized
        if hasattr(app, 'app'):
            print("✓ Flask app instance found")
        else:
            print("✗ Flask app instance not found")
            return False
            
        return True
        
    except Exception as e:
        print(f"✗ Backend import failed: {e}")
        return False

def main():
    """Run the test"""
    print("=" * 50)
    print("Testing Backend Startup")
    print("=" * 50)
    
    success = test_backend_import()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 BACKEND IMPORT TEST PASSED!")
        print("\nThe backend is ready to start.")
        print("To start the backend manually:")
        print("1. cd backend")
        print("2. venv\\Scripts\\activate")
        print("3. python app.py")
    else:
        print("❌ BACKEND IMPORT TEST FAILED!")
        print("Please check the error messages above.")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())

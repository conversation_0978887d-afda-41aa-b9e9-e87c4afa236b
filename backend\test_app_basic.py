#!/usr/bin/env python3
"""
Test basic Flask app functionality without SocketIO
"""

import sys
import os

# Add the backend directory to the path
sys.path.insert(0, os.path.dirname(__file__))

print("Testing basic Flask app...")

try:
    from flask import Flask
    from flask_cors import CORS
    print("✓ Flask imports successful")
except ImportError as e:
    print(f"✗ Flask imports failed: {e}")
    sys.exit(1)

try:
    # Create a minimal Flask app similar to the main app
    app = Flask(__name__)
    CORS(app)
    
    @app.route('/health')
    def health():
        return {'status': 'ok', 'message': 'Flask app is running'}
    
    print("✓ Basic Flask app created successfully")
    print("✓ Health endpoint added")
    
    # Test if we can start the app (don't actually run it)
    print("✓ App is ready to run")
    print("  To test: python test_app_basic.py")
    print("  Then visit: http://localhost:5001/health")
    
    if __name__ == '__main__':
        print("Starting Flask app on port 5001...")
        app.run(debug=True, port=5001, host='0.0.0.0')
    
except Exception as e:
    print(f"✗ Flask app creation failed: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

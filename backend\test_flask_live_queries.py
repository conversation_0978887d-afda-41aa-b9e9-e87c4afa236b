#!/usr/bin/env python3
"""
Test script to verify Flask backend with live web search queries
"""

import requests
import json
import time

# Test the Flask backend
BASE_URL = "http://localhost:5000"

def test_flask_query(query, description):
    """Test a query against the Flask backend"""
    print(f"\n🧪 Testing: {description}")
    print(f"Query: '{query}'")
    print("-" * 60)
    
    try:
        # Test the chat endpoint
        response = requests.post(
            f"{BASE_URL}/api/chat",
            json={"message": query},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('response'):
                response_text = data['response']
                print(f"✅ Response received (length: {len(response_text)} chars)")
                print(f"Preview: {response_text[:200]}...")
                
                # Check for live data indicators
                live_indicators = ['current', 'latest', 'today', 'recent', 'now', '2024', '2025', 'breaking']
                has_live_data = any(indicator in response_text.lower() for indicator in live_indicators)
                
                if has_live_data:
                    print("✓ Response contains live/current information indicators")
                else:
                    print("⚠️  Response may lack live information indicators")
                
                return True
            else:
                print(f"⚠️  No response in data: {data}")
                return False
        else:
            print(f"❌ HTTP Error {response.status_code}: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - Is Flask backend running?")
        return False
    except requests.exceptions.Timeout:
        print("❌ Request timeout")
        return False
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def main():
    print("🌐 Testing Flask Backend with Live Web Search")
    print("=" * 70)
    print("Make sure Flask backend is running: python app.py")
    print("=" * 70)
    
    # Wait for user confirmation
    input("Press Enter when Flask backend is ready...")
    
    # Test queries as requested by user
    test_queries = [
        ("What's the latest news in India?", "Live News Query"),
        ("Show me the current weather update in Delhi.", "Live Weather Query"),
        ("What are today's breaking news headlines?", "Breaking News Query"),
        ("Current stock market updates", "Stock Market Query"),
        ("Explain artificial intelligence", "General Knowledge Query")
    ]
    
    results = []
    
    for query, description in test_queries:
        success = test_flask_query(query, description)
        results.append((description, success))
        time.sleep(2)  # Brief pause between requests
    
    # Summary
    print("\n" + "=" * 70)
    print("🎯 Flask Backend Test Summary")
    print("=" * 70)
    
    successful = sum(1 for _, success in results if success)
    total = len(results)
    
    print(f"✅ Successful queries: {successful}/{total}")
    
    for description, success in results:
        status = "✅" if success else "❌"
        print(f"   {status} {description}")
    
    if successful == total:
        print("\n🎉 All tests passed! Live web search is working correctly.")
        print("\n📊 Expected Console Messages in Flask Backend:")
        print("   🌐 Gemini Live Search active")
        print("   ✅ Live web data retrieved successfully via Gemini")
        print("   🔄 Serper fallback available (google.serper.dev)")
        print("   ✅ Response contains live, time-sensitive content")
    else:
        print(f"\n⚠️  {total - successful} tests failed. Check Flask backend logs.")
    
    print("\n💡 Next Steps:")
    print("   1. Test these queries through the React frontend")
    print("   2. Verify real-time responses in the browser")
    print("   3. Monitor Flask console for expected success messages")

if __name__ == "__main__":
    main()

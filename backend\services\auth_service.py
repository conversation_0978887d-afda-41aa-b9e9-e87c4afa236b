import uuid
from models.database import db, User

class AuthService:
    def __init__(self):
        pass
    
    def authenticate_user(self, username, email):
        """Simple authentication - in production, use proper auth"""
        user = User.query.filter_by(email=email).first()
        
        if not user:
            # Create new user
            user = User(
                id=str(uuid.uuid4()),
                username=username,
                email=email
            )
            db.session.add(user)
            db.session.commit()
        
        return user.id
    
    def validate_user(self, user_id):
        """Validate user exists - auto-create if doesn't exist"""
        user = User.query.get(user_id)

        if not user:
            # Auto-create user if doesn't exist (for development)
            try:
                user = User(
                    id=user_id,
                    username=f"user_{user_id[:8]}",
                    email=f"user_{user_id[:8]}@example.com"
                )
                db.session.add(user)
                db.session.commit()
                print(f"Auto-created user: {user_id}")
                return True
            except Exception as e:
                print(f"Error auto-creating user: {e}")
                db.session.rollback()
                return False

        return True
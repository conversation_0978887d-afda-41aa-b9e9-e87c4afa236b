#!/usr/bin/env python3
"""
Test script to verify the new live web search hierarchy:
Gemini (with web augmentation) → <PERSON><PERSON> → DuckDuckGo
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

print("🌐 Testing Live Web Search with New Hierarchy")
print("=" * 70)
print("New Search Order: Gemini → Serper → DuckDuckGo")
print("=" * 70)

# Import services
try:
    from services.gemini_web_service import GeminiWebService
    from services.web_search_service import WebSearchService
    
    print("✓ Services imported successfully")
    
    # Initialize services
    gemini_web_service = GeminiWebService()
    web_search_service = WebSearchService()
    
    print("✅ Services initialized successfully")
    
except Exception as e:
    print(f"❌ Service initialization failed: {e}")
    sys.exit(1)

# Test queries as requested by user
test_queries = [
    "What's the latest news in India?",
    "Show me the current weather in Chennai.",
    "What are today's breaking news headlines?",
    "Current stock market updates"
]

print(f"\n🧪 Testing {len(test_queries)} live data queries...")

for i, query in enumerate(test_queries, 1):
    print(f"\n{i}️⃣ Testing Query: '{query}'")
    print("-" * 60)
    
    # Test 1: Check if query needs live data
    needs_live = gemini_web_service.needs_live_data(query)
    print(f"   Live data needed: {needs_live}")
    
    # Test 2: Gemini Live Data Retrieval (Primary)
    print("   🤖 Testing Gemini Live Data Retrieval...")
    try:
        gemini_result = gemini_web_service.get_live_data_with_gemini(query)
        if gemini_result['success']:
            response_length = len(gemini_result['data'])
            print(f"   ✅ Gemini live data successful! (length: {response_length} chars)")
            print(f"   Source: {gemini_result['source']}")
            print(f"   Confidence: {gemini_result.get('confidence', 'unknown')}")
            print(f"   Preview: {gemini_result['data'][:100]}...")
            
            # Check for live indicators
            live_indicators = ['current', 'latest', 'today', 'recent', 'now', '2024', '2025']
            has_live_data = any(indicator in gemini_result['data'].lower() for indicator in live_indicators)
            if has_live_data:
                print("   ✓ Response contains live/current information indicators")
            else:
                print("   ⚠️  Response may lack live information indicators")
        else:
            print(f"   ⚠️  Gemini live data failed: {gemini_result['error']}")
            if gemini_result.get('fallback_needed'):
                print("   🔄 Fallback needed")
    except Exception as gemini_error:
        print(f"   ❌ Gemini live data error: {gemini_error}")
    
    # Test 3: Serper Fallback (Secondary)
    print("   📡 Testing Serper Fallback...")
    try:
        serper_result = gemini_web_service.search_with_serper_fallback(query, 3)
        if serper_result['success']:
            print(f"   ✅ Serper fallback successful! Found {len(serper_result['data'])} results")
            for j, result in enumerate(serper_result['data'][:2], 1):
                print(f"      {j}. {result['title'][:50]}...")
        else:
            print(f"   ⚠️  Serper fallback failed: {serper_result['error']}")
            if serper_result.get('fallback_needed'):
                print("   🔄 DuckDuckGo fallback needed")
    except Exception as serper_error:
        print(f"   ❌ Serper fallback error: {serper_error}")
    
    # Test 4: DuckDuckGo Fallback (Tertiary)
    print("   🦆 Testing DuckDuckGo Fallback...")
    try:
        ddg_result = gemini_web_service.search_with_duckduckgo_fallback(query, 3)
        if ddg_result['success']:
            print(f"   ✅ DuckDuckGo fallback successful! Found {len(ddg_result['data'])} results")
            for j, result in enumerate(ddg_result['data'][:2], 1):
                print(f"      {j}. {result['title'][:50]}...")
        else:
            print(f"   ⚠️  DuckDuckGo fallback failed: {ddg_result['error']}")
    except Exception as ddg_error:
        print(f"   ❌ DuckDuckGo fallback error: {ddg_error}")
    
    # Test 5: Comprehensive Response (Full Pipeline)
    print("   🔧 Testing Comprehensive Response Pipeline...")
    try:
        comprehensive_response = gemini_web_service.get_comprehensive_response(query)
        if comprehensive_response and len(comprehensive_response) > 50:
            print(f"   ✅ Comprehensive response generated! (length: {len(comprehensive_response)} chars)")
            print(f"   Preview: {comprehensive_response[:150]}...")
            
            # Check for success indicators
            success_indicators = [
                'live, time-sensitive content',
                'current web data',
                'current web search',
                'available search results'
            ]
            
            success_type = None
            for indicator in success_indicators:
                if indicator in comprehensive_response:
                    success_type = indicator
                    break
            
            if success_type:
                print(f"   ✓ Success type: {success_type}")
            else:
                print("   ⚠️  Response type unclear")
        else:
            print("   ⚠️  Comprehensive response seems short or empty")
    except Exception as comp_error:
        print(f"   ❌ Comprehensive response error: {comp_error}")

print("\n" + "=" * 70)
print("🎯 Live Web Search Test Summary")
print("\n📊 Expected Results:")
print("   ✅ Gemini provides live data with current information")
print("   ✅ Serper fallback works with api.serper.dev endpoint")
print("   ✅ DuckDuckGo fallback provides alternative results")
print("   ✅ No DNS resolution errors or connection timeouts")
print("   ✅ Smart routing based on response confidence")
print("\n🚀 Success Criteria:")
print("   • Gemini responses contain live/current indicators")
print("   • Serper uses correct API endpoint (api.serper.dev)")
print("   • DuckDuckGo provides fallback when others fail")
print("   • Comprehensive pipeline handles all failure scenarios")
print("   • Console shows expected success messages")
print("\n💡 Expected Console Messages:")
print("   🌐 Gemini Live Search active")
print("   ✅ Live web data retrieved successfully via Gemini")
print("   🔄 Serper fallback available (api.serper.dev)")
print("   ✅ Response contains live, time-sensitive content")
print("\n🔧 Next Steps:")
print("   1. Start Flask backend: python app.py")
print("   2. Test queries through frontend")
print("   3. Verify real-time responses")
print("   4. Monitor for DNS/connection errors")

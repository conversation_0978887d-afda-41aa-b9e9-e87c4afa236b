"""
Test file downloads to verify binary safety and MIME type correctness
"""
import os
import sys
import tempfile
import zipfile
from pathlib import Path

# Add backend to path
sys.path.insert(0, os.path.dirname(__file__))

from services.download_service import DownloadService
from services.file_conversion_service import FileConversionService


def test_excel_file_generation():
    """Test that generated Excel files are valid binary files"""
    print("\n=== Testing Excel File Generation ===")
    
    download_service = DownloadService()
    
    # Create test messages
    messages = [
        {'timestamp': '2024-01-01 10:00:00', 'role': 'user', 'content': 'Hello, how are you?'},
        {'timestamp': '2024-01-01 10:00:05', 'role': 'assistant', 'content': 'I am doing well, thank you!'},
        {'timestamp': '2024-01-01 10:00:10', 'role': 'user', 'content': 'Can you help me with Python?'},
        {'timestamp': '2024-01-01 10:00:15', 'role': 'assistant', 'content': 'Of course! I can help with Python.'},
    ]
    
    user_info = {'username': 'testuser', 'email': '<EMAIL>'}
    
    # Generate Excel file
    excel_path = download_service.generate_conversation_excel(messages, "Test Conversation", user_info)
    
    # Verify file exists
    assert os.path.exists(excel_path), f"Excel file not created at {excel_path}"
    
    # Verify file size
    file_size = os.path.getsize(excel_path)
    assert file_size > 0, f"Excel file is empty (size: {file_size})"
    print(f"✓ Excel file created successfully: {file_size} bytes")
    
    # Verify it's a valid ZIP (XLSX is a ZIP file)
    try:
        with zipfile.ZipFile(excel_path, 'r') as zip_ref:
            file_list = zip_ref.namelist()
            assert len(file_list) > 0, "XLSX file is empty ZIP"
            print(f"✓ XLSX is valid ZIP with {len(file_list)} files")
            
            # Check for expected XLSX structure
            has_xl = any('xl/' in f for f in file_list)
            assert has_xl, "XLSX missing xl/ directory"
            print("✓ XLSX has correct structure (contains xl/ directory)")
    except zipfile.BadZipFile:
        raise AssertionError("XLSX file is not a valid ZIP file")
    
    # Cleanup
    os.remove(excel_path)
    print("✓ Excel test passed!")


def test_docx_file_generation():
    """Test that generated DOCX files are valid binary files"""
    print("\n=== Testing DOCX File Generation ===")
    
    download_service = DownloadService()
    
    # Create test messages
    messages = [
        {'timestamp': '2024-01-01 10:00:00', 'role': 'user', 'content': 'Hello, how are you?'},
        {'timestamp': '2024-01-01 10:00:05', 'role': 'assistant', 'content': 'I am doing well, thank you!'},
    ]
    
    user_info = {'username': 'testuser', 'email': '<EMAIL>'}
    
    # Generate DOCX file
    docx_path = download_service.generate_conversation_docx(messages, "Test Conversation", user_info)
    
    # Verify file exists
    assert os.path.exists(docx_path), f"DOCX file not created at {docx_path}"
    
    # Verify file size
    file_size = os.path.getsize(docx_path)
    assert file_size > 0, f"DOCX file is empty (size: {file_size})"
    print(f"✓ DOCX file created successfully: {file_size} bytes")
    
    # Verify it's a valid ZIP (DOCX is a ZIP file)
    try:
        with zipfile.ZipFile(docx_path, 'r') as zip_ref:
            file_list = zip_ref.namelist()
            assert len(file_list) > 0, "DOCX file is empty ZIP"
            print(f"✓ DOCX is valid ZIP with {len(file_list)} files")
            
            # Check for expected DOCX structure
            has_word = any('word/' in f for f in file_list)
            assert has_word, "DOCX missing word/ directory"
            print("✓ DOCX has correct structure (contains word/ directory)")
    except zipfile.BadZipFile:
        raise AssertionError("DOCX file is not a valid ZIP file")
    
    # Cleanup
    os.remove(docx_path)
    print("✓ DOCX test passed!")


def test_pdf_file_generation():
    """Test that generated PDF files are valid binary files"""
    print("\n=== Testing PDF File Generation ===")
    
    download_service = DownloadService()
    
    # Create test messages
    messages = [
        {'timestamp': '2024-01-01 10:00:00', 'role': 'user', 'content': 'Hello, how are you?'},
        {'timestamp': '2024-01-01 10:00:05', 'role': 'assistant', 'content': 'I am doing well, thank you!'},
    ]
    
    user_info = {'username': 'testuser', 'email': '<EMAIL>'}
    
    # Generate PDF file
    pdf_path = download_service.generate_conversation_pdf(messages, "Test Conversation", user_info)
    
    # Verify file exists
    assert os.path.exists(pdf_path), f"PDF file not created at {pdf_path}"
    
    # Verify file size
    file_size = os.path.getsize(pdf_path)
    assert file_size > 0, f"PDF file is empty (size: {file_size})"
    print(f"✓ PDF file created successfully: {file_size} bytes")
    
    # Verify it's a valid PDF (starts with %PDF)
    with open(pdf_path, 'rb') as f:
        header = f.read(4)
        assert header == b'%PDF', f"PDF file has invalid header: {header}"
        print("✓ PDF has correct header (%PDF)")
    
    # Cleanup
    os.remove(pdf_path)
    print("✓ PDF test passed!")


def test_mime_types():
    """Test that MIME types are correctly configured"""
    print("\n=== Testing MIME Type Configuration ===")
    
    download_service = DownloadService()
    formats = download_service.get_download_formats()
    
    expected_mimes = {
        'pdf': 'application/pdf',
        'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'csv': 'text/csv',
        'txt': 'text/plain',
        'json': 'application/json'
    }
    
    for format_type, expected_mime in expected_mimes.items():
        assert format_type in formats, f"Format {format_type} not found in formats"
        actual_mime = formats[format_type]['mime_type']
        assert actual_mime == expected_mime, f"MIME type mismatch for {format_type}: {actual_mime} != {expected_mime}"
        print(f"✓ {format_type}: {actual_mime}")
    
    print("✓ MIME type test passed!")


if __name__ == '__main__':
    try:
        test_mime_types()
        test_excel_file_generation()
        test_docx_file_generation()
        test_pdf_file_generation()
        
        print("\n" + "="*50)
        print("✓ All tests passed!")
        print("="*50)
    except Exception as e:
        print(f"\n✗ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


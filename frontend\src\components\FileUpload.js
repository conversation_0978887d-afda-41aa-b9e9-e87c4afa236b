import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useDropzone } from 'react-dropzone';
import { Upload, File, X, AlertCircle, CheckCircle, Image, FileText, FileSpreadsheet, Presentation } from 'lucide-react';

const FileUpload = ({ onFileUpload, isUploading, user, conversationId, fileType = 'all' }) => {
  const [uploadStatus, setUploadStatus] = useState(null);
  const [uploadError, setUploadError] = useState(null);
  const [previewFile, setPreviewFile] = useState(null);
  const [analysisMode, setAnalysisMode] = useState('comprehensive'); // comprehensive, quick, custom
  const [customQuery, setCustomQuery] = useState('');
  const [showAnalysisOptions, setShowAnalysisOptions] = useState(false);

  // Define file types based on category
  const imageTypes = {
    'image/png': ['.png'],
    'image/jpeg': ['.jpg', '.jpeg'],
    'image/gif': ['.gif'],
    'image/webp': ['.webp'],
    'image/svg+xml': ['.svg']
  };

  const documentTypes = {
    'application/pdf': ['.pdf'],
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
    'text/plain': ['.txt'],
    'text/csv': ['.csv'],
    'application/vnd.openxmlformats-officedocument.presentationml.presentation': ['.pptx'],
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
    'application/json': ['.json'],
    'text/html': ['.html', '.htm']
  };

  // Get allowed types based on fileType prop
  const getAllowedTypes = () => {
    switch (fileType) {
      case 'image':
        return imageTypes;
      case 'document':
        return documentTypes;
      default:
        return { ...imageTypes, ...documentTypes };
    }
  };

  const allowedTypes = getAllowedTypes();

  const maxSize = 10 * 1024 * 1024; // 10MB

  const getFileIcon = (file) => {
    const extension = file.name.split('.').pop().toLowerCase();

    if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(extension)) {
      return <Image className="w-5 h-5 text-blue-500" />;
    } else if (['pdf'].includes(extension)) {
      return <FileText className="w-5 h-5 text-red-500" />;
    } else if (['docx', 'doc'].includes(extension)) {
      return <FileText className="w-5 h-5 text-blue-600" />;
    } else if (['xlsx', 'xls', 'csv'].includes(extension)) {
      return <FileSpreadsheet className="w-5 h-5 text-green-600" />;
    } else if (['pptx', 'ppt'].includes(extension)) {
      return <Presentation className="w-5 h-5 text-orange-600" />;
    } else {
      return <File className="w-5 h-5 text-gray-500" />;
    }
  };

  const onDrop = useCallback(async (acceptedFiles, rejectedFiles) => {
    setUploadError(null);
    setUploadStatus(null);

    if (rejectedFiles.length > 0) {
      const rejection = rejectedFiles[0];
      if (rejection.errors.some(e => e.code === 'file-too-large')) {
        setUploadError('File size exceeds 10MB limit');
      } else if (rejection.errors.some(e => e.code === 'file-invalid-type')) {
        const typeMessage = fileType === 'image'
          ? 'Please upload PNG, JPG, JPEG, GIF, WebP, or SVG files.'
          : fileType === 'document'
          ? 'Please upload PDF, DOCX, TXT, CSV, PPTX, XLSX, JSON, or HTML files.'
          : 'Please upload supported image or document files.';
        setUploadError(`File type not supported. ${typeMessage}`);
      } else {
        setUploadError('File upload failed');
      }
      return;
    }

    if (acceptedFiles.length === 0) {
      return;
    }

    const file = acceptedFiles[0];
    setPreviewFile(file);
    setUploadStatus(`Uploading ${file.name}...`);

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('user_id', user.id);
      if (conversationId) {
        formData.append('conversation_id', conversationId);
      }

      // Determine query based on analysis mode
      let query = '';
      let endpoint = '/api/upload';

      switch (analysisMode) {
        case 'comprehensive':
          query = 'Please provide a comprehensive analysis of this file including all details, structure, content, and insights.';
          // Use specialized endpoints for better analysis
          const extension = file.name.split('.').pop().toLowerCase();
          const isImage = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'bmp', 'tiff'].includes(extension);

          if (isImage) {
            endpoint = '/api/analyze/image';
            query = 'Provide comprehensive image analysis including visual description, objects, text extraction, technical details, and contextual insights.';
          } else {
            endpoint = '/api/analyze/file';
            query = 'Provide comprehensive file analysis including content summary, structure, tone, quality assessment, and practical insights.';
          }
          break;

        case 'quick':
          query = 'Please provide a quick summary of this file highlighting the key points.';
          break;

        case 'custom':
          query = customQuery || 'Please analyze this file.';
          break;

        default:
          query = 'Please analyze this document and provide a comprehensive summary.';
      }

      formData.append('query', query);
      formData.append('analysis_mode', analysisMode);

      setUploadStatus(`${analysisMode === 'comprehensive' ? 'Deep analyzing' : 'Processing'} ${file.name}...`);

      const response = await fetch(`http://localhost:5000${endpoint}`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Upload failed');
      }

      const result = await response.json();

      // Enhanced success message based on analysis type
      const analysisType = result.metadata?.analysis_type || 'standard';
      const successMessage = analysisType === 'image_comprehensive'
        ? `🖼️ Image analysis complete for ${file.name}`
        : analysisType === 'file_comprehensive'
        ? `📄 File analysis complete for ${file.name}`
        : `✅ Successfully processed ${file.name}`;

      setUploadStatus(successMessage);

      // Call the parent component's callback with enhanced result
      onFileUpload({
        ...result,
        analysisMode,
        endpoint: endpoint.replace('/api/', ''),
        processingTime: Date.now()
      }, file);

      // Clear status after 4 seconds for comprehensive analysis
      setTimeout(() => {
        setUploadStatus(null);
        setPreviewFile(null);
        setShowAnalysisOptions(false);
      }, analysisMode === 'comprehensive' ? 4000 : 3000);

    } catch (error) {
      console.error('Upload error:', error);
      setUploadError(error.message || 'Upload failed');
      setUploadStatus(null);
    }
  }, [user, conversationId, onFileUpload]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: allowedTypes,
    maxSize,
    multiple: false,
    disabled: isUploading
  });

  return (
    <div className="relative">
      {/* File Preview */}
      <AnimatePresence>
        {previewFile && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-xl flex items-center gap-3"
          >
            {getFileIcon(previewFile)}
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-800">{previewFile.name}</p>
              <p className="text-xs text-gray-500">
                {(previewFile.size / 1024 / 1024).toFixed(2)} MB
              </p>
            </div>
            <button
              onClick={() => setPreviewFile(null)}
              className="text-gray-400 hover:text-gray-600"
            >
              <X size={16} />
            </button>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Analysis Options */}
      <AnimatePresence>
        {previewFile && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mb-4 p-4 bg-gray-50 border border-gray-200 rounded-xl"
          >
            <div className="flex items-center justify-between mb-3">
              <h4 className="text-sm font-medium text-gray-800">Analysis Options</h4>
              <button
                onClick={() => setShowAnalysisOptions(!showAnalysisOptions)}
                className="text-xs text-blue-600 hover:text-blue-800"
              >
                {showAnalysisOptions ? 'Hide Options' : 'Show Options'}
              </button>
            </div>

            {showAnalysisOptions && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="space-y-3"
              >
                {/* Analysis Mode Selection */}
                <div>
                  <label className="text-xs font-medium text-gray-700 mb-2 block">Analysis Type</label>
                  <div className="grid grid-cols-3 gap-2">
                    <button
                      onClick={() => setAnalysisMode('comprehensive')}
                      className={`p-2 text-xs rounded-lg border transition-all ${
                        analysisMode === 'comprehensive'
                          ? 'bg-blue-100 border-blue-300 text-blue-800'
                          : 'bg-white border-gray-200 text-gray-600 hover:bg-gray-50'
                      }`}
                    >
                      🔍 Comprehensive
                    </button>
                    <button
                      onClick={() => setAnalysisMode('quick')}
                      className={`p-2 text-xs rounded-lg border transition-all ${
                        analysisMode === 'quick'
                          ? 'bg-green-100 border-green-300 text-green-800'
                          : 'bg-white border-gray-200 text-gray-600 hover:bg-gray-50'
                      }`}
                    >
                      ⚡ Quick
                    </button>
                    <button
                      onClick={() => setAnalysisMode('custom')}
                      className={`p-2 text-xs rounded-lg border transition-all ${
                        analysisMode === 'custom'
                          ? 'bg-purple-100 border-purple-300 text-purple-800'
                          : 'bg-white border-gray-200 text-gray-600 hover:bg-gray-50'
                      }`}
                    >
                      ✏️ Custom
                    </button>
                  </div>
                </div>

                {/* Custom Query Input */}
                {analysisMode === 'custom' && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                  >
                    <label className="text-xs font-medium text-gray-700 mb-1 block">Custom Analysis Request</label>
                    <textarea
                      value={customQuery}
                      onChange={(e) => setCustomQuery(e.target.value)}
                      placeholder="Describe what you want to know about this file..."
                      className="w-full p-2 text-xs border border-gray-200 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      rows={2}
                    />
                  </motion.div>
                )}

                {/* Analysis Mode Description */}
                <div className="text-xs text-gray-600 bg-white p-2 rounded border">
                  {analysisMode === 'comprehensive' && (
                    <span>🔍 <strong>Comprehensive:</strong> Deep analysis with detailed insights, structure, content, and technical information.</span>
                  )}
                  {analysisMode === 'quick' && (
                    <span>⚡ <strong>Quick:</strong> Fast summary highlighting key points and main content.</span>
                  )}
                  {analysisMode === 'custom' && (
                    <span>✏️ <strong>Custom:</strong> Analysis based on your specific requirements and questions.</span>
                  )}
                </div>
              </motion.div>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      <motion.div
        whileHover={{ scale: 1.01 }}
        whileTap={{ scale: 0.99 }}
        {...getRootProps()}
        className={`
          border-2 border-dashed rounded-xl p-8 text-center cursor-pointer transition-all duration-300
          ${isDragActive
            ? 'border-blue-500 bg-blue-50 shadow-lg'
            : 'border-gray-300 hover:border-blue-400 hover:bg-gray-50'
          }
          ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}
        `}
      >
        <input {...getInputProps()} />

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="flex flex-col items-center gap-4"
        >
          <motion.div
            animate={isDragActive ? { scale: 1.1 } : { scale: 1 }}
            transition={{ duration: 0.2 }}
          >
            <Upload
              size={40}
              className={`${isDragActive ? 'text-blue-500' : 'text-gray-400'} transition-colors`}
            />
          </motion.div>

          {isDragActive ? (
            <motion.p
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-blue-600 font-medium text-lg"
            >
              Drop the file here...
            </motion.p>
          ) : (
            <div>
              <p className="text-gray-700 font-medium mb-2 text-lg">
                Click to upload or drag and drop
              </p>
              <p className="text-sm text-gray-500">
                {fileType === 'image'
                  ? 'PNG, JPG, JPEG, GIF, WebP, SVG (max 10MB)'
                  : fileType === 'document'
                  ? 'PDF, DOCX, TXT, CSV, PPTX, XLSX, JSON, HTML (max 10MB)'
                  : 'Images and Documents (max 10MB)'
                }
              </p>
            </div>
          )}
        </motion.div>
      </motion.div>

      {/* Enhanced Status Messages */}
      <AnimatePresence>
        {uploadStatus && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-xl flex items-center gap-3 shadow-sm"
          >
            <CheckCircle size={20} className="text-blue-600" />
            <span className="text-sm text-blue-800 font-medium">{uploadStatus}</span>
          </motion.div>
        )}

        {uploadError && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="mt-4 p-4 bg-red-50 border border-red-200 rounded-xl flex items-center gap-3 shadow-sm"
          >
            <AlertCircle size={20} className="text-red-600" />
            <span className="text-sm text-red-800 font-medium flex-1">{uploadError}</span>
            <button
              onClick={() => setUploadError(null)}
              className="text-red-600 hover:text-red-800 transition-colors"
            >
              <X size={16} />
            </button>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

// Enhanced file upload button with modal for the chat interface
export const FileUploadButton = ({ onFileUpload, isUploading, user, conversationId }) => {
  const [showModal, setShowModal] = useState(false);
  const [selectedType, setSelectedType] = useState(null);

  const handleFileUpload = (result, file) => {
    onFileUpload(result, file);
    setShowModal(false);
    setSelectedType(null);
  };

  const handleTypeSelection = (type) => {
    setSelectedType(type);
  };

  const handleBackToMenu = () => {
    setSelectedType(null);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setSelectedType(null);
  };

  // Close modal when clicking outside
  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      handleCloseModal();
    }
  };

  return (
    <>
      <button
        onClick={() => setShowModal(true)}
        disabled={isUploading}
        className="p-2 text-gray-500 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-colors disabled:opacity-50"
        title="Upload file"
      >
        <Upload size={20} />
      </button>

      {/* Modal Overlay */}
      {showModal && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
          onClick={handleOverlayClick}
        >
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-800">
                {selectedType ? `Upload ${selectedType === 'image' ? 'Image' : 'Document'}` : 'Upload File'}
              </h3>
              <button
                onClick={handleCloseModal}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X size={20} />
              </button>
            </div>

            {/* Modal Content */}
            <div className="p-6">
              {!selectedType ? (
                // File type selection menu
                <div className="space-y-4">
                  <p className="text-gray-600 mb-6">Choose the type of file you want to upload:</p>

                  <button
                    onClick={() => handleTypeSelection('image')}
                    className="w-full p-4 border-2 border-gray-200 rounded-lg hover:border-primary-400 hover:bg-primary-50 transition-all duration-200 flex items-center gap-4"
                  >
                    <div className="p-3 bg-blue-100 rounded-lg">
                      <Image size={24} className="text-blue-600" />
                    </div>
                    <div className="text-left">
                      <h4 className="font-medium text-gray-800">Upload Image</h4>
                      <p className="text-sm text-gray-500">PNG, JPG, JPEG, GIF, WebP, SVG</p>
                      <p className="text-xs text-blue-600 mt-1">🔍 Deep visual analysis with OCR & insights</p>
                    </div>
                  </button>

                  <button
                    onClick={() => handleTypeSelection('document')}
                    className="w-full p-4 border-2 border-gray-200 rounded-lg hover:border-primary-400 hover:bg-primary-50 transition-all duration-200 flex items-center gap-4"
                  >
                    <div className="p-3 bg-green-100 rounded-lg">
                      <FileText size={24} className="text-green-600" />
                    </div>
                    <div className="text-left">
                      <h4 className="font-medium text-gray-800">Upload Document</h4>
                      <p className="text-sm text-gray-500">PDF, DOCX, TXT, CSV, PPTX, XLSX, JSON, HTML</p>
                      <p className="text-xs text-green-600 mt-1">📄 Comprehensive content & structure analysis</p>
                    </div>
                  </button>
                </div>
              ) : (
                // File upload interface
                <div>
                  <div className="mb-4">
                    <button
                      onClick={handleBackToMenu}
                      className="text-primary-600 hover:text-primary-700 text-sm font-medium flex items-center gap-1"
                    >
                      ← Back to menu
                    </button>
                  </div>

                  <FileUpload
                    onFileUpload={handleFileUpload}
                    isUploading={isUploading}
                    user={user}
                    conversationId={conversationId}
                    fileType={selectedType}
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default FileUpload;

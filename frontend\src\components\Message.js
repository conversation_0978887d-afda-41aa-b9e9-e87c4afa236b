import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { User, Download, FileText, Copy, ThumbsUp, ThumbsDown, Volume2, VolumeX, Globe, TrendingUp, Cloud, Calendar, ExternalLink } from 'lucide-react';

const Message = ({ message, showDownload, downloadUrl }) => {
  const isUser = message.role === 'user';

  // State for interactive options
  const [isLiked, setIsLiked] = useState(false);
  const [isDisliked, setIsDisliked] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [showCopiedToast, setShowCopiedToast] = useState(false);
  const [ttsStatus, setTtsStatus] = useState(''); // For showing TTS status/errors
  const [showTtsToast, setShowTtsToast] = useState(false);
  const speechRef = useRef(null);

  // Cleanup speech synthesis on unmount
  useEffect(() => {
    return () => {
      if (isSpeaking) {
        window.speechSynthesis.cancel();
      }
    };
  }, [isSpeaking]);

  // Enhanced live data detection and parsing
  const detectLiveDataType = (content) => {
    // Add null/undefined check to prevent toLowerCase() error
    if (!content || typeof content !== 'string') {
      return null;
    }

    const contentLower = content.toLowerCase();

    // Check for structured data patterns
    if (content.includes('📰') || content.includes('News Update') || contentLower.includes('breaking news')) {
      return 'news';
    }
    if (content.includes('🌤️') || content.includes('Weather') || contentLower.includes('temperature') || contentLower.includes('forecast')) {
      return 'weather';
    }
    if (content.includes('💹') || content.includes('Stock') || contentLower.includes('market price') || contentLower.includes('₹')) {
      return 'stock';
    }
    if (content.includes('🏏') || content.includes('Cricket') || contentLower.includes('match') || contentLower.includes('score')) {
      return 'cricket';
    }

    return null;
  };

  const parseLiveDataFromContent = (content, dataType) => {
    try {
      // Add null/undefined check to prevent split() error
      if (!content || typeof content !== 'string') {
        return null;
      }

      // Try to extract structured data from the content
      const lines = content.split('\n');
      const data = {};

      switch (dataType) {
        case 'news':
          return parseNewsData(lines);
        case 'weather':
          return parseWeatherData(lines);
        case 'stock':
          return parseStockData(lines);
        case 'cricket':
          return parseCricketData(lines);
        default:
          return null;
      }
    } catch (error) {
      console.warn('Failed to parse live data:', error);
      return null;
    }
  };

  const parseNewsData = (lines) => {
    const articles = [];
    let currentArticle = {};

    lines.forEach(line => {
      if (line.includes('📰') || line.includes('Title:')) {
        if (currentArticle.title) {
          articles.push(currentArticle);
          currentArticle = {};
        }
        currentArticle.title = line.replace(/📰|Title:/g, '').trim();
      } else if (line.includes('Source:')) {
        currentArticle.source = line.replace('Source:', '').trim();
      } else if (line.includes('Published:')) {
        currentArticle.publishedAt = line.replace('Published:', '').trim();
      } else if (line.includes('Description:')) {
        currentArticle.description = line.replace('Description:', '').trim();
      } else if (line.includes('URL:')) {
        currentArticle.url = line.replace('URL:', '').trim();
      }
    });

    if (currentArticle.title) {
      articles.push(currentArticle);
    }

    return articles.length > 0 ? { articles } : null;
  };

  const parseWeatherData = (lines) => {
    const weather = {};

    lines.forEach(line => {
      if (line.includes('🌤️') || line.includes('Weather for')) {
        weather.city = line.replace(/🌤️|Weather for|:/g, '').trim();
      } else if (line.includes('Temperature:')) {
        weather.temperature = line.replace('Temperature:', '').trim();
      } else if (line.includes('Condition:')) {
        weather.condition = line.replace('Condition:', '').trim();
      } else if (line.includes('Humidity:')) {
        weather.humidity = line.replace('Humidity:', '').trim();
      } else if (line.includes('Wind:')) {
        weather.wind = line.replace('Wind:', '').trim();
      }
    });

    return Object.keys(weather).length > 0 ? weather : null;
  };

  const parseStockData = (lines) => {
    const stock = {};

    lines.forEach(line => {
      if (line.includes('💹') || line.includes('Stock:')) {
        stock.symbol = line.replace(/💹|Stock:|Price/g, '').trim();
      } else if (line.includes('Current Price:') || line.includes('₹')) {
        const priceMatch = line.match(/₹([\d,]+\.?\d*)/);
        if (priceMatch) {
          stock.price = priceMatch[1];
        }
      } else if (line.includes('Change:')) {
        stock.change = line.replace('Change:', '').trim();
      } else if (line.includes('Market Cap:')) {
        stock.marketCap = line.replace('Market Cap:', '').trim();
      }
    });

    return Object.keys(stock).length > 0 ? stock : null;
  };

  const parseCricketData = (lines) => {
    const cricket = {};

    lines.forEach(line => {
      if (line.includes('🏏') || line.includes('Match:')) {
        cricket.match = line.replace(/🏏|Match:/g, '').trim();
      } else if (line.includes('Score:')) {
        cricket.score = line.replace('Score:', '').trim();
      } else if (line.includes('Status:')) {
        cricket.status = line.replace('Status:', '').trim();
      } else if (line.includes('Overs:')) {
        cricket.overs = line.replace('Overs:', '').trim();
      }
    });

    return Object.keys(cricket).length > 0 ? cricket : null;
  };

  // Interactive option handlers
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(message.content);
      setShowCopiedToast(true);
      setTimeout(() => setShowCopiedToast(false), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  const handleLike = () => {
    setIsLiked(!isLiked);
    if (isDisliked) setIsDisliked(false); // Can't like and dislike at the same time
  };

  const handleDislike = () => {
    setIsDisliked(!isDisliked);
    if (isLiked) setIsLiked(false); // Can't like and dislike at the same time
  };

  const showTtsMessage = (message, isError = false) => {
    setTtsStatus(message);
    setShowTtsToast(true);
    setTimeout(() => setShowTtsToast(false), 3000);
  };

  const handleReadAloud = async () => {
    if (isSpeaking) {
      // Stop speaking
      window.speechSynthesis.cancel();
      if (speechRef.current && speechRef.current.pause) {
        speechRef.current.pause();
      }
      setIsSpeaking(false);
      showTtsMessage('Speech stopped');
    } else {
      try {
        setIsSpeaking(true);
        showTtsMessage('Detecting language...');

        // First, detect the language of the text
        const languageInfo = await detectLanguage(message.content);

        // Show enhanced status message with confidence and method
        const confidenceText = languageInfo.confidence ? ` (${Math.round(languageInfo.confidence * 100)}% confidence)` : '';
        const methodText = languageInfo.detection_method ? ` via ${languageInfo.detection_method}` : '';

        if (languageInfo.is_indian_language) {
          showTtsMessage(`🇮🇳 Speaking in ${languageInfo.language_name}${confidenceText}...`);
        } else {
          showTtsMessage(`🌐 Speaking in ${languageInfo.language_name}${confidenceText}...`);
        }

        // Try Web Speech API first for supported languages
        const webSpeechSuccess = await tryWebSpeechAPI(message.content, languageInfo);

        if (!webSpeechSuccess) {
          showTtsMessage(`Using enhanced TTS for ${languageInfo.language_name}...`);
          // Fallback to backend TTS for unsupported languages
          const backendSuccess = await tryBackendTTS(message.content, languageInfo);

          if (!backendSuccess) {
            showTtsMessage('Text-to-speech is not available for this language', true);
            setIsSpeaking(false);
          }
        }

      } catch (error) {
        console.error('TTS Error:', error);
        showTtsMessage('Failed to read text aloud', true);
        setIsSpeaking(false);
      }
    }
  };

  const detectLanguage = async (text) => {
    try {
      // First try the enhanced language detection service
      const response = await fetch('/api/languages/detect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text })
      });

      if (response.ok) {
        const result = await response.json();

        // Map the enhanced language detection result to TTS format
        const languageMapping = {
          'hi': { name: 'हिंदी (Hindi)', is_indian: true },
          'ta': { name: 'தமிழ் (Tamil)', is_indian: true },
          'te': { name: 'తెలుగు (Telugu)', is_indian: true },
          'ml': { name: 'മലയാളം (Malayalam)', is_indian: true },
          'kn': { name: 'ಕನ್ನಡ (Kannada)', is_indian: true },
          'bn': { name: 'বাংলা (Bengali)', is_indian: true },
          'gu': { name: 'ગુજરાતી (Gujarati)', is_indian: true },
          'mr': { name: 'मराठी (Marathi)', is_indian: true },
          'pa': { name: 'ਪੰਜਾਬੀ (Punjabi)', is_indian: true },
          'or': { name: 'ଓଡ଼ିଆ (Odia)', is_indian: true },
          'as': { name: 'অসমীয়া (Assamese)', is_indian: true },
          'ur': { name: 'اردو (Urdu)', is_indian: true },
          'en': { name: 'English', is_indian: false },
          'ar': { name: 'العربية (Arabic)', is_indian: false },
          'fa': { name: 'فارسی (Persian)', is_indian: false },
          'zh': { name: '中文 (Chinese)', is_indian: false },
          'ja': { name: '日本語 (Japanese)', is_indian: false },
          'ko': { name: '한국어 (Korean)', is_indian: false },
          'es': { name: 'Español (Spanish)', is_indian: false },
          'fr': { name: 'Français (French)', is_indian: false },
          'de': { name: 'Deutsch (German)', is_indian: false },
          'it': { name: 'Italiano (Italian)', is_indian: false },
          'pt': { name: 'Português (Portuguese)', is_indian: false },
          'ru': { name: 'Русский (Russian)', is_indian: false }
        };

        const langInfo = languageMapping[result.detected_language] || { name: 'Unknown', is_indian: false };

        return {
          detected_language: result.detected_language,
          language_name: langInfo.name,
          is_supported: true,
          is_indian_language: langInfo.is_indian,
          can_generate_tts: true,
          confidence: result.confidence || 0.8,
          detection_method: result.detection_method || 'enhanced'
        };
      } else {
        console.warn('Enhanced language detection service unavailable, trying TTS service');
      }
    } catch (error) {
      console.error('Enhanced language detection failed:', error);
    }

    // Fallback to TTS language detection service
    try {
      const response = await fetch('/api/tts/detect-language', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text })
      });

      if (response.ok) {
        const result = await response.json();
        return {
          ...result,
          detection_method: 'tts_service'
        };
      } else {
        console.warn('TTS language detection service unavailable, using fallback');
      }
    } catch (error) {
      console.error('TTS language detection failed:', error);

      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        showTtsMessage('Cannot connect to language detection service');
      }
    }

    // Enhanced fallback with better Unicode detection
    const scriptPatterns = {
      'hi': /[\u0900-\u097F]/g,        // Devanagari (Hindi, Marathi, Sanskrit)
      'ta': /[\u0B80-\u0BFF]/g,        // Tamil
      'te': /[\u0C00-\u0C7F]/g,        // Telugu
      'ml': /[\u0D00-\u0D7F]/g,        // Malayalam
      'kn': /[\u0C80-\u0CFF]/g,        // Kannada
      'bn': /[\u0980-\u09FF]/g,        // Bengali
      'gu': /[\u0A80-\u0AFF]/g,        // Gujarati
      'mr': /[\u0900-\u097F]/g,        // Marathi (Devanagari)
      'pa': /[\u0A00-\u0A7F]/g,        // Punjabi (Gurmukhi)
      'or': /[\u0B00-\u0B7F]/g,        // Odia
      'ar': /[\u0600-\u06FF]/g,        // Arabic
      'fa': /[\u0600-\u06FF]/g,        // Persian (uses Arabic script)
      'zh': /[\u4e00-\u9fff]/g,        // Chinese
      'ja': /[\u3040-\u309f\u30a0-\u30ff\u4e00-\u9fff]/g, // Japanese
      'ko': /[\uac00-\ud7af]/g         // Korean
    };

    let detectedLang = 'en';
    let detectedName = 'English';
    let isIndian = false;

    // Check for script patterns
    for (const [lang, pattern] of Object.entries(scriptPatterns)) {
      if (pattern.test(text)) {
        detectedLang = lang;
        const langMapping = {
          'hi': { name: 'हिंदी (Hindi)', is_indian: true },
          'ta': { name: 'தமிழ் (Tamil)', is_indian: true },
          'te': { name: 'తెలుగు (Telugu)', is_indian: true },
          'ml': { name: 'മലയാളം (Malayalam)', is_indian: true },
          'kn': { name: 'ಕನ್ನಡ (Kannada)', is_indian: true },
          'bn': { name: 'বাংলা (Bengali)', is_indian: true },
          'gu': { name: 'ગુજરાતી (Gujarati)', is_indian: true },
          'mr': { name: 'मराठी (Marathi)', is_indian: true },
          'pa': { name: 'ਪੰਜਾਬੀ (Punjabi)', is_indian: true },
          'or': { name: 'ଓଡ଼ିଆ (Odia)', is_indian: true },
          'ar': { name: 'العربية (Arabic)', is_indian: false },
          'fa': { name: 'فارسی (Persian)', is_indian: false },
          'zh': { name: '中文 (Chinese)', is_indian: false },
          'ja': { name: '日本語 (Japanese)', is_indian: false },
          'ko': { name: '한국어 (Korean)', is_indian: false }
        };

        const langInfo = langMapping[lang] || { name: 'Unknown', is_indian: false };
        detectedName = langInfo.name;
        isIndian = langInfo.is_indian;
        break;
      }
    }

    return {
      detected_language: detectedLang,
      language_name: detectedName,
      is_supported: true,
      is_indian_language: isIndian,
      can_generate_tts: true,
      confidence: 0.6,
      detection_method: 'fallback_unicode'
    };
  };

  const tryWebSpeechAPI = async (text, languageInfo) => {
    return new Promise((resolve) => {
      try {
        const utterance = new SpeechSynthesisUtterance(text);

        // Set language for the utterance
        utterance.lang = getWebSpeechLanguageCode(languageInfo.detected_language);

        // Enhanced voice selection logic
        const setVoice = () => {
          const voices = window.speechSynthesis.getVoices();
          console.log(`Available voices: ${voices.length}, Target language: ${utterance.lang}`);

          // Priority 1: Exact language match with quality indicators
          let selectedVoice = voices.find(voice =>
            voice.lang === utterance.lang && (
              voice.name.includes('Natural') ||
              voice.name.includes('Enhanced') ||
              voice.name.includes('Premium') ||
              voice.name.includes('Neural') ||
              voice.name.includes('Wavenet') ||
              !voice.localService
            )
          );

          // Priority 2: Exact language match (any voice)
          if (!selectedVoice) {
            selectedVoice = voices.find(voice => voice.lang === utterance.lang);
          }

          // Priority 3: Language family match (e.g., hi-IN matches hi-*)
          if (!selectedVoice) {
            const langPrefix = utterance.lang.split('-')[0];
            selectedVoice = voices.find(voice =>
              voice.lang.startsWith(langPrefix) && (
                voice.name.includes('Natural') ||
                voice.name.includes('Enhanced') ||
                voice.name.includes('Premium') ||
                voice.name.includes('Neural') ||
                !voice.localService
              )
            );
          }

          // Priority 4: Any voice in the language family
          if (!selectedVoice) {
            const langPrefix = utterance.lang.split('-')[0];
            selectedVoice = voices.find(voice => voice.lang.startsWith(langPrefix));
          }

          // Priority 5: High-quality English voice as fallback
          if (!selectedVoice) {
            selectedVoice = voices.find(voice =>
              voice.lang.startsWith('en') && (
                voice.name.includes('Natural') ||
                voice.name.includes('Enhanced') ||
                voice.name.includes('Premium') ||
                voice.name.includes('Neural') ||
                !voice.localService
              )
            );
          }

          // Priority 6: Any English voice
          if (!selectedVoice) {
            selectedVoice = voices.find(voice => voice.lang.startsWith('en'));
          }

          // Priority 7: Any available voice
          if (!selectedVoice && voices.length > 0) {
            selectedVoice = voices[0];
          }

          if (selectedVoice) {
            utterance.voice = selectedVoice;
            console.log(`Selected voice: ${selectedVoice.name} (${selectedVoice.lang})`);
          } else {
            console.warn('No suitable voice found, using default');
          }
        };

        // Set voice immediately if available, or wait for voices to load
        if (window.speechSynthesis.getVoices().length > 0) {
          setVoice();
        } else {
          window.speechSynthesis.onvoiceschanged = setVoice;
        }

        utterance.rate = 0.9;
        utterance.pitch = 1;
        utterance.volume = 1;

        utterance.onstart = () => {
          setIsSpeaking(true);
          resolve(true);
        };

        utterance.onend = () => setIsSpeaking(false);

        utterance.onerror = (event) => {
          console.error('Web Speech API error:', event.error);
          setIsSpeaking(false);
          resolve(false);
        };

        speechRef.current = utterance;
        window.speechSynthesis.speak(utterance);

        // Timeout fallback
        setTimeout(() => {
          if (!isSpeaking) {
            resolve(false);
          }
        }, 1000);

      } catch (error) {
        console.error('Web Speech API failed:', error);
        resolve(false);
      }
    });
  };

  const tryBackendTTS = async (text, languageInfo) => {
    try {
      const response = await fetch('http://localhost:5000/api/tts/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text,
          language_code: languageInfo.detected_language
        })
      });

      if (response.ok) {
        const audioBlob = await response.blob();
        const audioUrl = URL.createObjectURL(audioBlob);
        const audio = new Audio(audioUrl);

        audio.onplay = () => {
          setIsSpeaking(true);
          showTtsMessage(`Playing in ${languageInfo.language_name}`);
        };

        audio.onended = () => {
          setIsSpeaking(false);
          URL.revokeObjectURL(audioUrl);
          showTtsMessage('Speech completed');
        };

        audio.onerror = () => {
          setIsSpeaking(false);
          URL.revokeObjectURL(audioUrl);
          showTtsMessage('Audio playback failed', true);
          return false;
        };

        speechRef.current = audio;
        await audio.play();
        return true;

      } else {
        const errorData = await response.json();
        console.error('Backend TTS failed:', errorData.error);

        if (errorData.error.includes('not supported')) {
          showTtsMessage(`${languageInfo.language_name} is not supported for text-to-speech`, true);
        } else {
          showTtsMessage('Text-to-speech service unavailable', true);
        }

        setIsSpeaking(false);
        return false;
      }

    } catch (error) {
      console.error('Backend TTS request failed:', error);

      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        showTtsMessage('Cannot connect to text-to-speech service', true);
      } else {
        showTtsMessage('Text-to-speech failed', true);
      }

      setIsSpeaking(false);
      return false;
    }
  };

  const getWebSpeechLanguageCode = (detectedLang) => {
    // Enhanced mapping of detected language codes to Web Speech API language codes
    const langMap = {
      // English variants
      'en': 'en-US',
      'en-us': 'en-US',
      'en-gb': 'en-GB',
      'en-au': 'en-AU',
      'en-in': 'en-IN',

      // Indian languages
      'hi': 'hi-IN',
      'bn': 'bn-IN',
      'gu': 'gu-IN',
      'kn': 'kn-IN',
      'ml': 'ml-IN',
      'mr': 'mr-IN',
      'ta': 'ta-IN',
      'te': 'te-IN',
      'ur': 'ur-PK',
      'pa': 'pa-IN',
      'or': 'or-IN',
      'as': 'as-IN',
      'ne': 'ne-NP',
      'si': 'si-LK',

      // European languages
      'fr': 'fr-FR',
      'es': 'es-ES',
      'de': 'de-DE',
      'it': 'it-IT',
      'pt': 'pt-PT',
      'ru': 'ru-RU',
      'nl': 'nl-NL',
      'sv': 'sv-SE',
      'da': 'da-DK',
      'no': 'nb-NO',
      'fi': 'fi-FI',
      'pl': 'pl-PL',
      'cs': 'cs-CZ',
      'sk': 'sk-SK',
      'hu': 'hu-HU',
      'ro': 'ro-RO',
      'bg': 'bg-BG',
      'hr': 'hr-HR',
      'sr': 'sr-RS',
      'sl': 'sl-SI',
      'et': 'et-EE',
      'lv': 'lv-LV',
      'lt': 'lt-LT',
      'el': 'el-GR',

      // Middle Eastern and African languages
      'ar': 'ar-SA',
      'fa': 'fa-IR',
      'he': 'he-IL',
      'tr': 'tr-TR',
      'sw': 'sw-KE',
      'am': 'am-ET',

      // East Asian languages
      'zh': 'zh-CN',
      'zh-cn': 'zh-CN',
      'zh-tw': 'zh-TW',
      'ja': 'ja-JP',
      'ko': 'ko-KR',
      'th': 'th-TH',
      'vi': 'vi-VN',
      'id': 'id-ID',
      'ms': 'ms-MY',
      'tl': 'tl-PH'
    };

    return langMap[detectedLang] || 'en-US';
  };

  const detectConversionIntent = (content) => {
    // Add null/undefined check to prevent toLowerCase() error
    if (!content || typeof content !== 'string') {
      return null;
    }

    const text = content.toLowerCase();
    const conversionKeywords = {
      'docx': ['convert to word', 'convert to docx', 'make it word', 'word document', 'docx format'],
      'pdf': ['convert to pdf', 'make it pdf', 'pdf format', 'pdf document'],
      'excel': ['convert to excel', 'make it excel', 'excel format', 'xlsx format', 'spreadsheet'],
      'csv': ['convert to csv', 'csv format', 'comma separated']
    };

    const downloadKeywords = ['download', 'save', 'export', 'get file'];

    for (const [format, keywords] of Object.entries(conversionKeywords)) {
      if (keywords.some(keyword => text.includes(keyword))) {
        const wantsDownload = downloadKeywords.some(keyword => text.includes(keyword));
        return { format, wantsDownload };
      }
    }

    return null;
  };

  const handleDownload = async () => {
    if (downloadUrl) {
      try {
        // Use programmatic download for all download URLs
        const response = await fetch(downloadUrl, {
          method: 'GET',
          credentials: 'same-origin'
        });

        if (!response.ok) {
          throw new Error(`Download failed: ${response.status} ${response.statusText}`);
        }

        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;

        // Extract filename from Content-Disposition header or create default
        const contentDisposition = response.headers.get('content-disposition');
        let filename = 'download';

        if (contentDisposition) {
          const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
          if (filenameMatch && filenameMatch[1]) {
            filename = filenameMatch[1].replace(/['"]/g, '');
          }
        } else {
          // Fallback: determine filename from URL or content type
          if (downloadUrl.includes('/api/convert_to_excel') || downloadUrl.includes('format=xlsx')) {
            filename = 'download.xlsx';
          } else if (downloadUrl.includes('/api/convert_to_pdf') || downloadUrl.includes('format=pdf')) {
            filename = 'download.pdf';
          } else if (downloadUrl.includes('/api/convert_to_docx') || downloadUrl.includes('format=docx')) {
            filename = 'download.docx';
          } else if (downloadUrl.includes('format=csv')) {
            filename = 'download.csv';
          } else if (downloadUrl.includes('format=txt')) {
            filename = 'download.txt';
          } else if (downloadUrl.includes('format=json')) {
            filename = 'download.json';
          }
        }

        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        console.log(`File downloaded successfully: ${filename}`);
      } catch (error) {
        console.error('Download failed:', error);
        alert(`Download failed: ${error.message}`);
      }
    }
  };

  // Check if message content suggests conversion intent
  const conversionIntent = !isUser ? detectConversionIntent(message.content) : null;
  const shouldShowDownload = showDownload || (conversionIntent && conversionIntent.wantsDownload);

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={`flex gap-4 ${isUser ? 'justify-end' : 'justify-start'} mb-8`}
    >
      {/* AI Avatar */}
      {!isUser && (
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.1, type: "spring", stiffness: 200 }}
          className="flex-shrink-0 w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg"
        >
          <span className="text-white text-lg font-bold">S</span>
        </motion.div>
      )}

      {/* Message Content */}
      <div className={`max-w-[80%] ${isUser ? 'order-first' : ''}`}>
        {/* File Preview for User Messages */}
        {isUser && message.file && (
          <div className="mb-3">
            <div className="bg-gray-50 border border-gray-200 rounded-xl p-3 flex items-center gap-3">
              {message.file.type === 'image' ? (
                <>
                  <img
                    src={message.file.url}
                    alt={message.file.name}
                    className="w-10 h-10 object-cover rounded-lg"
                  />
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-800">{message.file.name}</p>
                    <p className="text-xs text-gray-500">Image</p>
                  </div>
                </>
              ) : (
                <>
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                    <FileText className="w-5 h-5 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-800">{message.file.name}</p>
                    <p className="text-xs text-gray-500">Document</p>
                  </div>
                </>
              )}
            </div>
          </div>
        )}

        {/* Enhanced Message Bubble */}
        <motion.div
          initial={{ scale: 0.95 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2 }}
          className={`px-6 py-4 rounded-2xl shadow-sm ${
            isUser
              ? 'bg-blue-600 text-white ml-auto max-w-[80%]'
              : 'bg-white text-gray-800 border border-gray-200 shadow-md hover:shadow-lg transition-shadow duration-200'
          }`}
        >
          <div className="text-sm leading-relaxed whitespace-pre-wrap">
            {message.content}
          </div>

          {/* Enhanced Live Data Display */}
          {!isUser && (() => {
            const liveDataType = detectLiveDataType(message.content);
            const liveData = liveDataType ? parseLiveDataFromContent(message.content, liveDataType) : null;

            if (liveData && liveDataType === 'news' && liveData.articles) {
              return (
                <div className="mt-4 space-y-3">
                  <div className="flex items-center gap-2 text-xs font-medium text-blue-600">
                    <Globe size={14} />
                    <span>Live News Updates</span>
                  </div>
                  {liveData.articles.slice(0, 3).map((article, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="p-3 bg-blue-50 border border-blue-200 rounded-lg"
                    >
                      <h4 className="font-medium text-gray-800 text-sm mb-1">{article.title}</h4>
                      {article.description && (
                        <p className="text-xs text-gray-600 mb-2">{article.description}</p>
                      )}
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span>{article.source}</span>
                        {article.url && (
                          <a
                            href={article.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center gap-1 text-blue-600 hover:text-blue-800"
                          >
                            <ExternalLink size={10} />
                            Read more
                          </a>
                        )}
                      </div>
                    </motion.div>
                  ))}
                </div>
              );
            }

            if (liveData && liveDataType === 'weather') {
              return (
                <div className="mt-4">
                  <div className="flex items-center gap-2 text-xs font-medium text-blue-600 mb-3">
                    <Cloud size={14} />
                    <span>Weather Information</span>
                  </div>
                  <div className="p-4 bg-gradient-to-br from-blue-50 to-cyan-50 border border-blue-200 rounded-lg">
                    <div className="grid grid-cols-2 gap-3 text-sm">
                      {liveData.city && (
                        <div>
                          <span className="font-medium text-gray-700">Location:</span>
                          <span className="ml-2 text-gray-600">{liveData.city}</span>
                        </div>
                      )}
                      {liveData.temperature && (
                        <div>
                          <span className="font-medium text-gray-700">Temperature:</span>
                          <span className="ml-2 text-gray-600">{liveData.temperature}</span>
                        </div>
                      )}
                      {liveData.condition && (
                        <div>
                          <span className="font-medium text-gray-700">Condition:</span>
                          <span className="ml-2 text-gray-600">{liveData.condition}</span>
                        </div>
                      )}
                      {liveData.humidity && (
                        <div>
                          <span className="font-medium text-gray-700">Humidity:</span>
                          <span className="ml-2 text-gray-600">{liveData.humidity}</span>
                        </div>
                      )}
                      {liveData.wind && (
                        <div className="col-span-2">
                          <span className="font-medium text-gray-700">Wind:</span>
                          <span className="ml-2 text-gray-600">{liveData.wind}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            }

            if (liveData && liveDataType === 'stock') {
              return (
                <div className="mt-4">
                  <div className="flex items-center gap-2 text-xs font-medium text-green-600 mb-3">
                    <TrendingUp size={14} />
                    <span>Stock Information</span>
                  </div>
                  <div className="p-4 bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200 rounded-lg">
                    <div className="space-y-2 text-sm">
                      {liveData.symbol && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Symbol:</span>
                          <span className="text-gray-600">{liveData.symbol}</span>
                        </div>
                      )}
                      {liveData.price && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Price:</span>
                          <span className="text-gray-600 font-mono">₹{liveData.price}</span>
                        </div>
                      )}
                      {liveData.change && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Change:</span>
                          <span className={`font-mono ${liveData.change.includes('+') ? 'text-green-600' : 'text-red-600'}`}>
                            {liveData.change}
                          </span>
                        </div>
                      )}
                      {liveData.marketCap && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Market Cap:</span>
                          <span className="text-gray-600">{liveData.marketCap}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            }

            if (liveData && liveDataType === 'cricket') {
              return (
                <div className="mt-4">
                  <div className="flex items-center gap-2 text-xs font-medium text-orange-600 mb-3">
                    <Calendar size={14} />
                    <span>Cricket Match</span>
                  </div>
                  <div className="p-4 bg-gradient-to-br from-orange-50 to-yellow-50 border border-orange-200 rounded-lg">
                    <div className="space-y-2 text-sm">
                      {liveData.match && (
                        <div>
                          <span className="font-medium text-gray-700">Match:</span>
                          <span className="ml-2 text-gray-600">{liveData.match}</span>
                        </div>
                      )}
                      {liveData.score && (
                        <div>
                          <span className="font-medium text-gray-700">Score:</span>
                          <span className="ml-2 text-gray-600 font-mono">{liveData.score}</span>
                        </div>
                      )}
                      {liveData.status && (
                        <div>
                          <span className="font-medium text-gray-700">Status:</span>
                          <span className="ml-2 text-gray-600">{liveData.status}</span>
                        </div>
                      )}
                      {liveData.overs && (
                        <div>
                          <span className="font-medium text-gray-700">Overs:</span>
                          <span className="ml-2 text-gray-600 font-mono">{liveData.overs}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            }

            return null;
          })()}

          {/* Enhanced Download Button */}
          {!isUser && shouldShowDownload && downloadUrl && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="mt-4 pt-3 border-t border-gray-200"
            >
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={handleDownload}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white text-sm rounded-xl hover:bg-blue-700 transition-all duration-200 shadow-md hover:shadow-lg"
              >
                <Download className="w-4 h-4" />
                Download
              </motion.button>
            </motion.div>
          )}

          {/* Enhanced Metadata Tags */}
          {!isUser && message.metadata && (
            <div className="flex flex-wrap gap-1 mt-2">
              {message.metadata.used_agent && (
                <span className="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full">
                  🤖 Agent
                </span>
              )}
              {message.metadata.used_rag && (
                <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">
                  📚 Knowledge
                </span>
              )}
              {message.metadata.analysis_type === 'image_comprehensive' && (
                <span className="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full">
                  🖼️ Deep Image Analysis
                </span>
              )}
              {message.metadata.analysis_type === 'file_comprehensive' && (
                <span className="px-2 py-1 bg-orange-100 text-orange-700 text-xs rounded-full">
                  📄 Deep File Analysis
                </span>
              )}
              {message.metadata.analyzer_used && (
                <span className="px-2 py-1 bg-indigo-100 text-indigo-700 text-xs rounded-full">
                  🔍 {message.metadata.analyzer_used}
                </span>
              )}
              {message.metadata.file_info && (
                <span className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full">
                  📎 {message.metadata.file_info.name}
                </span>
              )}
              {message.metadata.has_live_data && (
                <span className="px-2 py-1 bg-red-100 text-red-700 text-xs rounded-full">
                  🔴 Live Data
                </span>
              )}
              {message.metadata.live_data_type && (
                <span className="px-2 py-1 bg-yellow-100 text-yellow-700 text-xs rounded-full">
                  {message.metadata.live_data_type === 'news' && '📰 News'}
                  {message.metadata.live_data_type === 'weather' && '🌤️ Weather'}
                  {message.metadata.live_data_type === 'stock' && '💹 Stocks'}
                  {message.metadata.live_data_type === 'cricket' && '🏏 Cricket'}
                </span>
              )}
              {message.metadata.live_data_source && (
                <span className="px-2 py-1 bg-cyan-100 text-cyan-700 text-xs rounded-full">
                  📡 {message.metadata.live_data_source}
                </span>
              )}
            </div>
          )}
        </motion.div>

        {/* Timestamp */}
        <div className={`text-xs text-gray-500 mt-1 ${isUser ? 'text-right' : 'text-left'}`}>
          {new Date(message.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
        </div>

        {/* Interactive Options for Assistant Messages */}
        {!isUser && (
          <motion.div
            initial={{ opacity: 0, y: 5 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="flex items-center gap-1 mt-2 relative"
          >
            {/* Copy Button */}
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
              onClick={handleCopy}
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-all duration-200 cursor-pointer group relative"
              title="Copy message"
            >
              <Copy className="w-4 h-4" />
            </motion.button>

            {/* Like Button */}
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
              onClick={handleLike}
              className={`p-2 rounded-lg transition-all duration-200 cursor-pointer ${
                isLiked
                  ? 'text-blue-600 bg-blue-50 hover:bg-blue-100'
                  : 'text-gray-500 hover:text-blue-600 hover:bg-gray-100'
              }`}
              title="Like message"
            >
              <ThumbsUp className="w-4 h-4" />
            </motion.button>

            {/* Dislike Button */}
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
              onClick={handleDislike}
              className={`p-2 rounded-lg transition-all duration-200 cursor-pointer ${
                isDisliked
                  ? 'text-red-600 bg-red-50 hover:bg-red-100'
                  : 'text-gray-500 hover:text-red-600 hover:bg-gray-100'
              }`}
              title="Dislike message"
            >
              <ThumbsDown className="w-4 h-4" />
            </motion.button>

            {/* Enhanced Read Aloud Button with Language Indicator */}
            <div className="relative flex items-center">
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleReadAloud}
                className={`p-2 rounded-lg transition-all duration-200 cursor-pointer ${
                  isSpeaking
                    ? 'text-green-600 bg-green-50 hover:bg-green-100'
                    : 'text-gray-500 hover:text-green-600 hover:bg-gray-100'
                }`}
                title={isSpeaking ? "Stop reading" : "Read aloud"}
              >
                {isSpeaking ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
              </motion.button>

              {/* Language indicator when speaking */}
              {isSpeaking && (
                <motion.div
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -10 }}
                  className="ml-2 px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full font-medium"
                >
                  🔊 Speaking
                </motion.div>
              )}
            </div>

            {/* Copied Toast */}
            {showCopiedToast && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8, y: -10 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.8, y: -10 }}
                className="absolute left-0 top-12 bg-gray-800 text-white text-xs px-3 py-1 rounded-lg shadow-lg z-10"
              >
                Copied!
                <div className="absolute -top-1 left-4 w-2 h-2 bg-gray-800 transform rotate-45"></div>
              </motion.div>
            )}

            {/* TTS Status Toast */}
            {showTtsToast && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8, y: -10 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.8, y: -10 }}
                className={`absolute left-0 top-12 text-white text-xs px-3 py-1 rounded-lg shadow-lg z-10 ${
                  ttsStatus.includes('failed') || ttsStatus.includes('not available') || ttsStatus.includes('Cannot connect')
                    ? 'bg-red-600'
                    : 'bg-blue-600'
                }`}
              >
                {ttsStatus}
                <div className={`absolute -top-1 left-4 w-2 h-2 transform rotate-45 ${
                  ttsStatus.includes('failed') || ttsStatus.includes('not available') || ttsStatus.includes('Cannot connect')
                    ? 'bg-red-600'
                    : 'bg-blue-600'
                }`}></div>
              </motion.div>
            )}
          </motion.div>
        )}
      </div>

      {/* Enhanced User Avatar */}
      {isUser && (
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.1, type: "spring", stiffness: 200 }}
          className="flex-shrink-0 w-10 h-10 bg-gray-500 rounded-full flex items-center justify-center shadow-lg"
        >
          <User className="w-5 h-5 text-white" />
        </motion.div>
      )}
    </motion.div>
  );
};

export default Message;
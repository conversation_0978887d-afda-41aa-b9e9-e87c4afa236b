{"version": 3, "file": "static/css/main.54fd126f.css", "mappings": "AAGA,EAGE,qBAAsB,CAFtB,QAAS,CACT,SAEF,CAEA,KAIE,kCAAmC,CACnC,iCAAkC,CAJlC,mIAKF,CAGA,MAAQ,YAAe,CACvB,QAAU,QAAS,CACnB,UAAY,qBAAwB,CACpC,cAAgB,kBAAqB,CACrC,gBAAkB,sBAAyB,CAC3C,iBAAmB,6BAAgC,CACnD,aAAe,wBAA2B,CAC1C,eAAiB,0BAA6B,CAC9C,OAAS,UAAc,CACvB,OAAS,SAAa,CACtB,OAAS,UAAc,CACvB,OAAS,QAAW,CACpB,OAAS,UAAa,CACtB,UAAY,YAAe,CAC3B,QAAU,WAAc,CACxB,MAAQ,aAAgB,CACxB,QAAU,UAAa,CACvB,MAAQ,WAAc,CACtB,MAAQ,YAAe,CACvB,KAAO,WAAe,CACtB,KAAO,YAAgB,CACvB,KAAO,UAAa,CACpB,KAAO,WAAc,CACrB,KAAO,aAAgB,CACvB,KAAO,cAAiB,CACxB,KAAO,YAAe,CACtB,KAAO,aAAgB,CACvB,KAAO,UAAa,CACpB,KAAO,WAAc,CACrB,MAAQ,UAAa,CACrB,MAAQ,WAAc,CACtB,MAAQ,UAAa,CACrB,MAAQ,WAAc,CACtB,UAAY,eAAkB,CAC9B,WAAa,eAAkB,CAC/B,WAAa,eAAkB,CAC/B,WAAa,eAAkB,CAC/B,UAAY,aAAgB,CAC5B,UAAY,aAAgB,CAC5B,WAAa,eAAkB,CAC/B,cAAgB,gBAAmB,CACnC,KAAO,aAAiB,CACxB,KAAO,cAAkB,CACzB,KAAO,YAAe,CACtB,KAAO,cAAiB,CACxB,KAAO,YAAe,CACtB,MAAQ,mBAAqB,CAAE,oBAAwB,CACvD,MAAQ,iBAAkB,CAAE,kBAAqB,CACjD,MAAQ,mBAAoB,CAAE,oBAAuB,CACrD,MAA8B,qBAAuB,CAA7C,kBAA+C,CACvD,MAA6B,oBAAsB,CAA3C,iBAA6C,CACrD,MAA8B,qBAAuB,CAA7C,kBAA+C,CACvD,MAA2B,mBAAoB,CAAvC,gBAAyC,CACjD,MAAQ,kBAAqB,CAC7B,MAAQ,qBAAwB,CAChC,MAAQ,mBAAuB,CAC/B,MAAQ,oBAAwB,CAChC,MAAQ,kBAAqB,CAC7B,MAAQ,oBAAuB,CAC/B,MAAQ,kBAAqB,CAC7B,MAAQ,iBAAqB,CAC7B,MAAQ,gBAAoB,CAC5B,MAAQ,iBAAqB,CAC7B,MAAQ,eAAkB,CAC1B,MAAQ,iBAAoB,CAC5B,MAAQ,eAAkB,CAC1B,SAAW,gBAAmB,CAC9B,MAAQ,mBAAuB,CAC/B,SAAW,gBAAoB,CAC/B,SAAW,iBAAqB,CAChC,SAAW,kBAAqB,CAChC,SAAW,iBAAoB,CAC/B,UAAY,gBAAmB,CAC/B,UAAY,kBAAqB,CACjC,UAAY,cAAiB,CAC7B,UAAY,iBAAoB,CAChC,aAAe,eAAkB,CACjC,eAAiB,eAAkB,CACnC,WAAa,eAAkB,CAC/B,aAAe,iBAAoB,CACnC,YAAc,UAAc,CAC5B,eAAiB,aAAgB,CACjC,eAAiB,aAAgB,CACjC,eAAiB,aAAgB,CACjC,eAAiB,aAAgB,CACjC,cAAgB,aAAgB,CAChC,cAAgB,aAAgB,CAChC,UAAY,qBAAyB,CACrC,YAAc,wBAA2B,CACzC,aAAe,wBAA2B,CAC1C,aAAe,wBAA2B,CAC1C,aAAe,wBAA2B,CAC1C,aAAe,wBAA2B,CAC1C,aAAe,wBAA2B,CAC1C,YAAc,wBAA2B,CACzC,cAAgB,wBAA2B,CAC3C,QAAU,gBAAmB,CAC7B,UAAY,gBAAmB,CAC/B,iBAAmB,oBAAuB,CAC1C,iBAAmB,oBAAuB,CAC1C,UAAY,oBAAuB,CACnC,UAAY,uBAA0B,CACtC,YAAc,mBAAuB,CACrC,YAAc,oBAAwB,CACtC,aAAe,kBAAqB,CACpC,aAAe,oBAAuB,CACtC,cAAgB,oBAAuB,CACvC,WAAa,gCAA6C,CAC1D,WAAa,4DAAmF,CAChG,WAAa,8DAAqF,CAClG,WAAa,gEAAuF,CACpG,YAAc,sCAAmD,CACjE,iBAAmB,eAAkB,CACrC,iBAAmB,eAAkB,CACrC,UAAY,iBAAoB,CAChC,UAAY,iBAAoB,CAChC,SAA6B,QAAS,CAAE,MAAO,CAA5B,OAAQ,CAAhB,KAAsC,CACjD,OAAS,SAAa,CACtB,OAAS,QAAW,CACpB,QAAU,UAAa,CACvB,QAAU,QAAW,CACrB,QAAU,SAAY,CACtB,SAAW,OAAU,CACrB,SAAW,WAAe,CAC1B,UAAY,YAAe,CAC3B,YAAc,gBAAmB,CACjC,WAAa,WAAc,CAC3B,SAAW,WAAc,CACzB,SAAW,SAAY,CACvB,aAAe,WAAc,CAC7B,MAAQ,UAAa,CACrB,MAAQ,UAAa,CACrB,OAAS,cAAiB,CAC1B,OAA4B,mBAAoB,CAAvC,gBAAyC,CAClD,cAAgB,gBAAmB,CACnC,gBAAkB,cAAiB,CACnC,gBAAkB,uBAA2B,CAC7C,mBAAqB,mDAAyD,CAC9E,oBAAsB,2BAA+B,CACrD,cAAgB,uBAA4B,CAC5C,cAAgB,uBAA4B,CAC5C,MAAQ,+BAAkC,CAC1C,WAAa,SAAY,CACzB,aAAe,SAAY,CAC3B,eAAqB,iBAAoB,CACzC,eAAqB,gBAAoB,CACzC,MAAQ,YAAe,CACvB,aAAe,6CAAkD,CACjE,aAAe,6CAAkD,CACjE,aAAe,WAAc,CAC7B,eAAiB,aAAgB,CACjC,UAAY,eAAgB,CAAE,sBAAuB,CAAE,kBAAqB,CAC5E,qBAAuB,oBAAuB,CAC9C,iBAAmB,iBAAoB,CACvC,aAAe,WAAc,CAC7B,2BAA6B,YAAe,CAC5C,qBAAuB,8BAA+C,CACtE,iCAAmC,kBAA2B,CAC9D,0BAA4B,wBAA2B,CACvD,2BAA6B,0BAA4C,CACzE,2BAA6B,sBAA4C,CACzE,6BAA+B,0BAA0C,CACzE,6BAA+B,0BAA0C,CACzE,2BAA6B,aAAgB,CAC7C,wBAA0B,qBAAwB,CAClD,wBAA0B,4DAAmF,CAC7G,wBAA0B,8DAAqF,CAC/G,2CAA6C,kBAAqB,CAClE,6CAA+C,eAAkB,CACjE,uCAAyC,SAAY,CACrD,kBAAoB,iCAA0B,CAA1B,yBAA4B,CAChD,kBAAoB,kCAA2B,CAA3B,0BAA6B,CAGjD,mBAAqB,0EAA8E,CACnG,kBAAoB,mEAAuE,CAC3F,kBAAoB,oEAAwE,CAG5F,eAAiB,0BAA2B,CAAE,2EAA4F,CAC1I,eAAiB,0BAA2B,CAAE,2EAA2F,CAEzI,eAAiB,wBAA2B,CAC5C,YAAc,wBAA2B,CACzC,aAAe,wBAA2B,CAC1C,aAAe,wBAA2B,CAC1C,aAAe,wBAA2B,CAC1C,aAAe,wBAA2B,CAC1C,eAAiB,wBAA2B,CAC5C,eAAiB,wBAA2B,CAC5C,eAAiB,aAAgB,CACjC,eAAiB,aAAgB,CACjC,iBAAmB,aAAgB,CACnC,cAAgB,aAAgB,CAChC,WAAa,wBAA2B,CACxC,yBAA2B,wBAA2B,CACtD,0BAA4B,wBAA2B,CACvD,yBAA2B,wBAA2B,CACtD,4BAA8B,aAAgB,CAC9C,4BAA8B,aAAgB,CAC9C,4CAA8C,oBAAuB,CAGrE,kBAAoB,0BAA2B,CAAE,2EAA6F,CAC9I,kBAAoB,0BAA2B,CAAE,2EAA6F,CAC9I,kBAAoB,0BAA2B,CAAE,2EAA4F,CAC7I,kBAAoB,wBAA2B,CAC/C,kBAAoB,wBAA2B,CAC/C,kBAAoB,wBAA2B,CAC/C,gBAAkB,wBAA2B,CAG7C,gBAAkB,0BAA2B,CAAE,2EAA0F,CACzI,eAAiB,0BAAqC,CAAE,mFAAmG,CAC3J,cAAgB,wBAA2B,CAG3C,cAAgB,4BAA6B,CAAE,oBAAuB,CACtE,kBAAoB,WAAoB,CAGxC,eAAiB,wBAA2B,CAC5C,gBAAkB,wBAA2B,CAC7C,kBAAoB,aAAgB,CACpC,kBAAoB,aAAgB,CACpC,oBAAsB,oBAAuB,CAC7C,oBAAsB,oBAAuB,CAG7C,aAAe,0BAA6C,CAC5D,cAAgB,0BAA4C,CAC5D,cAAgB,sBAA4C,CAC5D,cAAgB,sBAA4C,CAC5D,cAAgB,0BAA6C,CAC7D,kBAAoB,sBAAwC,CAC5D,kBAAoB,kBAAwC,CAC5D,kBAAoB,sBAAwC,CAC5D,gBAAkB,WAAiC,CACnD,gBAAkB,eAAiC,CAGnD,kCAAoC,0BAA6B,CACjE,gCAAkC,wBAA2B,CAC7D,kCAAoC,0BAA6B,CACjE,gCAAkC,wBAA2B,CAG7D,+BAAiC,0BAA6B,CAC9D,+BAAiC,wBAA2B,CAC5D,6BAA+B,wBAA2B,CAC1D,6BAA+B,wBAA2B,CAG1D,+BAAiC,8BAAgD,CACjF,+BAAiC,8BAAgD,CACjF,iCAAmC,oBAAuB,CAG1D,yBACE,iBAAmB,6CAAkD,CACvE,CAGA,sBACE,MAAW,SAAY,CACvB,IAAM,UAAc,CACtB,CAEA,uBACE,MAAW,uBAA0B,CACrC,IAAM,0BAA6B,CACrC,CAEA,gBACE,GAAK,uBAA2B,CAClC,CAGA,oBAAsB,wDAAgE,CACtF,qBAAuB,iCAAoC,CAC3D,gBAAkB,4BAA+B,CACjD,eAAiB,mDAA2D,CAC5E,cAAgB,iCAAoC,CAGpD,qCACE,SACF,CAEA,2CACE,gBACF,CAEA,2CACE,oBAAoC,CACpC,iBACF,CAEA,iDACE,oBACF,CAGA,YACE,0CACF,CAEA,wBACE,kBACF,CAEA,yBACE,mBACF,CAEA,yBACE,mBACF,CAEA,kBACE,UACE,uBACF,CACA,IACE,2BACF,CACF,CAGA,kBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,mBACE,GACE,SAAU,CACV,oBACF,CACA,GACE,SAAU,CACV,kBACF,CACF,CAEA,mBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAGA,gBAEE,aAAc,CADd,cAEF,CAEA,yBACE,gBACE,eACF,CACF,CAEA,0BACE,gBACE,gBACF,CACF,CAGA,SAEE,eAAgB,CADhB,WAAY,CAGZ,6BAAoC,CADpC,oBAEF,CAEA,4BACE,SACF,CAEA,kCACE,gBACF,CAEA,kCACE,wBAAyB,CACzB,iBACF,CAGA,qBACE,MACE,SACF,CACA,IACE,UACF,CACF,CAEA,mBACE,6CACF,CAGA,gBAAkB,6BAAiC,CACnD,iBAAmB,8BAAkC,CACrD,iBAAmB,8BAAkC,CAGrD,kBACE,0BAA2B,CAC3B,6BACF,CAEA,kBACE,6BAA4C,CAC5C,8BACF,CAGA,iBACE,MACE,uBACF,CACA,IACE,2BACF,CACF,CAEA,yBACE,MACE,uBACF,CACA,IACE,2BACF,CACF,CAEA,sBACE,MACE,uBACF,CACA,IACE,2BACF,CACF,CAEA,mBACE,MACE,UAAY,CACZ,kBACF,CACA,IACE,SAAU,CACV,oBACF,CACF,CAEA,2BACE,MACE,UAAY,CACZ,kBACF,CACA,IACE,UAAY,CACZ,oBACF,CACF,CAGA,eACE,uCACF,CAEA,uBACE,+CAAgD,CAChD,kBACF,CAEA,oBACE,6CAA8C,CAC9C,kBACF,CAEA,iBACE,yCACF,CAEA,yBACE,iDAAkD,CAClD,oBACF,CAGA,iBAAmB,0BAA2B,CAAE,2EAA2F,CAC3I,gBAAkB,0BAAsC,CAAE,mFAAoG,CAC9J,eAAiB,wBAA2B,CAC5C,iBAAmB,aAAgB,CACnC,eAAiB,aAAgB,CACjC,eAAiB,0BAA2B,CAAE,2EAA4F,CAC1I,eAAiB,wBAA2B,CAC5C,eAAiB,0BAA2B,CAAE,2EAA6F,CAC3I,eAAiB,wBAA2B,CAC5C,qBAAuB,sCAAuD,CAC9E,qBAAuB,sCAAuD,CAC9E,uBAAyB,sBAAwC,CACjE,iBAAmB,aAAgB,CACnC,QAAU,yCAA4C,CACtD,gBAAkB,yBAA2C,CAC7D,4BAA8B,uBAAwB,CAAE,8BAAgD,CACxG,8BAAgC,oBAAuB", "sources": ["index.css"], "sourcesContent": ["/* Modern CSS Reset and Base Styles */\r\n\r\n/* Custom global styles */\r\n* {\r\n  margin: 0;\r\n  padding: 0;\r\n  box-sizing: border-box;\r\n}\r\n\r\nbody {\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\r\n    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\r\n    sans-serif;\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n}\r\n\r\n/* Utility Classes */\r\n.flex { display: flex; }\r\n.flex-1 { flex: 1; }\r\n.flex-col { flex-direction: column; }\r\n.items-center { align-items: center; }\r\n.justify-center { justify-content: center; }\r\n.justify-between { justify-content: space-between; }\r\n.justify-end { justify-content: flex-end; }\r\n.justify-start { justify-content: flex-start; }\r\n.gap-1 { gap: 0.25rem; }\r\n.gap-2 { gap: 0.5rem; }\r\n.gap-3 { gap: 0.75rem; }\r\n.gap-4 { gap: 1rem; }\r\n.gap-6 { gap: 1.5rem; }\r\n.h-screen { height: 100vh; }\r\n.h-full { height: 100%; }\r\n.h-10 { height: 2.5rem; }\r\n.w-full { width: 100%; }\r\n.w-80 { width: 20rem; }\r\n.w-10 { width: 2.5rem; }\r\n.w-2 { width: 0.5rem; }\r\n.h-2 { height: 0.5rem; }\r\n.w-4 { width: 1rem; }\r\n.h-4 { height: 1rem; }\r\n.w-5 { width: 1.25rem; }\r\n.h-5 { height: 1.25rem; }\r\n.w-6 { width: 1.5rem; }\r\n.h-6 { height: 1.5rem; }\r\n.w-8 { width: 2rem; }\r\n.h-8 { height: 2rem; }\r\n.w-12 { width: 3rem; }\r\n.h-12 { height: 3rem; }\r\n.w-16 { width: 4rem; }\r\n.h-16 { height: 4rem; }\r\n.max-w-md { max-width: 28rem; }\r\n.max-w-3xl { max-width: 48rem; }\r\n.max-w-4xl { max-width: 56rem; }\r\n.max-w-2xl { max-width: 42rem; }\r\n.max-w-70 { max-width: 70%; }\r\n.max-w-80 { max-width: 80%; }\r\n.min-w-160 { min-width: 160px; }\r\n.min-h-screen { min-height: 100vh; }\r\n.p-2 { padding: 0.5rem; }\r\n.p-3 { padding: 0.75rem; }\r\n.p-4 { padding: 1rem; }\r\n.p-6 { padding: 1.5rem; }\r\n.p-8 { padding: 2rem; }\r\n.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }\r\n.px-4 { padding-left: 1rem; padding-right: 1rem; }\r\n.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }\r\n.py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }\r\n.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }\r\n.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }\r\n.py-4 { padding-top: 1rem; padding-bottom: 1rem; }\r\n.pt-6 { padding-top: 1.5rem; }\r\n.pb-6 { padding-bottom: 1.5rem; }\r\n.mb-2 { margin-bottom: 0.5rem; }\r\n.mb-3 { margin-bottom: 0.75rem; }\r\n.mb-4 { margin-bottom: 1rem; }\r\n.mb-6 { margin-bottom: 1.5rem; }\r\n.mb-8 { margin-bottom: 2rem; }\r\n.mt-1 { margin-top: 0.25rem; }\r\n.mt-2 { margin-top: 0.5rem; }\r\n.mt-3 { margin-top: 0.75rem; }\r\n.mt-4 { margin-top: 1rem; }\r\n.mt-6 { margin-top: 1.5rem; }\r\n.mt-8 { margin-top: 2rem; }\r\n.ml-auto { margin-left: auto; }\r\n.mr-3 { margin-right: 0.75rem; }\r\n.text-xs { font-size: 0.75rem; }\r\n.text-sm { font-size: 0.875rem; }\r\n.text-lg { font-size: 1.125rem; }\r\n.text-xl { font-size: 1.25rem; }\r\n.text-2xl { font-size: 1.5rem; }\r\n.text-3xl { font-size: 1.875rem; }\r\n.text-5xl { font-size: 3rem; }\r\n.text-6xl { font-size: 3.75rem; }\r\n.font-medium { font-weight: 500; }\r\n.font-semibold { font-weight: 600; }\r\n.font-bold { font-weight: 700; }\r\n.text-center { text-align: center; }\r\n.text-white { color: white; }\r\n.text-gray-500 { color: #6b7280; }\r\n.text-gray-600 { color: #4b5563; }\r\n.text-gray-700 { color: #374151; }\r\n.text-gray-800 { color: #1f2937; }\r\n.text-red-400 { color: #f87171; }\r\n.text-red-300 { color: #fca5a5; }\r\n.bg-white { background-color: white; }\r\n.bg-gray-50 { background-color: #f9fafb; }\r\n.bg-gray-100 { background-color: #f3f4f6; }\r\n.bg-gray-200 { background-color: #e5e7eb; }\r\n.bg-gray-300 { background-color: #d1d5db; }\r\n.bg-gray-400 { background-color: #9ca3af; }\r\n.bg-gray-800 { background-color: #1f2937; }\r\n.bg-red-500 { background-color: #ef4444; }\r\n.bg-green-500 { background-color: #10b981; }\r\n.border { border-width: 1px; }\r\n.border-2 { border-width: 2px; }\r\n.border-gray-200 { border-color: #e5e7eb; }\r\n.border-gray-300 { border-color: #d1d5db; }\r\n.border-t { border-top-width: 1px; }\r\n.border-b { border-bottom-width: 1px; }\r\n.rounded-lg { border-radius: 0.5rem; }\r\n.rounded-xl { border-radius: 0.75rem; }\r\n.rounded-2xl { border-radius: 1rem; }\r\n.rounded-3xl { border-radius: 1.5rem; }\r\n.rounded-full { border-radius: 9999px; }\r\n.shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }\r\n.shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }\r\n.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }\r\n.shadow-xl { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }\r\n.shadow-2xl { box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); }\r\n.overflow-hidden { overflow: hidden; }\r\n.overflow-y-auto { overflow-y: auto; }\r\n.relative { position: relative; }\r\n.absolute { position: absolute; }\r\n.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }\r\n.top-2 { top: 0.5rem; }\r\n.top-8 { top: 2rem; }\r\n.top-10 { top: 2.5rem; }\r\n.top-20 { top: 5rem; }\r\n.top-60 { top: 15rem; }\r\n.right-0 { right: 0; }\r\n.right-2 { right: 0.5rem; }\r\n.right-10 { right: 2.5rem; }\r\n.right-1\\/3 { right: 33.333333%; }\r\n.bottom-20 { bottom: 5rem; }\r\n.left-10 { left: 2.5rem; }\r\n.left-20 { left: 5rem; }\r\n.bottom-full { bottom: 100%; }\r\n.z-10 { z-index: 10; }\r\n.z-50 { z-index: 50; }\r\n.fixed { position: fixed; }\r\n.py-20 { padding-top: 5rem; padding-bottom: 5rem; }\r\n.object-cover { object-fit: cover; }\r\n.cursor-pointer { cursor: pointer; }\r\n.transition-all { transition: all 0.3s ease; }\r\n.transition-colors { transition: color 0.3s ease, background-color 0.3s ease; }\r\n.transition-opacity { transition: opacity 0.3s ease; }\r\n.duration-200 { transition-duration: 200ms; }\r\n.duration-300 { transition-duration: 300ms; }\r\n.ease { transition-timing-function: ease; }\r\n.opacity-0 { opacity: 0; }\r\n.opacity-100 { opacity: 1; }\r\n.space-y-6 > * + * { margin-top: 1.5rem; }\r\n.space-y-2 > * + * { margin-top: 0.5rem; }\r\n.grid { display: grid; }\r\n.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }\r\n.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }\r\n.order-first { order: -9999; }\r\n.flex-shrink-0 { flex-shrink: 0; }\r\n.truncate { overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }\r\n.whitespace-pre-wrap { white-space: pre-wrap; }\r\n.leading-relaxed { line-height: 1.625; }\r\n.resize-none { resize: none; }\r\n.focus\\:outline-none:focus { outline: none; }\r\n.focus\\:ring-2:focus { box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5); }\r\n.focus\\:border-transparent:focus { border-color: transparent; }\r\n.hover\\:bg-gray-200:hover { background-color: #e5e7eb; }\r\n.hover\\:bg-white\\/10:hover { background-color: rgba(255, 255, 255, 0.1); }\r\n.hover\\:bg-white\\/20:hover { background-color: rgba(255, 255, 255, 0.2); }\r\n.hover\\:bg-red-500\\/20:hover { background-color: rgba(239, 68, 68, 0.2); }\r\n.hover\\:bg-red-500\\/30:hover { background-color: rgba(239, 68, 68, 0.3); }\r\n.hover\\:text-red-300:hover { color: #fca5a5; }\r\n.hover\\:scale-105:hover { transform: scale(1.05); }\r\n.hover\\:shadow-md:hover { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }\r\n.hover\\:shadow-lg:hover { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }\r\n.disabled\\:hover\\:scale-100:disabled:hover { transform: scale(1); }\r\n.disabled\\:hover\\:shadow-none:disabled:hover { box-shadow: none; }\r\n.group:hover .group-hover\\:opacity-100 { opacity: 1; }\r\n.backdrop-blur-sm { backdrop-filter: blur(4px); }\r\n.backdrop-blur-xl { backdrop-filter: blur(24px); }\r\n\r\n/* Gradient Backgrounds */\r\n.bg-gradient-to-br { background-image: linear-gradient(to bottom right, var(--tw-gradient-stops)); }\r\n.bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)); }\r\n.bg-gradient-to-b { background-image: linear-gradient(to bottom, var(--tw-gradient-stops)); }\r\n\r\n/* Gemini-style Colors */\r\n.from-blue-500 { --tw-gradient-from: #3b82f6; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(59, 130, 246, 0)); }\r\n.from-blue-600 { --tw-gradient-from: #2563eb; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(37, 99, 235, 0)); }\r\n.to-purple-600 { --tw-gradient-to: #9333ea; }\r\n.to-purple-700 { --tw-gradient-to: #7c3aed; }\r\n.bg-blue-50 { background-color: #eff6ff; }\r\n.bg-blue-100 { background-color: #dbeafe; }\r\n.bg-blue-500 { background-color: #3b82f6; }\r\n.bg-blue-600 { background-color: #2563eb; }\r\n.bg-blue-700 { background-color: #1d4ed8; }\r\n.bg-purple-100 { background-color: #f3e8ff; }\r\n.bg-purple-600 { background-color: #9333ea; }\r\n.text-blue-600 { color: #2563eb; }\r\n.text-blue-700 { color: #1d4ed8; }\r\n.text-purple-700 { color: #7c3aed; }\r\n.text-red-600 { color: #dc2626; }\r\n.bg-red-50 { background-color: #fef2f2; }\r\n.hover\\:bg-blue-50:hover { background-color: #eff6ff; }\r\n.hover\\:bg-blue-700:hover { background-color: #1d4ed8; }\r\n.hover\\:bg-gray-50:hover { background-color: #f9fafb; }\r\n.hover\\:text-blue-600:hover { color: #2563eb; }\r\n.hover\\:text-gray-700:hover { color: #374151; }\r\n.focus-within\\:border-blue-500:focus-within { border-color: #3b82f6; }\r\n\r\n/* Primary Colors (Legacy support) */\r\n.from-primary-400 { --tw-gradient-from: #8b93f8; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(139, 147, 248, 0)); }\r\n.from-primary-500 { --tw-gradient-from: #667eea; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(102, 126, 234, 0)); }\r\n.from-primary-600 { --tw-gradient-from: #5a67d8; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(90, 103, 216, 0)); }\r\n.to-secondary-400 { --tw-gradient-to: #e879f9; }\r\n.to-secondary-500 { --tw-gradient-to: #764ba2; }\r\n.to-secondary-600 { --tw-gradient-to: #c026d3; }\r\n.to-primary-600 { --tw-gradient-to: #5a67d8; }\r\n\r\n/* Slate Colors */\r\n.from-slate-900 { --tw-gradient-from: #0f172a; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(15, 23, 42, 0)); }\r\n.via-slate-800 { --tw-gradient-to: rgba(30, 41, 59, 0); --tw-gradient-stops: var(--tw-gradient-from), #1e293b, var(--tw-gradient-to, rgba(30, 41, 59, 0)); }\r\n.to-slate-900 { --tw-gradient-to: #0f172a; }\r\n\r\n/* Background Clip Text */\r\n.bg-clip-text { -webkit-background-clip: text; background-clip: text; }\r\n.text-transparent { color: transparent; }\r\n\r\n/* Primary Color Classes */\r\n.bg-primary-50 { background-color: #f0f4ff; }\r\n.bg-primary-500 { background-color: #667eea; }\r\n.text-primary-600 { color: #5a67d8; }\r\n.text-primary-700 { color: #4c51bf; }\r\n.border-primary-400 { border-color: #8b93f8; }\r\n.border-primary-500 { border-color: #667eea; }\r\n\r\n/* White with opacity */\r\n.bg-white\\/5 { background-color: rgba(255, 255, 255, 0.05); }\r\n.bg-white\\/10 { background-color: rgba(255, 255, 255, 0.1); }\r\n.bg-white\\/20 { background-color: rgba(255, 255, 255, 0.2); }\r\n.bg-white\\/80 { background-color: rgba(255, 255, 255, 0.8); }\r\n.bg-white\\/95 { background-color: rgba(255, 255, 255, 0.95); }\r\n.border-white\\/10 { border-color: rgba(255, 255, 255, 0.1); }\r\n.border-white\\/20 { border-color: rgba(255, 255, 255, 0.2); }\r\n.border-white\\/30 { border-color: rgba(255, 255, 255, 0.3); }\r\n.text-white\\/60 { color: rgba(255, 255, 255, 0.6); }\r\n.text-white\\/70 { color: rgba(255, 255, 255, 0.7); }\r\n\r\n/* Disabled states */\r\n.disabled\\:from-gray-300:disabled { --tw-gradient-from: #d1d5db; }\r\n.disabled\\:to-gray-400:disabled { --tw-gradient-to: #9ca3af; }\r\n.disabled\\:from-gray-400:disabled { --tw-gradient-from: #9ca3af; }\r\n.disabled\\:to-gray-500:disabled { --tw-gradient-to: #6b7280; }\r\n\r\n/* Hover states for gradients */\r\n.hover\\:from-primary-600:hover { --tw-gradient-from: #5a67d8; }\r\n.hover\\:to-secondary-600:hover { --tw-gradient-to: #c026d3; }\r\n.hover\\:bg-primary-100:hover { background-color: #e0e9ff; }\r\n.hover\\:bg-primary-500:hover { background-color: #667eea; }\r\n\r\n/* Focus states */\r\n.focus\\:ring-primary-200:focus { box-shadow: 0 0 0 3px rgba(199, 214, 254, 0.5); }\r\n.focus\\:ring-primary-500:focus { box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.5); }\r\n.focus\\:border-primary-500:focus { border-color: #667eea; }\r\n\r\n/* Responsive grid */\r\n@media (min-width: 768px) {\r\n  .md\\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }\r\n}\r\n\r\n/* Animation keyframes */\r\n@keyframes pulse-slow {\r\n  0%, 100% { opacity: 1; }\r\n  50% { opacity: 0.5; }\r\n}\r\n\r\n@keyframes bounce-slow {\r\n  0%, 100% { transform: translateY(0); }\r\n  50% { transform: translateY(-25%); }\r\n}\r\n\r\n@keyframes spin {\r\n  to { transform: rotate(360deg); }\r\n}\r\n\r\n/* Animation classes */\r\n.animate-pulse-slow { animation: pulse-slow 3s cubic-bezier(0.4, 0, 0.6, 1) infinite; }\r\n.animate-bounce-slow { animation: bounce-slow 3s infinite; }\r\n.animate-bounce { animation: bounce 1s infinite; }\r\n.animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }\r\n.animate-spin { animation: spin 1s linear infinite; }\r\n\r\n/* Custom scrollbar styles */\r\n.custom-scrollbar::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.custom-scrollbar::-webkit-scrollbar-track {\r\n  background: transparent;\r\n}\r\n\r\n.custom-scrollbar::-webkit-scrollbar-thumb {\r\n  background: rgba(156, 163, 175, 0.5);\r\n  border-radius: 3px;\r\n}\r\n\r\n.custom-scrollbar::-webkit-scrollbar-thumb:hover {\r\n  background: rgba(156, 163, 175, 0.7);\r\n}\r\n\r\n/* Typing animation for loading dots */\r\n.typing-dot {\r\n  animation: typing 1.4s infinite ease-in-out;\r\n}\r\n\r\n.typing-dot:nth-child(1) {\r\n  animation-delay: 0s;\r\n}\r\n\r\n.typing-dot:nth-child(2) {\r\n  animation-delay: 0.2s;\r\n}\r\n\r\n.typing-dot:nth-child(3) {\r\n  animation-delay: 0.4s;\r\n}\r\n\r\n@keyframes typing {\r\n  0%, 60%, 100% {\r\n    transform: translateY(0);\r\n  }\r\n  30% {\r\n    transform: translateY(-10px);\r\n  }\r\n}\r\n\r\n/* Gemini-style fade and scale animations */\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(10px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n@keyframes scaleIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: scale(0.95);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n@keyframes slideUp {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(20px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n/* ChatGPT-style enhancements */\r\n.chat-container {\r\n  max-width: 100%;\r\n  margin: 0 auto;\r\n}\r\n\r\n@media (min-width: 768px) {\r\n  .chat-container {\r\n    max-width: 768px;\r\n  }\r\n}\r\n\r\n@media (min-width: 1024px) {\r\n  .chat-container {\r\n    max-width: 1024px;\r\n  }\r\n}\r\n\r\n/* Enhanced textarea auto-resize */\r\ntextarea {\r\n  resize: none;\r\n  overflow-y: auto;\r\n  scrollbar-width: thin;\r\n  scrollbar-color: #cbd5e1 transparent;\r\n}\r\n\r\ntextarea::-webkit-scrollbar {\r\n  width: 4px;\r\n}\r\n\r\ntextarea::-webkit-scrollbar-track {\r\n  background: transparent;\r\n}\r\n\r\ntextarea::-webkit-scrollbar-thumb {\r\n  background-color: #cbd5e1;\r\n  border-radius: 2px;\r\n}\r\n\r\n/* Voice recording pulse animation */\r\n@keyframes pulse-red {\r\n  0%, 100% {\r\n    opacity: 1;\r\n  }\r\n  50% {\r\n    opacity: 0.5;\r\n  }\r\n}\r\n\r\n.animate-pulse-red {\r\n  animation: pulse-red 1.5s ease-in-out infinite;\r\n}\r\n\r\n/* Animation classes for Gemini-style effects */\r\n.animate-fadeIn { animation: fadeIn 0.3s ease-out; }\r\n.animate-scaleIn { animation: scaleIn 0.2s ease-out; }\r\n.animate-slideUp { animation: slideUp 0.4s ease-out; }\r\n\r\n/* Gemini-style hover effects */\r\n.hover-lift:hover {\r\n  transform: translateY(-2px);\r\n  transition: transform 0.2s ease;\r\n}\r\n\r\n.hover-glow:hover {\r\n  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);\r\n  transition: box-shadow 0.3s ease;\r\n}\r\n\r\n/* Login Page Glassmorphism Animations */\r\n@keyframes float {\r\n  0%, 100% {\r\n    transform: translateY(0px);\r\n  }\r\n  50% {\r\n    transform: translateY(-20px);\r\n  }\r\n}\r\n\r\n@keyframes float-delayed {\r\n  0%, 100% {\r\n    transform: translateY(0px);\r\n  }\r\n  50% {\r\n    transform: translateY(-15px);\r\n  }\r\n}\r\n\r\n@keyframes float-slow {\r\n  0%, 100% {\r\n    transform: translateY(0px);\r\n  }\r\n  50% {\r\n    transform: translateY(-10px);\r\n  }\r\n}\r\n\r\n@keyframes twinkle {\r\n  0%, 100% {\r\n    opacity: 0.3;\r\n    transform: scale(1);\r\n  }\r\n  50% {\r\n    opacity: 1;\r\n    transform: scale(1.2);\r\n  }\r\n}\r\n\r\n@keyframes twinkle-delayed {\r\n  0%, 100% {\r\n    opacity: 0.2;\r\n    transform: scale(1);\r\n  }\r\n  50% {\r\n    opacity: 0.8;\r\n    transform: scale(1.1);\r\n  }\r\n}\r\n\r\n/* Animation classes for login particles */\r\n.animate-float {\r\n  animation: float 6s ease-in-out infinite;\r\n}\r\n\r\n.animate-float-delayed {\r\n  animation: float-delayed 8s ease-in-out infinite;\r\n  animation-delay: 2s;\r\n}\r\n\r\n.animate-float-slow {\r\n  animation: float-slow 10s ease-in-out infinite;\r\n  animation-delay: 1s;\r\n}\r\n\r\n.animate-twinkle {\r\n  animation: twinkle 3s ease-in-out infinite;\r\n}\r\n\r\n.animate-twinkle-delayed {\r\n  animation: twinkle-delayed 4s ease-in-out infinite;\r\n  animation-delay: 1.5s;\r\n}\r\n\r\n/* Additional glassmorphism colors */\r\n.from-indigo-900 { --tw-gradient-from: #312e81; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(49, 46, 129, 0)); }\r\n.via-purple-900 { --tw-gradient-to: rgba(88, 28, 135, 0); --tw-gradient-stops: var(--tw-gradient-from), #581c87, var(--tw-gradient-to, rgba(88, 28, 135, 0)); }\r\n.to-indigo-700 { --tw-gradient-to: #4338ca; }\r\n.text-indigo-200 { color: #c7d2fe; }\r\n.text-pink-400 { color: #f472b6; }\r\n.from-pink-500 { --tw-gradient-from: #ec4899; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(236, 72, 153, 0)); }\r\n.to-purple-600 { --tw-gradient-to: #9333ea; }\r\n.from-pink-400 { --tw-gradient-from: #f472b6; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(244, 114, 182, 0)); }\r\n.to-purple-500 { --tw-gradient-to: #a855f7; }\r\n.shadow-pink-500\\/30 { box-shadow: 0 25px 50px -12px rgba(236, 72, 153, 0.3); }\r\n.shadow-pink-500\\/50 { box-shadow: 0 25px 50px -12px rgba(236, 72, 153, 0.5); }\r\n.border-indigo-300\\/40 { border-color: rgba(165, 180, 252, 0.4); }\r\n.text-indigo-100 { color: #e0e7ff; }\r\n.ring-1 { box-shadow: 0 0 0 1px var(--tw-ring-color); }\r\n.ring-white\\/10 { --tw-ring-color: rgba(255, 255, 255, 0.1); }\r\n.focus\\:ring-pink-400:focus { --tw-ring-color: #f472b6; box-shadow: 0 0 0 3px rgba(244, 114, 182, 0.5); }\r\n.focus\\:border-pink-400:focus { border-color: #f472b6; }\r\n"], "names": [], "sourceRoot": ""}
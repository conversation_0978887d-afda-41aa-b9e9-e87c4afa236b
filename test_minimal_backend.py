#!/usr/bin/env python3
"""
Minimal test to check what's preventing the backend from starting
"""

import sys
import os

# Add backend to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

def test_imports():
    """Test imports one by one to find the issue"""
    print("Testing imports step by step...")
    
    try:
        print("1. Testing eventlet...")
        import eventlet
        eventlet.monkey_patch()
        print("   ✓ eventlet imported and patched")
        
        print("2. Testing Flask...")
        from flask import Flask
        print("   ✓ Flask imported")
        
        print("3. Testing Flask-SocketIO...")
        from flask_socketio import SocketIO
        print("   ✓ Flask-SocketIO imported")
        
        print("4. Testing database models...")
        from models.database import db, User, UserPreference
        print("   ✓ Database models imported")
        
        print("5. Testing basic services...")
        from services.langchain_service import LangChainService
        print("   ✓ LangChain service imported")
        
        print("6. Testing RAG service (this might take time)...")
        from services.rag_service import RAGService
        print("   ✓ RAG service imported")
        
        print("7. Testing other services...")
        from services.memory_service import MemoryService
        from services.agent_service import AgentService
        from services.auth_service import AuthService
        from services.document_service import DocumentService
        from services.language_service import LanguageService
        from services.personalization_service import PersonalizationService
        from services.download_service import DownloadService
        from services.file_conversion_service import FileConversionService
        from services.web_search_service import WebSearchService
        from services.speech_service import SpeechToTextService
        from services.tts_service import TTSService
        from services.streaming_asr_service import StreamingASRService
        print("   ✓ All other services imported")
        
        return True
        
    except Exception as e:
        print(f"   ✗ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_service_initialization():
    """Test service initialization"""
    print("\nTesting service initialization...")
    
    try:
        print("1. Initializing LangChain service...")
        from services.langchain_service import LangChainService
        langchain_service = LangChainService()
        print("   ✓ LangChain service initialized")
        
        print("2. Initializing RAG service...")
        from services.rag_service import RAGService
        rag_service = RAGService()
        print("   ✓ RAG service initialized")
        
        return True
        
    except Exception as e:
        print(f"   ✗ Service initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the test"""
    print("=" * 60)
    print("MINIMAL BACKEND TEST")
    print("=" * 60)
    
    # Test imports
    import_success = test_imports()
    
    if import_success:
        # Test service initialization
        init_success = test_service_initialization()
        
        if init_success:
            print("\n" + "=" * 60)
            print("🎉 ALL TESTS PASSED!")
            print("Backend should be able to start successfully.")
        else:
            print("\n" + "=" * 60)
            print("❌ SERVICE INITIALIZATION FAILED!")
    else:
        print("\n" + "=" * 60)
        print("❌ IMPORT TEST FAILED!")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())

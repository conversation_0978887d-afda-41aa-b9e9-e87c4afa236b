#!/usr/bin/env python3
"""
Debug SocketIO installation
"""

print("Checking Flask-SocketIO installation...")

try:
    import flask_socketio
    print(f"✓ Flask-SocketIO imported successfully")
    try:
        print(f"  Version: {flask_socketio.__version__}")
    except AttributeError:
        print("  Version: Unknown")
except ImportError as e:
    print(f"✗ Flask-SocketIO import failed: {e}")

try:
    import socketio
    print(f"✓ Python-SocketIO version: {socketio.__version__}")
except ImportError as e:
    print(f"✗ Python-SocketIO import failed: {e}")

try:
    import engineio
    print(f"✓ Python-EngineIO version: {engineio.__version__}")
except ImportError as e:
    print(f"✗ Python-EngineIO import failed: {e}")

print("\nTesting basic SocketIO server creation...")

try:
    import socketio
    sio = socketio.Server()
    print("✓ Basic SocketIO server created successfully")
except Exception as e:
    print(f"✗ Basic SocketIO server creation failed: {e}")

print("\nTesting Flask-SocketIO without async_mode...")

try:
    from flask import Flask
    from flask_socketio import SocketIO
    
    app = Flask(__name__)
    # Try without any parameters first
    socketio = SocketIO()
    socketio.init_app(app, cors_allowed_origins="*")
    print("✓ Flask-SocketIO initialized with init_app")
except Exception as e:
    print(f"✗ Flask-SocketIO init_app failed: {e}")

print("\nTesting minimal Flask-SocketIO...")

try:
    from flask import Flask
    from flask_socketio import SocketIO
    
    app = Flask(__name__)
    socketio = SocketIO(app)
    print("✓ Minimal Flask-SocketIO works")
except Exception as e:
    print(f"✗ Minimal Flask-SocketIO failed: {e}")

print("Debug completed!")

import os
import requests
import json
from typing import Dict, List, Optional
from dotenv import load_dotenv
import time
from urllib.parse import quote_plus
from bs4 import BeautifulSoup
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.error_handler import WebSearchErrorHandler, create_fallback_response

# Load environment variables from the backend directory
load_dotenv(os.path.join(os.path.dirname(__file__), '..', '.env'))

class WebSearchService:
    """
    Web Search Service for real-time information retrieval
    Supports Serper API and Google Custom Search as fallback
    """
    
    def __init__(self):
        # Ensure environment variables are loaded
        load_dotenv(os.path.join(os.path.dirname(__file__), '..', '.env'))

        self.serper_api_key = os.getenv('SERPER_API_KEY')
        self.google_search_key = os.getenv('GOOGLE_SEARCH_KEY')
        self.search_engine_id = os.getenv('SEARCH_ENGINE_ID')

        # Optional: Log configuration warnings
        if not self.serper_api_key and not (self.google_search_key and self.search_engine_id):
            print("Warning: No web search APIs configured. Set SERPER_API_KEY or Google Custom Search credentials.")
        
        # API endpoints
        self.serper_url = "https://google.serper.dev/search"
        self.google_search_url = "https://www.googleapis.com/customsearch/v1"
        
        # Request headers
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 1.0  # 1 second between requests
    
    def _rate_limit(self):
        """Implement rate limiting to avoid API abuse"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time

        if time_since_last < self.min_request_interval:
            time.sleep(self.min_request_interval - time_since_last)

        self.last_request_time = time.time()
    
    def _clean_text(self, text: str) -> str:
        """Clean and format text content"""
        if not text:
            return ""
        
        # Remove extra whitespace and newlines
        text = ' '.join(text.split())
        
        # Limit length to avoid token limits
        max_length = 500
        if len(text) > max_length:
            text = text[:max_length] + "..."
        
        return text
    
    def _search_with_serper(self, query: str, num_results: int = 5) -> Dict:
        """Search using Serper API (primary method) with enhanced error handling"""
        if not self.serper_api_key:
            raise ValueError("SERPER_API_KEY not configured")

        # Validate API key format (should be a hex string)
        if not self.serper_api_key.strip():
            raise ValueError("SERPER_API_KEY is empty")

        self._rate_limit()

        try:
            print(f"🔍 Attempting Serper API search for: {query}")
            # Use the robust error handler for Serper API
            data = WebSearchErrorHandler.safe_serper_search(
                self.serper_api_key,
                query,
                num_results
            )
            print(f"✅ Serper API search successful")
            return self._format_serper_results(data)

        except Exception as e:
            error_msg = str(e)
            print(f"❌ Serper API failed: {error_msg}")

            # Check if it's a DNS/network issue
            if any(indicator in error_msg.lower() for indicator in ['dns', 'resolve', 'connection', 'network', 'unreachable']):
                print("🔄 DNS/Network issue detected, trying alternative approaches...")

                # Try with different DNS resolution approach
                try:
                    print("🔄 Attempting Serper API with alternative network settings...")
                    import socket
                    # Try to resolve DNS manually first
                    socket.gethostbyname('google.serper.dev')

                    # If DNS resolves, try the request again with different settings
                    import requests
                    session = requests.Session()
                    session.trust_env = False  # Disable proxy detection

                    headers = {
                        'X-API-KEY': self.serper_api_key,
                        'Content-Type': 'application/json',
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    }
                    payload = {
                        'q': query,
                        'num': min(num_results, 10),
                        'hl': 'en',
                        'gl': 'us'
                    }

                    response = session.post(
                        "https://google.serper.dev/search",
                        headers=headers,
                        json=payload,
                        timeout=10,
                        verify=True
                    )

                    if response.status_code == 200:
                        print("✅ Alternative Serper API approach successful")
                        return self._format_serper_results(response.json())
                    else:
                        print(f"⚠️  Alternative Serper API returned status: {response.status_code}")

                except Exception as alt_error:
                    print(f"⚠️  Alternative Serper approach failed: {alt_error}")

            # Try DuckDuckGo fallback
            try:
                print("🔄 Attempting DuckDuckGo fallback...")
                fallback_data = WebSearchErrorHandler.fallback_duckduckgo_search(query, num_results)
                if fallback_data.get('organic'):
                    print(f"✅ DuckDuckGo fallback successful with {len(fallback_data['organic'])} results")
                    return self._format_serper_results(fallback_data)
                else:
                    print("⚠️  DuckDuckGo returned no results")
            except Exception as fallback_error:
                print(f"❌ DuckDuckGo fallback failed: {fallback_error}")

            # If all methods fail, re-raise the original error
            raise e
    
    def _search_with_google(self, query: str, num_results: int = 5) -> Dict:
        """Search using Google Custom Search API (fallback method)"""
        if not self.google_search_key or not self.search_engine_id:
            raise ValueError("Google Custom Search API not configured")
        
        self._rate_limit()
        
        params = {
            'key': self.google_search_key,
            'cx': self.search_engine_id,
            'q': query,
            'num': min(num_results, 10)
        }
        
        try:
            response = requests.get(
                self.google_search_url,
                params=params,
                timeout=10
            )
            response.raise_for_status()
            
            data = response.json()
            return self._format_google_results(data)
            
        except requests.exceptions.RequestException as e:
            raise Exception(f"Google Custom Search API error: {str(e)}")
    
    def _format_serper_results(self, data: Dict) -> Dict:
        """Format Serper API results"""
        results = []
        
        # Process organic results
        organic_results = data.get('organic', [])
        for item in organic_results:
            result = {
                'title': self._clean_text(item.get('title', '')),
                'link': item.get('link', ''),
                'snippet': self._clean_text(item.get('snippet', '')),
                'source': 'serper'
            }
            results.append(result)
        
        # Process knowledge graph if available
        knowledge_graph = data.get('knowledgeGraph', {})
        if knowledge_graph:
            kg_result = {
                'title': self._clean_text(knowledge_graph.get('title', '')),
                'link': knowledge_graph.get('website', ''),
                'snippet': self._clean_text(knowledge_graph.get('description', '')),
                'source': 'knowledge_graph'
            }
            results.insert(0, kg_result)  # Put knowledge graph first
        
        return {
            'query': data.get('searchParameters', {}).get('q', ''),
            'results': results,
            'total_results': len(results),
            'search_time': data.get('searchInformation', {}).get('searchTime', 0)
        }
    
    def _format_google_results(self, data: Dict) -> Dict:
        """Format Google Custom Search API results"""
        results = []
        
        items = data.get('items', [])
        for item in items:
            result = {
                'title': self._clean_text(item.get('title', '')),
                'link': item.get('link', ''),
                'snippet': self._clean_text(item.get('snippet', '')),
                'source': 'google_custom_search'
            }
            results.append(result)
        
        search_info = data.get('searchInformation', {})
        
        return {
            'query': data.get('queries', {}).get('request', [{}])[0].get('searchTerms', ''),
            'results': results,
            'total_results': int(search_info.get('totalResults', 0)),
            'search_time': float(search_info.get('searchTime', 0))
        }
    
    def search(self, query: str, num_results: int = 5) -> Dict:
        """
        Perform web search with automatic fallback
        
        Args:
            query: Search query string
            num_results: Number of results to return (max 10)
            
        Returns:
            Dict containing search results and metadata
        """
        if not query or not query.strip():
            return {
                'query': query,
                'results': [],
                'total_results': 0,
                'search_time': 0,
                'error': 'Empty query provided'
            }
        
        query = query.strip()
        
        # Try Serper API first
        if self.serper_api_key:
            try:
                return self._search_with_serper(query, num_results)
            except Exception as e:
                print(f"Serper API failed: {str(e)}")
        
        # Fallback to Google Custom Search
        if self.google_search_key and self.search_engine_id:
            try:
                return self._search_with_google(query, num_results)
            except Exception as e:
                print(f"Google Custom Search failed: {str(e)}")
        
        # If all methods fail
        return {
            'query': query,
            'results': [],
            'total_results': 0,
            'search_time': 0,
            'error': 'No search APIs configured or all APIs failed'
        }
    
    def get_search_summary(self, query: str, num_results: int = 3) -> str:
        """
        Get a formatted summary of search results for LLM consumption
        
        Args:
            query: Search query
            num_results: Number of results to include in summary
            
        Returns:
            Formatted string summary of search results
        """
        search_results = self.search(query, num_results)
        
        if search_results.get('error') or not search_results.get('results'):
            return f"No recent information found for: {query}"
        
        summary_parts = [
            f"Recent web search results for: {query}\n",
            f"Found {search_results['total_results']} results in {search_results['search_time']:.2f}s\n"
        ]
        
        for i, result in enumerate(search_results['results'][:num_results], 1):
            summary_parts.append(
                f"{i}. {result['title']}\n"
                f"   {result['snippet']}\n"
                f"   Source: {result['link']}\n"
            )
        
        return "\n".join(summary_parts)
    
    def is_configured(self) -> bool:
        """Check if at least one search API is configured"""
        return bool(self.serper_api_key or (self.google_search_key and self.search_engine_id))

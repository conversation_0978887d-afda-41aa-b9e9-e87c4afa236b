#!/usr/bin/env python3
"""
Test script to verify RAG service initialization and web search functionality
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

print("🧪 Testing RAG Service and Web Search Integration")
print("=" * 60)

# Test 1: RAG Service Initialization
print("\n1️⃣ Testing RAG Service Initialization...")
try:
    from services.rag_service import RAGService
    print("✓ RAG Service imported successfully")
    
    rag_service = RAGService()
    print("✓ RAG Service instantiated")
    
    # Check initialization status
    if rag_service.is_initialized():
        print("✅ RAG Service initialized successfully!")
        status = rag_service.get_status()
        print(f"   Status: {status['status']}")
        print(f"   Message: {status['message']}")
        print(f"   Document count: {status['document_count']}")
    else:
        print("⚠️  RAG Service not fully initialized")
        status = rag_service.get_status()
        print(f"   Status: {status['status']}")
        print(f"   Message: {status['message']}")
        
except Exception as e:
    print(f"❌ RAG Service test failed: {e}")
    import traceback
    traceback.print_exc()

# Test 2: Web Search Service
print("\n2️⃣ Testing Web Search Service...")
try:
    from services.web_search_service import WebSearchService
    print("✓ Web Search Service imported successfully")
    
    web_service = WebSearchService()
    print("✓ Web Search Service instantiated")
    
    # Check configuration
    if web_service.is_configured():
        print("✅ Web Search Service configured!")
        print(f"   Serper API: {'✓' if web_service.serper_api_key else '✗'}")
        print(f"   Google Search: {'✓' if (web_service.google_search_key and web_service.search_engine_id) else '✗'}")
        
        # Test a simple search
        print("\n   Testing search functionality...")
        try:
            results = web_service.search("latest AI news", num_results=2)
            if results.get('results'):
                print(f"✅ Search successful! Found {len(results['results'])} results")
                for i, result in enumerate(results['results'][:2], 1):
                    print(f"      {i}. {result['title'][:50]}...")
            else:
                print(f"⚠️  Search returned no results: {results.get('error', 'Unknown error')}")
        except Exception as search_error:
            print(f"⚠️  Search test failed: {search_error}")
    else:
        print("⚠️  Web Search Service not configured")
        
except Exception as e:
    print(f"❌ Web Search Service test failed: {e}")

# Test 3: Gemini Web Service
print("\n3️⃣ Testing Gemini Web Service...")
try:
    from services.gemini_web_service import GeminiWebService
    print("✓ Gemini Web Service imported successfully")
    
    gemini_web_service = GeminiWebService()
    print("✓ Gemini Web Service instantiated")
    
    # Check configuration
    if gemini_web_service.is_configured():
        print("✅ Gemini Web Service configured!")
        print(f"   Gemini API: {'✓' if gemini_web_service.gemini_model else '✗'}")
        print(f"   Serper API: {'✓' if gemini_web_service.serper_api_key else '✗'}")
        
        # Test live data detection
        test_query = "What's the latest news today?"
        needs_live = gemini_web_service.needs_live_data(test_query)
        print(f"   Live data needed for '{test_query}': {needs_live}")
        
    else:
        print("⚠️  Gemini Web Service not configured")
        
except Exception as e:
    print(f"❌ Gemini Web Service test failed: {e}")

# Test 4: LangChain Service
print("\n4️⃣ Testing LangChain Service...")
try:
    from services.langchain_service import LangChainService
    print("✓ LangChain Service imported successfully")
    
    langchain_service = LangChainService()
    print("✅ LangChain Service instantiated successfully!")
    
except Exception as e:
    print(f"❌ LangChain Service test failed: {e}")

# Test 5: Agent Service
print("\n5️⃣ Testing Agent Service...")
try:
    from services.agent_service import AgentService
    print("✓ Agent Service imported successfully")
    
    agent_service = AgentService()
    print("✅ Agent Service instantiated successfully!")
    
    # Test available tools
    tools = agent_service.get_available_tools()
    print(f"   Available tools: {tools}")
    
except Exception as e:
    print(f"❌ Agent Service test failed: {e}")

print("\n" + "=" * 60)
print("🏁 Test Summary Complete!")
print("\nIf you see ✅ for RAG Service and Web Search, the main issues are fixed!")
print("If you see ⚠️  warnings, the services are working but may have limited functionality.")
print("If you see ❌ errors, there are still issues that need to be addressed.")

#!/usr/bin/env python3
"""
Test script for TTS service functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.tts_service import TTSService

def test_tts_service():
    """Test the TTS service functionality"""
    print("Testing TTS Service...")
    
    try:
        # Initialize TTS service
        tts_service = TTSService()
        print("✓ TTS Service initialized successfully")
        
        # Test language detection
        test_texts = [
            "Hello, how are you today?",
            "नमस्ते, आप कैसे हैं?",  # Hindi
            "வணக்கம், நீங்கள் எப்படி இருக்கிறீர்கள்?",  # Tamil
            "Bonjour, comment allez-vous?"  # French
        ]
        
        for text in test_texts:
            print(f"\nTesting text: {text}")
            
            # Test language detection
            lang_code, lang_name = tts_service.detect_language(text)
            print(f"  Detected language: {lang_name} ({lang_code})")
            
            # Check if supported
            is_supported = tts_service.is_language_supported(lang_code)
            print(f"  Supported: {is_supported}")
            
            # Check if Indian language
            is_indian = tts_service.is_indian_language(lang_code)
            print(f"  Indian language: {is_indian}")
            
            # Test TTS generation (without actually generating audio)
            if is_supported:
                print(f"  ✓ Can generate TTS for {lang_name}")
            else:
                print(f"  ✗ Cannot generate TTS for {lang_name}")
        
        # Test get_supported_languages
        supported_langs = tts_service.get_supported_languages()
        print(f"\nSupported languages: {len(supported_langs['languages'])} total")
        print(f"Indian languages: {len(supported_langs['indian_languages'])}")
        
        # Test get_language_info
        info = tts_service.get_language_info("Hello world")
        print(f"\nLanguage info for 'Hello world': {info}")
        
        print("\n✓ All TTS service tests passed!")
        return True
        
    except Exception as e:
        print(f"\n✗ TTS service test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_tts_service()
    sys.exit(0 if success else 1)

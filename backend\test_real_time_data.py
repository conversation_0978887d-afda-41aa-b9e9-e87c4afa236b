#!/usr/bin/env python3
"""
Test script to verify true real-time web data retrieval with Gemini + external API fallbacks
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

print("🌐 Testing True Real-Time Web Data Retrieval")
print("=" * 70)
print("New Hierarchy: Gemini → External APIs → Serper → DuckDuckGo")
print("=" * 70)

# Import services
try:
    from services.gemini_web_service import GeminiWebService
    
    print("✓ Services imported successfully")
    
    # Initialize services
    gemini_web_service = GeminiWebService()
    
    print("✅ Services initialized successfully")
    
except Exception as e:
    print(f"❌ Service initialization failed: {e}")
    sys.exit(1)

# Test queries for different domains
test_queries = [
    ("What's the latest news in India?", "📰 News Domain"),
    ("Show me the current weather in Chennai.", "🌦️ Weather Domain"),
    ("What's the TCS stock price today?", "💹 Stock Domain"),
    ("Give me today's cricket score India vs Australia.", "🏏 Cricket Domain"),
    ("What are today's breaking news headlines?", "📰 Breaking News"),
    ("Current stock market updates", "💹 Market Updates"),
    ("Explain artificial intelligence", "🧠 General Knowledge")
]

print(f"\n🧪 Testing {len(test_queries)} queries across multiple domains...")

for i, (query, domain) in enumerate(test_queries, 1):
    print(f"\n{i}️⃣ {domain}: '{query}'")
    print("-" * 60)
    
    # Test 1: Check live data detection
    needs_live = gemini_web_service.needs_live_data(query)
    print(f"   Live data needed: {needs_live}")
    
    # Test 2: Gemini Live Data Retrieval with Enhanced Parsing
    print("   🤖 Testing Enhanced Gemini Live Data Retrieval...")
    try:
        gemini_result = gemini_web_service.get_live_data_with_gemini(query)
        if gemini_result['success']:
            response_length = len(gemini_result['data'])
            print(f"   ✅ Gemini live data successful! (length: {response_length} chars)")
            print(f"   Source: {gemini_result['source']}")
            print(f"   Confidence: {gemini_result.get('confidence', 'unknown')}")
            print(f"   Preview: {gemini_result['data'][:150]}...")
            
            # Check for disclaimer patterns
            disclaimer_patterns = [
                "do not have access",
                "cannot provide",
                "i don't have access",
                "my knowledge is limited"
            ]
            
            has_disclaimer = any(pattern in gemini_result['data'].lower() for pattern in disclaimer_patterns)
            if has_disclaimer:
                print("   ⚠️  Response contains disclaimer - will trigger external API fallback")
            else:
                print("   ✓ Response contains real-time information")
        else:
            print(f"   ⚠️  Gemini live data failed: {gemini_result['error']}")
            if gemini_result.get('fallback_needed'):
                print("   🔄 External API fallback needed")
    except Exception as gemini_error:
        print(f"   ❌ Gemini live data error: {gemini_error}")
    
    # Test 3: External API Fallback (domain-specific)
    if domain.startswith("📰"):
        print("   📰 Testing News API Fallback...")
        try:
            news_result = gemini_web_service._fetch_news_data(query)
            if news_result['success']:
                print(f"   ✅ News API successful! (length: {len(news_result['data'])} chars)")
                print(f"   Preview: {news_result['data'][:100]}...")
            else:
                print(f"   ⚠️  News API failed: {news_result['error']}")
        except Exception as news_error:
            print(f"   ❌ News API error: {news_error}")
    
    elif domain.startswith("🌦️"):
        print("   🌦️ Testing Weather API Fallback...")
        try:
            weather_result = gemini_web_service._fetch_weather_data(query)
            if weather_result['success']:
                print(f"   ✅ Weather API successful! (length: {len(weather_result['data'])} chars)")
                print(f"   Preview: {weather_result['data'][:100]}...")
            else:
                print(f"   ⚠️  Weather API failed: {weather_result['error']}")
        except Exception as weather_error:
            print(f"   ❌ Weather API error: {weather_error}")
    
    elif domain.startswith("💹"):
        print("   💹 Testing Stock API Fallback...")
        try:
            stock_result = gemini_web_service._fetch_stock_data(query)
            if stock_result['success']:
                print(f"   ✅ Stock API successful! (length: {len(stock_result['data'])} chars)")
                print(f"   Preview: {stock_result['data'][:100]}...")
            else:
                print(f"   ⚠️  Stock API failed: {stock_result['error']}")
        except Exception as stock_error:
            print(f"   ❌ Stock API error: {stock_error}")
    
    elif domain.startswith("🏏"):
        print("   🏏 Testing Cricket API Fallback...")
        try:
            cricket_result = gemini_web_service._fetch_cricket_data(query)
            if cricket_result['success']:
                print(f"   ✅ Cricket API successful! (length: {len(cricket_result['data'])} chars)")
                print(f"   Preview: {cricket_result['data'][:100]}...")
            else:
                print(f"   ⚠️  Cricket API failed: {cricket_result['error']}")
        except Exception as cricket_error:
            print(f"   ❌ Cricket API error: {cricket_error}")
    
    # Test 4: Comprehensive Response Pipeline (Full Integration)
    print("   🔧 Testing Complete Real-Time Pipeline...")
    try:
        comprehensive_response = gemini_web_service.get_comprehensive_response(query)
        if comprehensive_response and len(comprehensive_response) > 50:
            print(f"   ✅ Comprehensive response generated! (length: {len(comprehensive_response)} chars)")
            print(f"   Preview: {comprehensive_response[:200]}...")
            
            # Check for success indicators
            success_indicators = [
                'live, time-sensitive content',
                'current web data',
                'current web search',
                'available search results',
                '📰', '🌦️', '💹', '🏏', '✅', '📅'
            ]
            
            success_type = None
            for indicator in success_indicators:
                if indicator in comprehensive_response:
                    success_type = indicator
                    break
            
            if success_type:
                print(f"   ✓ Success type: {success_type}")
            
            # Check for disclaimers (should be eliminated)
            disclaimer_check = any(pattern in comprehensive_response.lower() for pattern in [
                "do not have access",
                "cannot provide",
                "i don't have access"
            ])
            
            if disclaimer_check:
                print("   ⚠️  WARNING: Response still contains disclaimers!")
            else:
                print("   ✓ No disclaimers found - real-time data delivered")
        else:
            print("   ⚠️  Comprehensive response seems short or empty")
    except Exception as comp_error:
        print(f"   ❌ Comprehensive response error: {comp_error}")

print("\n" + "=" * 70)
print("🎯 Real-Time Data Retrieval Test Summary")
print("\n📊 Expected Results:")
print("   ✅ Gemini provides live data without disclaimers")
print("   ✅ External APIs provide domain-specific fallbacks")
print("   ✅ Smart routing based on query type")
print("   ✅ No 'I don't have access to live data' messages")
print("   ✅ Timestamped, factual responses")

print("\n🚀 Success Criteria:")
print("   • Gemini responses contain real-time information")
print("   • External APIs fill gaps when Gemini fails")
print("   • All domains supported (News, Weather, Stocks, Cricket)")
print("   • Smart keyword-based routing")
print("   • Zero disclaimer messages")

print("\n💡 Expected Console Messages:")
print("   🌐 Gemini Live Data Mode Enabled")
print("   ✅ Real-time content retrieved successfully")
print("   📰 News fetched via external API")
print("   🌦️ Weather updated via Open-Meteo API")
print("   💹 Stock data synced from Yahoo Finance")
print("   🏏 Cricket score retrieved successfully")

print("\n🔧 Next Steps:")
print("   1. Start Flask backend: python app.py")
print("   2. Test queries through frontend")
print("   3. Verify real-time responses without disclaimers")
print("   4. Monitor for successful external API usage")

print("\n✅ Expected Final Outcome:")
print("   • Live news headlines with sources and timestamps")
print("   • Current weather data with temperature and conditions")
print("   • Real-time stock prices with percentage changes")
print("   • Live cricket scores and match status")
print("   • Zero 'no access to live data' disclaimers")
print("   • Dynamic, timestamped live summaries always shown")

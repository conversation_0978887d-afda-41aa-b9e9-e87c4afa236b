#!/usr/bin/env python3
"""
Test script to verify Gemini 2.5 model integration and quota fallback mechanisms
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

print("🧪 Testing Gemini 2.5 Integration and Quota Fallback")
print("=" * 70)

# Test 1: Global Gemini Configuration
print("\n1️⃣ Testing Global Gemini Configuration...")
try:
    from config.gemini_config import GeminiConfig
    print("✓ GeminiConfig imported successfully")
    
    # Check initialization
    if GeminiConfig.is_initialized():
        print("✅ Global Gemini API configuration initialized")
        print(f"   Available models: {', '.join(GeminiConfig.MODELS.values())}")
        print(f"   Embedding model: {GeminiConfig.get_embedding_model_name()}")
        
        # Test model creation
        try:
            chat_model = GeminiConfig.get_model('chat')
            print(f"✅ Chat model created successfully: {type(chat_model).__name__}")
        except Exception as model_error:
            print(f"⚠️  Chat model creation failed: {model_error}")
    else:
        print("⚠️  Global Gemini configuration not initialized")
        
except Exception as e:
    print(f"❌ Global Gemini configuration test failed: {e}")

# Test 2: RAG Service with Updated Models and Fallback
print("\n2️⃣ Testing RAG Service with Gemini 2.5 and Local Fallback...")
try:
    from services.rag_service import RAGService
    print("✓ RAG Service imported successfully")
    
    rag_service = RAGService()
    print("✓ RAG Service instantiated")
    
    # Check initialization status
    if rag_service.is_initialized():
        print("✅ RAG Service initialized successfully!")
        status = rag_service.get_status()
        print(f"   Status: {status['status']}")
        print(f"   Message: {status['message']}")
        print(f"   Embedding mode: {rag_service.embedding_mode}")
        
        # Test context retrieval (should handle quota gracefully)
        print("\n   Testing context retrieval with quota handling...")
        try:
            context = rag_service.get_context("test query about AI")
            print(f"✅ Context retrieval completed (length: {len(context)} chars)")
            if rag_service.embedding_mode == "local":
                print("   ✓ Using local embedding fallback")
            elif rag_service.embedding_mode == "gemini":
                print("   ✓ Using Gemini embeddings")
            else:
                print("   ⚠️  Using basic text search fallback")
        except Exception as context_error:
            print(f"⚠️  Context retrieval test failed: {context_error}")
    else:
        print("⚠️  RAG Service not fully initialized")
        
except Exception as e:
    print(f"❌ RAG Service test failed: {e}")

# Test 3: LangChain Service with Gemini 2.5
print("\n3️⃣ Testing LangChain Service with Gemini 2.5...")
try:
    from services.langchain_service import LangChainService
    print("✓ LangChain Service imported successfully")
    
    langchain_service = LangChainService()
    print("✅ LangChain Service instantiated successfully!")
    
    # Test basic response generation
    print("   Testing response generation...")
    try:
        response = langchain_service.generate_response("Hello, can you tell me about AI?")
        if response and len(response) > 10:
            print(f"✅ Response generated successfully (length: {len(response)} chars)")
            print(f"   Preview: {response[:100]}...")
        else:
            print("⚠️  Response seems too short or empty")
    except Exception as response_error:
        print(f"⚠️  Response generation failed: {response_error}")
        
except Exception as e:
    print(f"❌ LangChain Service test failed: {e}")

# Test 4: Gemini Web Service with 2.5 Models
print("\n4️⃣ Testing Gemini Web Service with 2.5 Models...")
try:
    from services.gemini_web_service import GeminiWebService
    print("✓ Gemini Web Service imported successfully")
    
    gemini_web_service = GeminiWebService()
    print("✅ Gemini Web Service instantiated successfully!")
    
    # Check configuration
    if gemini_web_service.is_configured():
        print("✅ Gemini Web Service configured!")
        print(f"   Gemini model available: {'✓' if gemini_web_service.gemini_model else '✗'}")
        print(f"   Serper API: {'✓' if gemini_web_service.serper_api_key else '✗'}")
        
        # Test live data detection
        test_query = "What's the latest news today?"
        needs_live = gemini_web_service.needs_live_data(test_query)
        print(f"   Live data needed for '{test_query}': {needs_live}")
        
        # Test comprehensive response (with quota handling)
        print("   Testing comprehensive response with quota handling...")
        try:
            comprehensive_response = gemini_web_service.get_comprehensive_response("latest AI developments")
            if comprehensive_response and len(comprehensive_response) > 50:
                print(f"✅ Comprehensive response generated (length: {len(comprehensive_response)} chars)")
                print(f"   Preview: {comprehensive_response[:150]}...")
            else:
                print("⚠️  Comprehensive response seems short")
        except Exception as comp_error:
            print(f"⚠️  Comprehensive response failed: {comp_error}")
    else:
        print("⚠️  Gemini Web Service not fully configured")
        
except Exception as e:
    print(f"❌ Gemini Web Service test failed: {e}")

# Test 5: Web Search Service (should work independently)
print("\n5️⃣ Testing Web Search Service...")
try:
    from services.web_search_service import WebSearchService
    print("✓ Web Search Service imported successfully")
    
    web_service = WebSearchService()
    print("✓ Web Search Service instantiated")
    
    if web_service.is_configured():
        print("✅ Web Search Service configured!")
        
        # Test search functionality
        print("   Testing search functionality...")
        try:
            results = web_service.search("latest technology news", num_results=2)
            if results.get('results'):
                print(f"✅ Search successful! Found {len(results['results'])} results")
                for i, result in enumerate(results['results'][:2], 1):
                    print(f"      {i}. {result['title'][:50]}...")
            else:
                print(f"⚠️  Search returned no results: {results.get('error', 'Unknown error')}")
        except Exception as search_error:
            print(f"⚠️  Search test failed: {search_error}")
    else:
        print("⚠️  Web Search Service not configured")
        
except Exception as e:
    print(f"❌ Web Search Service test failed: {e}")

print("\n" + "=" * 70)
print("🎯 Gemini 2.5 Integration Test Summary")
print("\n📊 Expected Results:")
print("   ✅ Global Gemini config using gemini-2.0-flash-exp")
print("   ✅ RAG service with text-embedding-004 or local fallback")
print("   ✅ Quota errors handled gracefully with local embeddings")
print("   ✅ Web search working independently")
print("   ✅ All services using consistent Gemini 2.5 models")
print("\n🚀 If you see mostly ✅ symbols, the Gemini 2.5 upgrade is successful!")
print("   Any ⚠️  warnings indicate fallback mechanisms are working.")
print("   ❌ errors need to be addressed.")

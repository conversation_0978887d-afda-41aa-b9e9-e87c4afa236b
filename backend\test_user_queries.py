#!/usr/bin/env python3
"""
Test script to validate user queries work with Gemini 2.5 and live web data
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

print("🌐 Testing User Queries with Gemini 2.5 and Live Web Data")
print("=" * 70)

# Import services
try:
    from services.langchain_service import LangChainService
    from services.rag_service import RAGService
    from services.web_search_service import WebSearchService
    from services.gemini_web_service import GeminiWebService
    from services.agent_service import AgentService
    
    print("✓ All services imported successfully")
    
    # Initialize services
    langchain_service = LangChainService()
    rag_service = RAGService()
    web_search_service = WebSearchService()
    gemini_web_service = GeminiWebService()
    agent_service = AgentService()
    
    print("✅ All services initialized successfully")
    
except Exception as e:
    print(f"❌ Service initialization failed: {e}")
    sys.exit(1)

# Test queries as requested by user
test_queries = [
    "What is today's latest news in India?",
    "Show me the current weather update in Delhi.",
    "Explain prompt engineering and download it in Word format.",
    "Summarize AI policy updates from October 2025."
]

print(f"\n🧪 Testing {len(test_queries)} user queries...")

for i, query in enumerate(test_queries, 1):
    print(f"\n{i}️⃣ Testing Query: '{query}'")
    print("-" * 60)
    
    # Test 1: Web Search Service
    print("   📡 Testing Web Search Service...")
    try:
        search_results = web_search_service.search(query, num_results=3)
        if search_results.get('results'):
            print(f"   ✅ Web search successful! Found {len(search_results['results'])} results")
            for j, result in enumerate(search_results['results'][:2], 1):
                print(f"      {j}. {result['title'][:50]}...")
        else:
            print(f"   ⚠️  Web search returned no results: {search_results.get('error', 'Unknown')}")
    except Exception as search_error:
        print(f"   ❌ Web search failed: {search_error}")
    
    # Test 2: Gemini Web Service
    print("   🤖 Testing Gemini Web Service...")
    try:
        # Check if live data is needed
        needs_live = gemini_web_service.needs_live_data(query)
        print(f"   Live data needed: {needs_live}")
        
        if gemini_web_service.is_configured():
            comprehensive_response = gemini_web_service.get_comprehensive_response(query)
            if comprehensive_response and len(comprehensive_response) > 50:
                print(f"   ✅ Gemini response generated! (length: {len(comprehensive_response)} chars)")
                print(f"   Preview: {comprehensive_response[:100]}...")
                
                # Check for live data indicators
                live_indicators = ['recent', 'current', 'latest', 'today', 'now', '2024', '2025']
                has_live_data = any(indicator in comprehensive_response.lower() for indicator in live_indicators)
                if has_live_data:
                    print("   ✓ Response appears to include live/current information")
                else:
                    print("   ⚠️  Response may not include live information")
            else:
                print("   ⚠️  Gemini response seems short or empty")
        else:
            print("   ⚠️  Gemini Web Service not configured")
    except Exception as gemini_error:
        print(f"   ❌ Gemini Web Service failed: {gemini_error}")
    
    # Test 3: Agent Service (RAG + Web integration)
    print("   🔧 Testing Agent Service (RAG + Web)...")
    try:
        agent_response = agent_service.run_enhanced_agent(query, use_rag=True)
        if agent_response and len(agent_response) > 50:
            print(f"   ✅ Agent response generated! (length: {len(agent_response)} chars)")
            print(f"   Preview: {agent_response[:100]}...")
            
            # Check for integration indicators
            integration_indicators = ['search', 'found', 'results', 'web', 'current', 'latest']
            has_integration = any(indicator in agent_response.lower() for indicator in integration_indicators)
            if has_integration:
                print("   ✓ Response appears to integrate web search data")
            else:
                print("   ⚠️  Response may not include web integration")
        else:
            print("   ⚠️  Agent response seems short or empty")
    except Exception as agent_error:
        print(f"   ❌ Agent Service failed: {agent_error}")
    
    # Test 4: LangChain Service (basic response)
    print("   💬 Testing LangChain Service...")
    try:
        langchain_response = langchain_service.generate_response(query)
        if langchain_response and len(langchain_response) > 50:
            print(f"   ✅ LangChain response generated! (length: {len(langchain_response)} chars)")
            print(f"   Preview: {langchain_response[:100]}...")
        else:
            print("   ⚠️  LangChain response seems short or empty")
    except Exception as langchain_error:
        print(f"   ❌ LangChain Service failed: {langchain_error}")

print("\n" + "=" * 70)
print("🎯 User Query Test Summary")
print("\n📊 Expected Results:")
print("   ✅ Web search returns live, relevant results")
print("   ✅ Gemini Web Service provides current information")
print("   ✅ Agent Service integrates RAG + web search")
print("   ✅ LangChain Service generates coherent responses")
print("   ✅ No '429 quota exceeded' or 'model not found' errors")
print("\n🚀 Success Criteria:")
print("   • All services respond without critical errors")
print("   • Live data queries return current information")
print("   • Fallback mechanisms work when quota is exceeded")
print("   • RAG integration enhances responses with context")
print("\n💡 Next Steps:")
print("   1. Start the Flask backend: python app.py")
print("   2. Test these queries through the frontend UI")
print("   3. Verify real-time responses in the browser")
print("   4. Monitor console logs for expected success messages")

import React, { useState, useEffect } from 'react';
import { Download, FileText, File, Table, Database, Code, X } from 'lucide-react';
import axios from 'axios';

const DownloadButton = ({ conversationId, user, messages }) => {
  const [showFormats, setShowFormats] = useState(false);
  const [availableFormats, setAvailableFormats] = useState({});
  const [isDownloading, setIsDownloading] = useState(false);
  const [downloadingFormat, setDownloadingFormat] = useState(null);

  useEffect(() => {
    loadAvailableFormats();
  }, []);

  const loadAvailableFormats = async () => {
    try {
      const response = await axios.get('http://localhost:5000/api/download/formats');
      setAvailableFormats(response.data.formats);
    } catch (error) {
      console.error('Error loading download formats:', error);
    }
  };

  const getFormatIcon = (format) => {
    switch (format) {
      case 'pdf':
        return <FileText size={16} className="text-red-500" />;
      case 'docx':
        return <File size={16} className="text-blue-500" />;
      case 'xlsx':
        return <Table size={16} className="text-green-500" />;
      case 'csv':
        return <Database size={16} className="text-orange-500" />;
      case 'txt':
        return <FileText size={16} className="text-gray-500" />;
      case 'json':
        return <Code size={16} className="text-purple-500" />;
      default:
        return <File size={16} className="text-gray-500" />;
    }
  };

  const downloadConversation = async (format) => {
    if (!conversationId || !user) {
      alert('No conversation to download');
      return;
    }

    try {
      setIsDownloading(true);
      setDownloadingFormat(format);

      const response = await axios.get(
        `http://localhost:5000/api/download/conversation/${conversationId}`,
        {
          params: {
            format: format,
            user_id: user.id
          },
          responseType: 'blob'
        }
      );

      // Create blob and download
      const blob = new Blob([response.data]);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      
      // Get filename from response headers or create default
      const contentDisposition = response.headers['content-disposition'];
      let filename = `conversation_${conversationId}.${format}`;
      
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      }
      
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      setShowFormats(false);
    } catch (error) {
      console.error('Download error:', error);
      alert('Failed to download conversation. Please try again.');
    } finally {
      setIsDownloading(false);
      setDownloadingFormat(null);
    }
  };

  // Don't show download button if no conversation or messages
  if (!conversationId || !messages || messages.length === 0) {
    return null;
  }

  return (
    <div className="relative">
      <button
        onClick={() => setShowFormats(!showFormats)}
        disabled={isDownloading}
        className="flex items-center gap-2 px-3 py-2 text-gray-600 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-colors disabled:opacity-50"
        title="Download conversation"
      >
        <Download size={18} />
        <span className="text-sm font-medium">Download</span>
      </button>

      {showFormats && (
        <div className="absolute top-full right-0 mt-2 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
          <div className="p-3 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="font-medium text-gray-800">Download Format</h3>
              <button
                onClick={() => setShowFormats(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X size={16} />
              </button>
            </div>
            <p className="text-xs text-gray-500 mt-1">
              Choose a format to download your conversation
            </p>
          </div>

          <div className="p-2">
            {Object.entries(availableFormats).map(([format, info]) => (
              <button
                key={format}
                onClick={() => downloadConversation(format)}
                disabled={isDownloading}
                className={`
                  w-full flex items-center gap-3 px-3 py-2 rounded-lg transition-colors text-left
                  ${isDownloading && downloadingFormat === format
                    ? 'bg-primary-50 text-primary-600'
                    : 'hover:bg-gray-50 text-gray-700'
                  }
                  disabled:opacity-50
                `}
              >
                {getFormatIcon(format)}
                <div className="flex-1">
                  <div className="font-medium text-sm">{info.name}</div>
                  <div className="text-xs text-gray-500">{info.extension}</div>
                </div>
                {isDownloading && downloadingFormat === format && (
                  <div className="w-4 h-4 border-2 border-primary-600/30 border-t-primary-600 rounded-full animate-spin"></div>
                )}
              </button>
            ))}
          </div>

          <div className="p-3 border-t border-gray-200 bg-gray-50 rounded-b-lg">
            <p className="text-xs text-gray-600">
              <strong>Note:</strong> Downloads include the entire conversation history with timestamps and formatting.
            </p>
          </div>
        </div>
      )}

      {/* Backdrop to close dropdown */}
      {showFormats && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowFormats(false)}
        />
      )}
    </div>
  );
};

export default DownloadButton;

#!/usr/bin/env python3
"""
Minimal Flask app to test live data functionality
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import os
import socket
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Initialize Flask app
app = Flask(__name__)
CORS(app)

print("🚀 Starting minimal Flask app...")

# Network health diagnostics
def test_network_connectivity():
    """Test network connectivity to external APIs"""
    api_hosts = [
        "api.open-meteo.com",
        "newsapi.org", 
        "gnews.io",
        "api.cricapi.com",
        "query2.finance.yahoo.com"
    ]
    
    print("🌐 Testing network connectivity...")
    
    for host in api_hosts:
        try:
            ip = socket.gethostbyname(host)
            print(f"✅ DNS OK for {host} -> {ip}")
        except socket.gaierror as e:
            print(f"⚠️ DNS FAILED for {host}: {e}")

# Test connectivity
test_network_connectivity()

# Initialize services
print("🔧 Initializing services...")

try:
    from services.gemini_web_service import GeminiWebService
    gemini_service = GeminiWebService()
    print("✅ Gemini Web Service initialized successfully")
except Exception as e:
    print(f"❌ Failed to initialize Gemini service: {e}")
    gemini_service = None

try:
    from services.live_data_service import LiveDataService
    live_data_service = LiveDataService()
    print("✅ Live Data Service initialized successfully")
except Exception as e:
    print(f"❌ Failed to initialize Live Data service: {e}")
    live_data_service = None

print("🌐 Services initialized, starting Flask routes...")

# ===============================
# API Routes
# ===============================

@app.route('/api/chat', methods=['POST'])
def chat():
    """Main chat endpoint with live data support"""
    try:
        data = request.get_json()
        user_message = data.get('message', '')
        
        if not user_message:
            return jsonify({"error": "No message provided"}), 400
        
        print(f"🔍 User query: {user_message}")
        
        if gemini_service:
            # Use Gemini service for live data
            response = gemini_service.get_comprehensive_response(user_message)
            
            return jsonify({
                "response": response.get("response", "No response generated"),
                "source": response.get("source", "unknown"),
                "timestamp": response.get("timestamp", ""),
                "success": True
            })
        else:
            return jsonify({
                "response": "Gemini service not available",
                "success": False
            }), 500
            
    except Exception as e:
        print(f"❌ Chat error: {e}")
        return jsonify({
            "error": str(e),
            "success": False
        }), 500

@app.route('/api/test-live-data', methods=['GET'])
def test_live_data():
    """Test endpoint for live data functionality"""
    try:
        if not live_data_service:
            return jsonify({
                "error": "Live data service not available",
                "success": False
            }), 500
        
        # Test weather
        weather_result = live_data_service.fetch_weather("Chennai")
        
        # Test cricket
        cricket_result = live_data_service.fetch_cricket()
        
        return jsonify({
            "weather": weather_result,
            "cricket": cricket_result,
            "success": True
        })
        
    except Exception as e:
        print(f"❌ Live data test error: {e}")
        return jsonify({
            "error": str(e),
            "success": False
        }), 500

@app.route('/api/weather', methods=['GET'])
def get_weather():
    """Get weather data for a specific city"""
    try:
        city = request.args.get('city', 'Chennai')
        
        if not live_data_service:
            return jsonify({
                "error": "Live data service not available",
                "success": False
            }), 500
        
        result = live_data_service.fetch_weather(city)
        return jsonify(result)
        
    except Exception as e:
        print(f"❌ Weather API error: {e}")
        return jsonify({
            "error": str(e),
            "success": False
        }), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "gemini_service": gemini_service is not None,
        "live_data_service": live_data_service is not None,
        "timestamp": datetime.now().isoformat()
    })

@app.route('/', methods=['GET'])
def home():
    """Home endpoint"""
    return jsonify({
        "message": "Flask Live Data API is running!",
        "endpoints": [
            "POST /api/chat - Main chat with live data",
            "GET /api/test-live-data - Test all live data services",
            "GET /api/weather?city=Chennai - Get weather data",
            "GET /api/health - Health check"
        ],
        "timestamp": datetime.now().isoformat()
    })

if __name__ == '__main__':
    print("🚀 Starting minimal Flask app with live data support...")
    print("🌐 Server will be available at http://localhost:5000")
    print("📡 Live data APIs tested and ready!")
    print("🔗 Available endpoints:")
    print("  - POST /api/chat - Main chat with live data")
    print("  - GET /api/test-live-data - Test all live data services")
    print("  - GET /api/weather?city=Chennai - Get weather data")
    print("  - GET /api/health - Health check")
    print("  - GET / - Home page")
    
    app.run(debug=True, host='0.0.0.0', port=5000, threaded=True)

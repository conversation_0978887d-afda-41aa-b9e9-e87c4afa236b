import os
import google.generativeai as genai
from dotenv import load_dotenv
import json
import re
from datetime import datetime, timedelta
from .web_search_service import WebSearchService
from .gemini_web_service import GeminiWebService
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.error_handler import <PERSON><PERSON>rror<PERSON>andler, APIErrorHandler, create_fallback_response
from config.gemini_config import GeminiConfig

load_dotenv()

class LangChainService:
    def __init__(self):
        # Use global Gemini configuration
        if not GeminiConfig.is_initialized():
            GeminiConfig.initialize()

        if not GeminiConfig.is_initialized():
            raise ValueError("Gemini API not available")

        # Get Gemini models from global config with detailed configurations
        try:
            self.model = GeminiConfig.create_detailed_model('chat')
            self.analysis_model = GeminiConfig.create_analysis_model('chat')
            self.api_key = GeminiConfig.get_api_key()
            print("✅ LangChain Service initialized with Gemini 2.0 Flash (Detailed Mode)")
        except Exception as e:
            print(f"❌ LangChain Service Gemini initialization failed: {e}")
            raise e

        # Simple conversation history storage
        self.conversation_history = []

        # Initialize web search services
        self.web_search_service = WebSearchService()
        self.gemini_web_service = GeminiWebService()

        # Response validation patterns
        self.outdated_patterns = [
            r"as of my last update",
            r"i don't have access to real-time",
            r"i cannot browse the internet",
            r"my knowledge cutoff",
            r"i don't have current information",
            r"as of \d{4}",
            r"my training data",
            r"i cannot access current",
            r"i don't have recent",
            r"my last training"
        ]

        # Keywords that suggest need for current information
        self.current_info_keywords = [
            "today", "now", "current", "latest", "recent", "2024", "2025",
            "this year", "this month", "this week", "breaking news",
            "stock price", "weather", "news", "events"
        ]

    def _build_prompt(self, message, conversation_history=None, system_prompt=None):
        """Build prompt with conversation history for detailed responses"""
        if system_prompt is None:
            system_prompt = """You are a comprehensive AI assistant that provides detailed, thorough, and complete responses.

CRITICAL INSTRUCTIONS:
- Provide COMPLETE, DETAILED, and COMPREHENSIVE answers
- DO NOT summarize or condense information
- Include ALL relevant details, context, and background information
- Write in full paragraphs with thorough explanations
- Provide specific examples, data, and evidence when available
- Cover all aspects of the topic comprehensively
- Never give brief or bullet-point responses unless specifically requested
- Always aim for thorough, educational, and informative responses"""

        prompt_parts = [system_prompt]

        if conversation_history:
            for msg in conversation_history[-10:]:  # Last 10 messages
                if msg['role'] == 'user':
                    prompt_parts.append(f"Human: {msg['content']}")
                elif msg['role'] == 'assistant':
                    prompt_parts.append(f"Assistant: {msg['content']}")

        prompt_parts.append(f"Human: {message}")
        prompt_parts.append("Assistant:")

        return "\n\n".join(prompt_parts)

    def _is_response_outdated(self, response: str, query: str) -> bool:
        """Check if Gemini response indicates outdated or missing information"""
        response_lower = response.lower()
        query_lower = query.lower()

        # Check for explicit outdated response patterns
        for pattern in self.outdated_patterns:
            if re.search(pattern, response_lower):
                return True

        # Check if query asks for current information but response is vague
        has_current_keywords = any(keyword in query_lower for keyword in self.current_info_keywords)

        if has_current_keywords:
            # If query asks for current info but response is short or vague
            if len(response.strip()) < 100 or "i don't know" in response_lower:
                return True

        return False

    def _should_use_web_search(self, query: str) -> bool:
        """Determine if query likely needs real-time information"""
        query_lower = query.lower()

        # Check for current information keywords
        if any(keyword in query_lower for keyword in self.current_info_keywords):
            return True

        # Check for specific types of queries that need real-time data
        realtime_patterns = [
            r"what.*happening",
            r"latest.*news",
            r"current.*price",
            r"today.*weather",
            r"recent.*events",
            r"breaking.*news"
        ]

        for pattern in realtime_patterns:
            if re.search(pattern, query_lower):
                return True

        return False

    def generate_response(self, message, conversation_history=None, system_prompt=None):
        """Generate response using Gemini with robust error handling and web search fallback"""
        gemini_response = None

        # First, try to get response from Gemini with retry logic
        try:
            prompt = self._build_prompt(message, conversation_history, system_prompt)
            gemini_response = GeminiErrorHandler.safe_generate_content(self.model, prompt)

            # Check if we should use web search (either proactively or as fallback)
            should_search = self._should_use_web_search(message)
            is_outdated = self._is_response_outdated(gemini_response, message)

        except Exception as gemini_error:
            error_msg = str(gemini_error)
            print(f"Gemini API error: {error_msg}")

            # Handle different types of Gemini errors
            if APIErrorHandler.is_quota_error(error_msg):
                # Try web search as fallback for quota errors
                should_search = True
                is_outdated = False
                gemini_response = None
            else:
                # For other errors, return appropriate fallback
                if "quota" in error_msg.lower():
                    return create_fallback_response("gemini_quota", message)["data"]
                else:
                    return create_fallback_response("gemini_error", message)["data"]

        # Handle web search if needed
        if should_search or is_outdated:
            try:
                # Use enhanced Gemini web service for better live data retrieval
                if self.gemini_web_service.is_configured():
                    comprehensive_response = self.gemini_web_service.get_comprehensive_response(message)
                    return comprehensive_response
                else:
                    # Fallback to traditional web search
                    search_summary = self.web_search_service.get_search_summary(message, num_results=3)

                if "No recent information found" not in search_summary:
                    # If we have Gemini response, enhance it with web search
                    if gemini_response:
                        enhanced_prompt = f"""
{system_prompt or "You are a helpful AI assistant."}

Here is some recent information from the web that might be relevant:

{search_summary}

Based on this current information and your knowledge, please answer the following question:
{message}

Please provide a comprehensive answer that incorporates both the recent web information and your existing knowledge. If the web information contradicts your knowledge, prioritize the more recent web information.
"""
                        try:
                            enhanced_response = GeminiErrorHandler.safe_generate_content(self.model, enhanced_prompt)
                            return f"{enhanced_response}\n\n*This response includes recent information from web search.*"
                        except Exception as enhance_error:
                            print(f"Enhanced response generation failed: {enhance_error}")
                            # Return web search results directly if Gemini enhancement fails
                            return f"Based on recent web search:\n\n{search_summary}"
                    else:
                        # If no Gemini response (due to quota), return web search results
                        return f"Here's what I found from recent web search:\n\n{search_summary}"

            except Exception as search_error:
                print(f"Web search failed: {search_error}")
                # If both Gemini and web search fail
                if not gemini_response:
                    return create_fallback_response("web_search_failed", message)["data"]

        # Return original Gemini response if available
        if gemini_response:
            return gemini_response

        # Final fallback if everything fails
        return create_fallback_response("general_error", message)["data"]

    def stream_response(self, message, conversation_history=None, system_prompt=None):
        """Stream response using Gemini with web search fallback"""
        try:
            # Check if we should use web search proactively
            should_search = self._should_use_web_search(message)

            if should_search and (self.gemini_web_service.is_configured() or self.web_search_service.is_configured()):
                try:
                    # Use enhanced Gemini web service for better live data
                    if self.gemini_web_service.is_configured():
                        comprehensive_response = self.gemini_web_service.get_comprehensive_response(message)
                        # Stream the comprehensive response
                        for chunk in comprehensive_response.split(' '):
                            yield chunk + ' '
                        return
                    else:
                        # Fallback to traditional web search
                        search_summary = self.web_search_service.get_search_summary(message, num_results=3)

                    if "No recent information found" not in search_summary:
                        # Create enhanced prompt with web search results
                        enhanced_prompt = f"""
{system_prompt or "You are a helpful AI assistant."}

Here is some recent information from the web that might be relevant:

{search_summary}

Based on this current information and your knowledge, please answer the following question:
{message}

Please provide a comprehensive answer that incorporates both the recent web information and your existing knowledge.
"""

                        # Stream enhanced response with error handling
                        try:
                            response = self.model.generate_content(enhanced_prompt, stream=True)
                            for chunk in response:
                                if chunk.text:
                                    yield chunk.text
                            yield "\n\n*This response includes recent information from web search.*"
                            return
                        except Exception as gemini_error:
                            error_msg = str(gemini_error)
                            if APIErrorHandler.is_quota_error(error_msg):
                                # If quota exceeded, return web search results directly
                                yield f"Here's what I found from recent web search:\n\n{search_summary}"
                                return
                            else:
                                # For other Gemini errors, fall back to regular streaming
                                print(f"Enhanced streaming failed: {gemini_error}")

                except Exception as search_error:
                    print(f"Web search failed during streaming: {search_error}")
                    # Fall back to regular streaming
                    pass

            # Regular streaming without web search
            try:
                prompt = self._build_prompt(message, conversation_history, system_prompt)
                response = self.model.generate_content(prompt, stream=True)

                for chunk in response:
                    if chunk.text:
                        yield chunk.text

            except Exception as gemini_error:
                error_msg = str(gemini_error)
                if APIErrorHandler.is_quota_error(error_msg):
                    yield create_fallback_response("gemini_quota", message)["data"]
                else:
                    yield create_fallback_response("gemini_error", message)["data"]

        except Exception as e:
            yield create_fallback_response("general_error", message)["data"]

    def get_memory_summary(self):
        """Get summary of current conversation memory"""
        return {"conversation_history": self.conversation_history}

class AdvancedLangChainService(LangChainService):
    def __init__(self):
        super().__init__()
        self.available_tools = ["search", "calculator"]

    def _search_web(self, query):
        """Simple web search simulation"""
        return f"Search results for '{query}': This is a simulated search result. In production, integrate with a real search API."

    def _calculate(self, expression):
        """Simple calculator"""
        try:
            # Basic math operations only for safety
            allowed_chars = set('0123456789+-*/.() ')
            if all(c in allowed_chars for c in expression):
                result = eval(expression)
                return f"Calculation result: {result}"
            else:
                return "Invalid calculation expression"
        except:
            return "Calculation error"

    def run_agent(self, message, conversation_history=None):
        """Run agent with simulated tools"""
        try:
            # Check if message requires tools
            message_lower = message.lower()

            if any(word in message_lower for word in ['search', 'find', 'look up', 'google']):
                # Extract search query
                search_query = message.replace('search', '').replace('find', '').replace('look up', '').strip()
                tool_result = self._search_web(search_query)
                enhanced_message = f"{message}\n\nTool Result: {tool_result}"
            elif any(word in message_lower for word in ['calculate', 'compute', 'math', '+', '-', '*', '/']):
                # Extract calculation
                import re
                calc_match = re.search(r'[\d+\-*/().]+', message)
                if calc_match:
                    tool_result = self._calculate(calc_match.group())
                    enhanced_message = f"{message}\n\nTool Result: {tool_result}"
                else:
                    enhanced_message = message
            else:
                enhanced_message = message

            # Generate response with tool results
            return self.generate_response(enhanced_message, conversation_history)

        except Exception as e:
            return f"Agent error: {str(e)}"
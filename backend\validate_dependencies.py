#!/usr/bin/env python3
"""
Dependency Validation Script for Gemini AI Application
Validates all required packages for Python 3.10.10 compatibility
"""

import sys
import importlib

def test_import(module_name, package_name, required=True):
    """Test if a module can be imported"""
    try:
        importlib.import_module(module_name)
        return True, f"✓ {package_name}"
    except ImportError as e:
        status = "✗" if required else "⚠️"
        return False, f"{status} {package_name}: {str(e)}"

def main():
    print("🔍 Gemini AI Application - Dependency Validation")
    print("=" * 60)
    print(f"Python Version: {sys.version}")
    print("=" * 60)
    
    # Core required packages
    required_packages = [
        # Flask & Web
        ('flask', 'Flask'),
        ('flask_cors', 'Flask-CORS'),
        ('flask_sqlalchemy', 'Flask-SQLAlchemy'),
        ('werkzeug', 'Werkzeug'),
        
        # AI & ML
        ('google.generativeai', 'Google Generative AI'),
        ('langchain', '<PERSON><PERSON><PERSON><PERSON>'),
        ('sentence_transformers', 'Sentence Transformers'),
        ('torch', 'PyTorch'),
        ('transformers', 'Transformers'),
        
        # Database & Storage
        ('chromadb', 'ChromaDB'),
        ('psycopg2', 'psycopg2-binary'),
        
        # Document Processing
        ('fitz', 'PyMuPDF'),
        ('docx', 'python-docx'),
        ('openpyxl', 'openpyxl'),
        ('pptx', 'python-pptx'),
        ('bs4', 'beautifulsoup4'),
        ('pdfplumber', 'pdfplumber'),
        ('PIL', 'Pillow'),
        
        # Language & Translation
        ('langdetect', 'langdetect'),
        ('googletrans', 'googletrans'),
        ('gtts', 'gTTS'),
        
        # Live Data APIs
        ('yfinance', 'yfinance'),
        
        # Utilities
        ('requests', 'requests'),
        ('numpy', 'numpy'),
        ('pandas', 'pandas'),
        ('sklearn', 'scikit-learn'),
        ('dotenv', 'python-dotenv'),
        ('multipart', 'python-multipart'),
        ('tqdm', 'tqdm'),
        ('lxml', 'lxml'),
        
        # WebSocket & Real-time
        ('eventlet', 'eventlet'),
        ('flask_socketio', 'Flask-SocketIO'),
        
        # Export & File Generation
        ('reportlab', 'reportlab'),
        ('pydantic', 'pydantic'),
    ]
    
    # Optional packages
    optional_packages = [
        ('fasttext', 'fasttext (enhanced language detection)'),
        ('pypandoc', 'pypandoc (document conversion)'),
    ]
    
    success_count = 0
    total_required = len(required_packages)
    
    print("\n📦 Required Packages:")
    print("-" * 40)
    for module_name, package_name in required_packages:
        success, message = test_import(module_name, package_name, required=True)
        print(message)
        if success:
            success_count += 1
    
    print("\n📦 Optional Packages:")
    print("-" * 40)
    for module_name, package_name in optional_packages:
        success, message = test_import(module_name, package_name, required=False)
        print(message)
    
    print("\n" + "=" * 60)
    print(f"📊 Results: {success_count}/{total_required} required packages available")
    
    if success_count == total_required:
        print("✅ All required dependencies are available!")
        print("🎯 System is ready for Gemini AI Application")
        return True
    else:
        missing_count = total_required - success_count
        print(f"❌ {missing_count} required packages are missing")
        print("🔧 Please install missing packages using: pip install -r requirements.txt")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

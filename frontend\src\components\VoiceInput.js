import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Mic, MicOff, Square, Globe, ChevronDown } from 'lucide-react';
import { io } from 'socket.io-client';

const VoiceInput = ({ onTranscript, isDisabled }) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isSupported, setIsSupported] = useState(false);
  const [error, setError] = useState(null);
  const recognitionRef = useRef(null);
  const [transcript, setTranscript] = useState('');
  const [finalTranscript, setFinalTranscript] = useState('');
  const [detectedLanguage, setDetectedLanguage] = useState('en-US');
  const [confidence, setConfidence] = useState(0);
  const [selectedLanguage, setSelectedLanguage] = useState('auto');
  const [showLanguageDropdown, setShowLanguageDropdown] = useState(false);
  const [useEnhancedAPI, setUseEnhancedAPI] = useState(false);
  const [isProcessingAPI, setIsProcessingAPI] = useState(false);
  const mediaRecorderRef = useRef(null);
  const audioChunksRef = useRef([]);
  const [languageChangeNotification, setLanguageChangeNotification] = useState(null);

  // Server streaming fallback state
  const [isServerStreaming, setIsServerStreaming] = useState(false);
  const [serverStreamingError, setServerStreamingError] = useState(null);
  const socketRef = useRef(null);
  const streamRef = useRef(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showLanguageDropdown && !event.target.closest('.language-dropdown')) {
        setShowLanguageDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [showLanguageDropdown]);

  useEffect(() => {
    // Check if speech recognition is supported
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;

    if (SpeechRecognition) {
      setIsSupported(true);

      // Initialize speech recognition
      const recognition = new SpeechRecognition();
      recognition.continuous = true;
      recognition.interimResults = true;
      recognition.maxAlternatives = 3; // Increased for better accuracy

      // Enhanced language support with all major Indian languages
      const supportedLanguages = [
        'en-US', 'en-IN', 'hi-IN', 'ta-IN', 'te-IN', 'ml-IN', 'kn-IN',
        'bn-IN', 'gu-IN', 'mr-IN', 'pa-IN', 'or-IN', 'as-IN', 'ur-IN',
        'sa-IN', 'ne-IN', 'sd-IN', 'ks-IN', 'mai-IN', 'bho-IN'
      ];

      // Set initial language based on selection or browser language
      const browserLang = navigator.language || 'en-US';
      const initialLang = supportedLanguages.includes(browserLang) ? browserLang : 'en-US';

      // Enhanced language setting with fallback
      const setRecognitionLanguage = (langCode) => {
        try {
          recognition.lang = langCode;
          console.log(`Speech recognition language set to: ${langCode}`);
          return true;
        } catch (error) {
          console.warn(`Failed to set language ${langCode}, trying server fallback:`, error);
          // For Indian languages, try server fallback instead of falling back to English
          if (langCode.endsWith('-IN') && langCode !== 'en-IN') {
            console.log(`Attempting server fallback for Indian language: ${langCode}`);
            return false; // Signal that server fallback should be tried
          }
          recognition.lang = 'en-US';
          return false;
        }
      };

      setRecognitionLanguage(initialLang);
      setDetectedLanguage(initialLang);

      // Enhanced recognition settings for better Indian language support
      if (recognition.webkitGrammar !== undefined) {
        recognition.webkitGrammar = '';
      }

      // Improved settings for Indian languages
      recognition.grammars = null;
      recognition.serviceURI = null; // Disable cloud service timeout
      
      recognition.onstart = () => {
        setIsRecording(true);
        setError(null);
        setTranscript('');
        setFinalTranscript('');
      };

      recognition.onresult = (event) => {
        let newFinalTranscript = '';
        let interimTranscript = '';
        let maxConfidence = 0;

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const result = event.results[i];

          // Check all alternatives for better accuracy with Indian languages
          let bestTranscript = result[0].transcript;
          let bestConfidence = result[0].confidence || 0;

          // For Indian languages, sometimes alternatives are more accurate
          for (let j = 0; j < result.length; j++) {
            const alternative = result[j];
            const altConfidence = alternative.confidence || 0;

            if (altConfidence > bestConfidence) {
              bestTranscript = alternative.transcript;
              bestConfidence = altConfidence;
            }
          }

          maxConfidence = Math.max(maxConfidence, bestConfidence);

          if (result.isFinal) {
            newFinalTranscript += bestTranscript;
          } else {
            interimTranscript += bestTranscript;
          }
        }

        setConfidence(maxConfidence);

        // Accumulate final transcripts
        if (newFinalTranscript) {
          setFinalTranscript(prev => prev + newFinalTranscript);
        }

        // Show current interim transcript
        setTranscript(interimTranscript);

        // Enhanced language detection for Indian languages
        if (newFinalTranscript || interimTranscript) {
          const text = newFinalTranscript || interimTranscript;
          if (text.trim().length > 2) { // Only detect for meaningful text
            enhancedLanguageDetection(text);
          }
        }
      };
      
      recognition.onerror = (event) => {
        console.error('Speech recognition error:', event.error, 'Language:', recognition.lang);

        // Enhanced error handling for Indian languages
        if (event.error === 'language-not-supported') {
          console.warn(`Language ${recognition.lang} not supported, attempting server fallback`);
          setError(`${getLanguageName(recognition.lang)} not supported by browser, trying server streaming`);

          // For Indian languages, immediately try server fallback
          if (recognition.lang.endsWith('-IN') && recognition.lang !== 'en-IN') {
            if (startServerFallbackStreaming()) {
              return; // Successfully started server fallback
            }
          }

          // If server fallback fails or not Indian language, stop recording
          setIsRecording(false);
        } else if (shouldFallbackToServer(event)) {
          console.warn(`Browser error ${event.error}, attempting server fallback`);
          setError('Browser recognition failed, trying server streaming...');

          if (startServerFallbackStreaming()) {
            return; // Successfully started server fallback
          }

          setIsRecording(false);
        } else {
          setError(getErrorMessage(event.error));
          setIsRecording(false);

          // Auto-restart for certain errors, but try server fallback for Indian languages first
          if (event.error === 'network' || event.error === 'no-speech') {
            if (recognition.lang.endsWith('-IN') && recognition.lang !== 'en-IN') {
              // For Indian languages, try server fallback first
              if (startServerFallbackStreaming()) {
                return;
              }
            }

            setTimeout(() => {
              if (isRecording) {
                try {
                  recognition.start();
                } catch (e) {
                  console.log('Could not restart recognition:', e);
                }
              }
            }, 1000);
          }
        }
      };

      recognition.onend = () => {
        if (isRecording) {
          // Auto-restart if still recording (like test pages)
          try {
            recognition.start();
          } catch (e) {
            console.log('Recognition ended, attempting server fallback:', e);
            // Try server fallback if browser recognition fails
            if (startServerFallbackStreaming()) {
              return; // Successfully started server fallback
            }
            // If server fallback also fails, stop recording
            setIsRecording(false);
          }
        }
      };
      
      recognitionRef.current = recognition;
    } else {
      setIsSupported(false);
    }
    
    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
    };
  }, [onTranscript]);

  const detectLanguageFromText = (text) => {
    // Enhanced language detection based on Unicode character ranges and common words
    const languagePatterns = {
      'hi-IN': {
        pattern: /[\u0900-\u097F]/g,        // Devanagari (Hindi, Marathi, Sanskrit)
        keywords: ['है', 'हैं', 'का', 'की', 'के', 'में', 'से', 'को', 'और', 'या']
      },
      'ta-IN': {
        pattern: /[\u0B80-\u0BFF]/g,        // Tamil
        keywords: ['இல்', 'உள்ள', 'என்', 'அல்ல', 'மற்றும்', 'அது', 'இது']
      },
      'te-IN': {
        pattern: /[\u0C00-\u0C7F]/g,        // Telugu
        keywords: ['లో', 'కు', 'నుండి', 'మరియు', 'లేదా', 'అది', 'ఇది']
      },
      'ml-IN': {
        pattern: /[\u0D00-\u0D7F]/g,        // Malayalam
        keywords: ['ൽ', 'ന്', 'ും', 'അല്ല', 'ആണ്', 'ഇത്', 'അത്']
      },
      'kn-IN': {
        pattern: /[\u0C80-\u0CFF]/g,        // Kannada
        keywords: ['ಇದು', 'ಅದು', 'ಮತ್ತು', 'ಅಥವಾ', 'ಅಲ್ಲ', 'ಆಗಿದೆ']
      },
      'bn-IN': {
        pattern: /[\u0980-\u09FF]/g,        // Bengali/Assamese
        keywords: ['এই', 'সেই', 'এবং', 'বা', 'না', 'আছে', 'নেই']
      },
      'gu-IN': {
        pattern: /[\u0A80-\u0AFF]/g,        // Gujarati
        keywords: ['આ', 'તે', 'અને', 'કે', 'નથી', 'છે', 'હતું']
      },
      'pa-IN': {
        pattern: /[\u0A00-\u0A7F]/g,        // Gurmukhi (Punjabi)
        keywords: ['ਇਹ', 'ਉਹ', 'ਅਤੇ', 'ਜਾਂ', 'ਨਹੀਂ', 'ਹੈ', 'ਸੀ']
      },
      'or-IN': {
        pattern: /[\u0B00-\u0B7F]/g,        // Odia
        keywords: ['ଏହି', 'ସେହି', 'ଏବଂ', 'କିମ୍ବା', 'ନୁହେଁ', 'ଅଛି']
      },
      'ur-IN': {
        pattern: /[\u0600-\u06FF]/g,        // Arabic script (Urdu)
        keywords: ['یہ', 'وہ', 'اور', 'یا', 'نہیں', 'ہے', 'تھا']
      }
    };

    // Count characters and keywords for each language
    const languageScores = {};
    const textLength = text.length;
    const lowerText = text.toLowerCase();

    for (const [langCode, config] of Object.entries(languagePatterns)) {
      const matches = text.match(config.pattern) || [];
      const charScore = matches.length / textLength;

      // Check for common keywords
      let keywordScore = 0;
      for (const keyword of config.keywords) {
        if (lowerText.includes(keyword.toLowerCase())) {
          keywordScore += 0.1;
        }
      }

      languageScores[langCode] = charScore + keywordScore;
    }

    // Find language with highest score
    let detectedLang = 'en-US';
    let maxScore = 0;

    for (const [langCode, score] of Object.entries(languageScores)) {
      if (score > maxScore && score > 0.05) { // Lowered threshold for better detection
        maxScore = score;
        detectedLang = langCode;
      }
    }

    // Special handling for languages that share scripts
    if (detectedLang === 'hi-IN') {
      if (text.includes('मराठी') || text.includes('मी') || text.includes('आहे')) {
        detectedLang = 'mr-IN';
      } else if (text.includes('संस्कृत') || text.includes('श्लोक')) {
        detectedLang = 'sa-IN';
      }
    } else if (detectedLang === 'bn-IN' && (text.includes('অসমীয়া') || text.includes('আমি'))) {
      detectedLang = 'as-IN';
    }

    console.log(`Language detected: ${detectedLang} (score: ${maxScore.toFixed(3)}) for text: "${text.substring(0, 50)}..."`);
    setDetectedLanguage(detectedLang);

    // Auto-switch recognition language if different and not in auto mode
    if (selectedLanguage === 'auto' && recognitionRef.current && detectedLang !== recognitionRef.current.lang) {
      try {
        recognitionRef.current.lang = detectedLang;
        console.log(`Switched recognition language to: ${detectedLang}`);
      } catch (error) {
        console.log('Could not switch recognition language:', error);
      }
    }
  };

  // Enhanced language detection using backend service
  const detectLanguageWithBackend = async (text) => {
    try {
      const response = await fetch('/api/languages/detect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text: text })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.detected_language && result.confidence > 0.7) {
          // Map backend language codes to speech recognition codes
          const langMapping = {
            'hi': 'hi-IN',
            'ta': 'ta-IN',
            'te': 'te-IN',
            'ml': 'ml-IN',
            'kn': 'kn-IN',
            'bn': 'bn-IN',
            'gu': 'gu-IN',
            'mr': 'mr-IN',
            'pa': 'pa-IN',
            'or': 'or-IN',
            'as': 'as-IN',
            'ur': 'ur-IN',
            'en': 'en-IN',
            'ar': 'ar',
            'fa': 'fa',
            'zh': 'zh-CN',
            'ja': 'ja',
            'ko': 'ko',
            'es': 'es',
            'fr': 'fr',
            'de': 'de',
            'it': 'it',
            'pt': 'pt',
            'ru': 'ru'
          };

          const speechLangCode = langMapping[result.detected_language] || result.detected_language;

          if (speechLangCode !== detectedLanguage) {
            console.log(`Backend detected language: ${speechLangCode} (${Math.round(result.confidence * 100)}% confidence)`);

            // Show language change notification
            const oldLangName = getLanguageName(detectedLanguage);
            const newLangName = getLanguageName(speechLangCode);
            setLanguageChangeNotification(`${oldLangName} → ${newLangName}`);

            // Clear notification after 3 seconds
            setTimeout(() => setLanguageChangeNotification(null), 3000);

            setDetectedLanguage(speechLangCode);

            // Auto-switch recognition language if in auto mode
            if (selectedLanguage === 'auto' && recognitionRef.current) {
              try {
                recognitionRef.current.lang = speechLangCode;
                console.log(`Auto-switched recognition to: ${speechLangCode}`);
              } catch (error) {
                console.warn(`Could not switch to ${speechLangCode}:`, error);
              }
            }
          }
        }
      }
    } catch (error) {
      console.warn('Backend language detection failed:', error);
      // Fallback to client-side detection is already done
    }
  };

  // Enhanced detection that combines both methods
  const enhancedLanguageDetection = (text) => {
    // First, try client-side detection for immediate feedback
    detectLanguageFromText(text);

    // Then, use backend detection for better accuracy (async)
    if (text.trim().length > 10) { // Only for longer text
      detectLanguageWithBackend(text);
    }
  };

  const getLanguageName = (langCode) => {
    const languages = {
      'en-US': 'English',
      'en-IN': 'English (India)',
      'hi-IN': 'हिंदी (Hindi)',
      'ta-IN': 'தமிழ் (Tamil)',
      'te-IN': 'తెలుగు (Telugu)',
      'ml-IN': 'മലയാളം (Malayalam)',
      'kn-IN': 'ಕನ್ನಡ (Kannada)',
      'bn-IN': 'বাংলা (Bengali)',
      'gu-IN': 'ગુજરાતી (Gujarati)',
      'mr-IN': 'मराठी (Marathi)',
      'pa-IN': 'ਪੰਜਾਬੀ (Punjabi)',
      'or-IN': 'ଓଡ଼ିଆ (Odia)',
      'as-IN': 'অসমীয়া (Assamese)',
      'ur-IN': 'اردو (Urdu)',
      'sa-IN': 'संस्कृत (Sanskrit)',
      'ne-IN': 'नेपाली (Nepali)',
      'sd-IN': 'سنڌي (Sindhi)',
      'ks-IN': 'कॉशुर (Kashmiri)',
      'mai-IN': 'मैथिली (Maithili)',
      'bho-IN': 'भोजपुरी (Bhojpuri)',
      'ar': 'العربية (Arabic)',
      'fa': 'فارسی (Persian)',
      'zh-CN': '中文 (Chinese)',
      'ja': '日本語 (Japanese)',
      'ko': '한국어 (Korean)',
      'es': 'Español (Spanish)',
      'fr': 'Français (French)',
      'de': 'Deutsch (German)',
      'it': 'Italiano (Italian)',
      'pt': 'Português (Portuguese)',
      'ru': 'Русский (Russian)'
    };
    return languages[langCode] || 'Unknown';
  };

  const getAvailableLanguages = () => {
    return [
      { code: 'auto', name: '🌐 Auto-detect' },
      { code: 'en-US', name: 'English (US)' },
      { code: 'en-IN', name: 'English (India)' },
      { code: 'hi-IN', name: 'हिंदी (Hindi)' },
      { code: 'ta-IN', name: 'தமிழ் (Tamil)' },
      { code: 'te-IN', name: 'తెలుగు (Telugu)' },
      { code: 'ml-IN', name: 'മലയാളം (Malayalam)' },
      { code: 'kn-IN', name: 'ಕನ್ನಡ (Kannada)' },
      { code: 'bn-IN', name: 'বাংলা (Bengali)' },
      { code: 'gu-IN', name: 'ગુજરાતી (Gujarati)' },
      { code: 'mr-IN', name: 'मराठी (Marathi)' },
      { code: 'pa-IN', name: 'ਪੰਜਾਬੀ (Punjabi)' },
      { code: 'or-IN', name: 'ଓଡ଼ିଆ (Odia)' },
      { code: 'as-IN', name: 'অসমীয়া (Assamese)' },
      { code: 'ur-IN', name: 'اردو (Urdu)' },
      { code: 'sa-IN', name: 'संस्कृत (Sanskrit)' },
      { code: 'ne-IN', name: 'नेपाली (Nepali)' },
      { code: 'ar', name: 'العربية (Arabic)' },
      { code: 'fa', name: 'فارسی (Persian)' },
      { code: 'zh-CN', name: '中文 (Chinese)' },
      { code: 'ja', name: '日本語 (Japanese)' },
      { code: 'ko', name: '한국어 (Korean)' },
      { code: 'es', name: 'Español (Spanish)' },
      { code: 'fr', name: 'Français (French)' },
      { code: 'de', name: 'Deutsch (German)' },
      { code: 'it', name: 'Italiano (Italian)' },
      { code: 'pt', name: 'Português (Portuguese)' },
      { code: 'ru', name: 'Русский (Russian)' }
    ];
  };

  const handleLanguageChange = (langCode) => {
    setSelectedLanguage(langCode);
    setShowLanguageDropdown(false);

    if (recognitionRef.current) {
      if (langCode === 'auto') {
        // Reset to browser default or English for auto mode
        const browserLang = navigator.language || 'en-US';
        const supportedLanguages = [
          'en-US', 'en-IN', 'hi-IN', 'ta-IN', 'te-IN', 'ml-IN', 'kn-IN',
          'bn-IN', 'gu-IN', 'mr-IN', 'pa-IN', 'or-IN', 'as-IN', 'ur-IN'
        ];
        const fallbackLang = supportedLanguages.includes(browserLang) ? browserLang : 'en-US';

        try {
          recognitionRef.current.lang = fallbackLang;
          setDetectedLanguage(fallbackLang);
          console.log(`Auto mode: Set recognition language to ${fallbackLang}`);
        } catch (error) {
          console.log('Could not set auto language:', error);
        }
      } else {
        // Set specific language
        const langSetSuccess = setRecognitionLanguage(langCode);
        if (langSetSuccess) {
          setDetectedLanguage(langCode);
          console.log(`Manual mode: Set recognition language to ${langCode}`);
        } else {
          // For Indian languages, inform user that server fallback will be used
          if (langCode.endsWith('-IN') && langCode !== 'en-IN') {
            setDetectedLanguage(langCode); // Keep the selected language for server fallback
            setError(`${getLanguageName(langCode)} will use server streaming for better accuracy.`);
            console.log(`Language ${langCode} will use server fallback when recording starts`);
          } else {
            // Fallback to English for non-Indian languages
            recognitionRef.current.lang = 'en-US';
            setDetectedLanguage('en-US');
            setError(`Language ${getLanguageName(langCode)} not supported. Using English.`);
          }
        }
      }
    }
  };

  const transcribeWithAPI = async (audioBlob, language) => {
    try {
      setIsProcessingAPI(true);

      const formData = new FormData();
      formData.append('audio', audioBlob, 'recording.webm');
      formData.append('language', language);
      formData.append('provider', 'auto');

      const response = await fetch('http://localhost:5000/api/speech/transcribe', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();

      if (result.success) {
        return {
          transcript: result.transcript,
          confidence: result.confidence,
          provider: result.provider
        };
      } else {
        console.error('API transcription failed:', result.error);
        return null;
      }
    } catch (error) {
      console.error('Error calling transcription API:', error);
      return null;
    } finally {
      setIsProcessingAPI(false);
    }
  };

  // Helper function to determine if we should fallback to server
  const shouldFallbackToServer = (errorEvent) => {
    const fallbackErrors = [
      'language-not-supported',
      'not-allowed',
      'audio-capture',
      'aborted',
      'network',
      'no-speech'
    ];
    return fallbackErrors.includes(errorEvent.error);
  };

  // Server-side streaming fallback implementation (matching test pages)
  const startServerFallbackStreaming = () => {
    if (isServerStreaming) {
      console.log('Server streaming already active');
      return false;
    }

    try {
      console.log('Starting server-side streaming fallback...');
      setIsServerStreaming(true);
      setServerStreamingError(null);
      setError('Connecting to server streaming...');

      // Initialize Socket.IO connection (matching test_tamil_speech.html)
      const socket = io('http://localhost:5000', {
        transports: ['websocket', 'polling']
      });
      socketRef.current = socket;

      socket.on('connect', () => {
        console.log('Connected to streaming ASR server');
        const currentLang = selectedLanguage === 'auto' ? detectedLanguage : selectedLanguage;
        setError(`Connected, starting ${getLanguageName(currentLang)} streaming...`);

        // Start streaming session with current language
        socket.emit('start_streaming', { language: currentLang });
      });

      socket.on('session_started', (data) => {
        console.log('Streaming session started:', data);
        setError('Server streaming active');
        startAudioCapture();
      });

      socket.on('interim_transcript', (data) => {
        console.log('Server interim:', data.text);
        setTranscript(data.text);
      });

      socket.on('final_transcript', (data) => {
        console.log('Server final:', data.text);
        setFinalTranscript(prev => prev + data.text + ' ');
        if (data.text.trim()) {
          onTranscript(data.text.trim());
        }
        setTranscript(''); // Clear interim
      });

      socket.on('error', (data) => {
        console.error('Server streaming error:', data);
        setError(`Server error: ${data.message}`);
        stopServerFallbackStreaming();
      });

      socket.on('disconnect', () => {
        console.log('Disconnected from streaming ASR server');
        setError('Disconnected from server');
        stopServerFallbackStreaming();
      });

      return true;
    } catch (error) {
      console.error('Failed to start server fallback:', error);
      setError(`Server fallback failed: ${error.message}`);
      setIsServerStreaming(false);
      return false;
    }
  };

  // Start audio capture for server streaming (matching test pages)
  const startAudioCapture = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: 16000,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true
        }
      });

      streamRef.current = stream;

      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });

      mediaRecorderRef.current = mediaRecorder;

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0 && socketRef.current) {
          const reader = new FileReader();
          reader.onload = () => {
            socketRef.current.emit('audio_chunk', reader.result);
          };
          reader.readAsArrayBuffer(event.data);
        }
      };

      mediaRecorder.start(250); // 250ms chunks like test pages
      console.log('Audio capture started for server streaming');

    } catch (error) {
      console.error('Audio capture error:', error);
      setError('Failed to access microphone');
      stopServerFallbackStreaming();
    }
  };

  // Stop server-side streaming
  const stopServerFallbackStreaming = () => {
    console.log('Stopping server-side streaming...');

    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
      mediaRecorderRef.current.stop();
    }

    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }

    if (socketRef.current) {
      socketRef.current.emit('stop_streaming');
      socketRef.current.disconnect();
      socketRef.current = null;
    }

    setIsServerStreaming(false);
    setServerStreamingError(null);
  };



  const startEnhancedRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: 48000,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      });

      audioChunksRef.current = [];

      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = async () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
        const currentLang = selectedLanguage === 'auto' ? detectedLanguage : selectedLanguage;

        const result = await transcribeWithAPI(audioBlob, currentLang);
        if (result) {
          onTranscript(result.transcript);
          setConfidence(result.confidence);
        }

        // Stop all tracks
        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorderRef.current = mediaRecorder;
      mediaRecorder.start();
      setIsRecording(true);
      setError(null);

    } catch (error) {
      console.error('Error starting enhanced recording:', error);
      setError('Could not access microphone for enhanced recording');
    }
  };

  const stopEnhancedRecording = () => {
    if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };

  const getErrorMessage = (error) => {
    switch (error) {
      case 'no-speech':
        return 'No speech detected. Please try again.';
      case 'audio-capture':
        return 'Microphone not accessible. Please check permissions.';
      case 'not-allowed':
        return 'Microphone permission denied. Please allow microphone access.';
      case 'network':
        return 'Network error. Please check your connection.';
      case 'language-not-supported':
        return 'Language not supported. Switching to English.';
      default:
        return 'Speech recognition error. Please try again.';
    }
  };

  const startRecording = () => {
    if (!isSupported || !recognitionRef.current || isDisabled) return;

    try {
      // Ensure language is properly set before starting
      const currentLang = selectedLanguage === 'auto' ? detectedLanguage : selectedLanguage;
      if (recognitionRef.current.lang !== currentLang) {
        const langSetSuccess = setRecognitionLanguage(currentLang);
        if (!langSetSuccess && currentLang.endsWith('-IN') && currentLang !== 'en-IN') {
          // If setting Indian language failed, try server fallback immediately
          console.log(`Browser doesn't support ${currentLang}, starting server fallback`);
          if (startServerFallbackStreaming()) {
            return; // Successfully started server fallback
          }
        }
        console.log(`Starting recording with language: ${recognitionRef.current.lang}`);
      }

      recognitionRef.current.start();
    } catch (error) {
      console.error('Error starting recognition:', error);

      // For Indian languages, try server fallback first
      const currentLang = selectedLanguage === 'auto' ? detectedLanguage : selectedLanguage;
      if (currentLang.endsWith('-IN') && currentLang !== 'en-IN') {
        console.log(`Browser recognition failed for ${currentLang}, trying server fallback`);
        if (startServerFallbackStreaming()) {
          return; // Successfully started server fallback
        }
      }

      // If language-specific error, try with English as final fallback
      if (error.message && error.message.includes('language')) {
        try {
          recognitionRef.current.lang = 'en-US';
          setDetectedLanguage('en-US');
          recognitionRef.current.start();
          setError('Language not supported. Started with English.');
        } catch (fallbackError) {
          setError('Failed to start recording. Please try again.');
        }
      } else {
        setError('Failed to start recording. Please try again.');
      }
    }
  };

  const stopRecording = () => {
    if (isServerStreaming) {
      // Stop server streaming
      stopServerFallbackStreaming();
    } else if (recognitionRef.current && isRecording) {
      setIsRecording(false); // Set this first to prevent auto-restart
      recognitionRef.current.stop();

      // Send the accumulated transcript immediately
      const fullTranscript = (finalTranscript + transcript).trim();
      if (fullTranscript) {
        onTranscript(fullTranscript);
      }
      setTranscript('');
      setFinalTranscript('');
    }
    setIsRecording(false);
    setError(null); // Clear any errors when stopping
  };

  const toggleRecording = () => {
    if (isRecording || isServerStreaming) {
      if (useEnhancedAPI) {
        stopEnhancedRecording();
      } else {
        stopRecording();
      }
    } else {
      if (useEnhancedAPI) {
        startEnhancedRecording();
      } else {
        startRecording();
      }
    }
  };

  if (!isSupported) {
    return (
      <div className="relative group">
        <button
          disabled
          className="p-2 text-gray-300 cursor-not-allowed"
          title="Speech recognition not supported in this browser"
        >
          <MicOff size={20} />
        </button>
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
          Speech recognition not supported
        </div>
      </div>
    );
  }

  return (
    <div className="relative flex items-center gap-2">
      {/* Language Selection Dropdown */}
      <div className="relative language-dropdown">
        <button
          onClick={() => setShowLanguageDropdown(!showLanguageDropdown)}
          disabled={isDisabled || isRecording}
          className={`
            p-2 rounded-lg transition-all duration-200 flex items-center gap-1 text-xs
            ${isDisabled || isRecording
              ? 'opacity-50 cursor-not-allowed text-gray-400'
              : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'
            }
          `}
          title="Select language for voice input"
        >
          <Globe size={14} />
          <span className="hidden sm:inline">
            {selectedLanguage === 'auto' ? 'Auto' : getLanguageName(selectedLanguage).split(' ')[0]}
          </span>
          <ChevronDown size={12} />
        </button>

        {/* Language Dropdown */}
        <AnimatePresence>
          {showLanguageDropdown && (
            <motion.div
              initial={{ opacity: 0, y: -10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -10, scale: 0.95 }}
              className="absolute bottom-full left-0 mb-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-64 overflow-y-auto"
            >
              {getAvailableLanguages().map((lang) => (
                <button
                  key={lang.code}
                  onClick={() => handleLanguageChange(lang.code)}
                  className={`
                    w-full text-left px-3 py-2 text-sm hover:bg-blue-50 transition-colors
                    ${selectedLanguage === lang.code ? 'bg-blue-100 text-blue-700' : 'text-gray-700'}
                  `}
                >
                  {lang.name}
                </button>
              ))}

              {/* Enhanced API Toggle */}
              <div className="border-t border-gray-200 mt-2 pt-2">
                <label className="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={useEnhancedAPI}
                    onChange={(e) => setUseEnhancedAPI(e.target.checked)}
                    className="mr-2 rounded"
                  />
                  <span className="text-xs">
                    🚀 Enhanced Accuracy
                    <div className="text-gray-500 text-xs">Uses AI API for better results</div>
                  </span>
                </label>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={toggleRecording}
        disabled={isDisabled}
        className={`
          p-3 rounded-xl transition-all duration-200 relative
          ${(isRecording || isServerStreaming)
            ? isServerStreaming
              ? 'bg-orange-500 text-white hover:bg-orange-600 shadow-lg'
              : 'bg-red-500 text-white hover:bg-red-600 shadow-lg'
            : 'text-gray-500 hover:text-blue-600 hover:bg-blue-50'
          }
          ${isDisabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        `}
        title={
          isServerStreaming ? 'Stop server streaming' :
          isRecording ? 'Stop recording' :
          'Start voice input'
        }
      >
        <AnimatePresence mode="wait">
          {(isRecording || isServerStreaming) ? (
            <motion.div
              key="recording"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              exit={{ scale: 0 }}
            >
              <Square size={20} />
            </motion.div>
          ) : (
            <motion.div
              key="idle"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              exit={{ scale: 0 }}
            >
              <Mic size={20} />
            </motion.div>
          )}
        </AnimatePresence>

        {/* Enhanced recording indicator with pulse animation */}
        <AnimatePresence>
          {isRecording && (
            <>
              <motion.div
                initial={{ scale: 0, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0, opacity: 0 }}
                className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-ping"
              />
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                exit={{ scale: 0 }}
                className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"
              />
            </>
          )}
        </AnimatePresence>
      </motion.button>

      {/* Recording status text */}
      {isRecording && (
        <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-2 py-1 bg-red-500 text-white text-xs rounded whitespace-nowrap">
          {useEnhancedAPI ? 'Recording (Enhanced)... Click to stop' : 'Recording... Click to stop'}
        </div>
      )}

      {/* Server streaming status */}
      {isServerStreaming && (
        <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-2 py-1 bg-orange-500 text-white text-xs rounded whitespace-nowrap">
          Server Streaming ({getLanguageName(selectedLanguage === 'auto' ? detectedLanguage : selectedLanguage)})... Click to stop
        </div>
      )}

      {/* Real-time language detection indicator */}
      {selectedLanguage === 'auto' && detectedLanguage && detectedLanguage !== 'en-US' && !isRecording && !languageChangeNotification && (
        <motion.div
          initial={{ opacity: 0, y: 5 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 5 }}
          className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-2 py-1 bg-blue-500 text-white text-xs rounded whitespace-nowrap"
        >
          <Globe size={10} className="inline mr-1" />
          Detected: {getLanguageName(detectedLanguage).split(' ')[0]}
        </motion.div>
      )}

      {/* Language change notification */}
      <AnimatePresence>
        {languageChangeNotification && (
          <motion.div
            initial={{ opacity: 0, y: 5, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 5, scale: 0.9 }}
            className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-3 py-2 bg-green-500 text-white text-xs rounded-lg shadow-lg whitespace-nowrap"
          >
            <div className="flex items-center gap-1">
              <Globe size={12} />
              <span className="font-medium">Language switched:</span>
              <span>{languageChangeNotification}</span>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* API Processing status */}
      {isProcessingAPI && (
        <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-2 py-1 bg-blue-500 text-white text-xs rounded whitespace-nowrap">
          Processing with AI... Please wait
        </div>
      )}

      {/* Enhanced live transcript preview */}
      <AnimatePresence>
        {isRecording && (transcript || finalTranscript) && (
          <motion.div
            initial={{ opacity: 0, y: 10, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 10, scale: 0.9 }}
            className="absolute top-full left-1/2 transform -translate-x-1/2 mt-8 max-w-sm p-3 bg-gray-800 text-white text-sm rounded-xl shadow-xl"
          >
            {/* Enhanced language and confidence indicator */}
            <div className="flex items-center gap-2 mb-2 text-xs text-gray-400">
              <Globe size={12} />
              <span className="font-medium">{getLanguageName(detectedLanguage)}</span>
              {selectedLanguage === 'auto' && (
                <span className="px-1 py-0.5 bg-blue-600 text-blue-100 rounded text-xs">
                  AUTO
                </span>
              )}
              {confidence > 0 && (
                <span className={`ml-auto font-medium ${
                  confidence > 0.8 ? 'text-green-400' :
                  confidence > 0.6 ? 'text-yellow-400' : 'text-red-400'
                }`}>
                  {Math.round(confidence * 100)}%
                </span>
              )}
            </div>

            {/* Transcript text */}
            <div className="text-gray-300">
              {finalTranscript}
              <span className="text-blue-400">{transcript}</span>
              <motion.span
                animate={{ opacity: [1, 0] }}
                transition={{ duration: 1, repeat: Infinity }}
                className="text-white"
              >
                |
              </motion.span>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Live transcript display */}
      {isRecording && transcript && (
        <div className="absolute bottom-full left-0 mb-2 max-w-xs bg-white border border-gray-200 rounded-lg shadow-lg p-3 z-50">
          <div className="text-xs text-gray-500 mb-1">Listening...</div>
          <div className="text-sm text-gray-800">{transcript}</div>
        </div>
      )}

      {/* Error display */}
      {error && (
        <div className="absolute bottom-full left-0 mb-2 max-w-xs bg-red-50 border border-red-200 rounded-lg p-3 z-50">
          <div className="text-xs text-red-600">{error}</div>
          <button
            onClick={() => setError(null)}
            className="text-xs text-red-500 hover:text-red-700 mt-1"
          >
            Dismiss
          </button>
        </div>
      )}

      {/* Recording status */}
      {isRecording && (
        <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-3 py-1 bg-red-500 text-white text-xs rounded-full whitespace-nowrap">
          Recording... Click to stop
        </div>
      )}
    </div>
  );
};

export default VoiceInput;

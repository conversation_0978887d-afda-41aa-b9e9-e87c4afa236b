import os
import requests
import json
import datetime
import socket
from typing import Dict, List, Optional, Any
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from dotenv import load_dotenv

# Handle Flask context gracefully
try:
    from flask import current_app
    FLASK_AVAILABLE = True
except ImportError:
    FLASK_AVAILABLE = False
    current_app = None

# Load environment variables
load_dotenv(os.path.join(os.path.dirname(__file__), '..', '.env'))

def safe_log(level: str, message: str):
    """Safe logging that works both inside and outside Flask context"""
    try:
        if FLASK_AVAILABLE and current_app:
            getattr(current_app.logger, level)(message)
        else:
            print(f"[{level.upper()}] {message}")
    except RuntimeError:
        # Outside Flask context
        print(f"[{level.upper()}] {message}")

class LiveDataService:
    """
    Service for fetching real-time data from external APIs
    Supports: News (GNews), Weather (Open-Meteo), Stocks (Yahoo Finance), Cricket (CricAPI)
    """

    def __init__(self):
        # API Keys from environment
        self.gnews_api_key = os.getenv("NEWS_API_KEY")  # Using NEWS_API_KEY from .env
        self.cricket_api_key = os.getenv("CRICKET_API_KEY")
        
        # Primary API endpoints
        self.gnews_url = "https://gnews.io/api/v4/top-headlines"
        self.newsapi_url = "https://newsapi.org/v2/top-headlines"
        self.weather_url = "https://api.open-meteo.com/v1/forecast"
        self.cricket_url = "https://api.cricapi.com/v1/currentMatches"

        # Backup API endpoints
        self.backup_weather_url = "https://wttr.in/{city}?format=j1"
        self.backup_news_url = "https://gnews.io/api/v4/search"  # Alternative GNews endpoint
        self.backup_stock_url = "https://financialmodelingprep.com/api/v3/quote-short/{symbol}"
        
        # City coordinates for weather
        self.city_coords = {
            'chennai': {'lat': 13.08, 'lon': 80.27},
            'delhi': {'lat': 28.61, 'lon': 77.23},
            'mumbai': {'lat': 19.07, 'lon': 72.87},
            'bangalore': {'lat': 12.97, 'lon': 77.59},
            'kolkata': {'lat': 22.57, 'lon': 88.36},
            'hyderabad': {'lat': 17.37, 'lon': 78.48},
            'pune': {'lat': 18.52, 'lon': 73.86}
        }
        
        # Stock symbols mapping
        self.stock_symbols = {
            'tcs': 'TCS.NS',
            'infosys': 'INFY.NS',
            'reliance': 'RELIANCE.NS',
            'hdfc': 'HDFCBANK.NS',
            'icici': 'ICICIBANK.NS',
            'wipro': 'WIPRO.NS',
            'bharti': 'BHARTIARTL.NS',
            'itc': 'ITC.NS',
            'sbi': 'SBIN.NS',
            'adani': 'ADANIENT.NS'
        }

        # Configure DNS and connection settings for reliability
        socket.setdefaulttimeout(15)  # Increase timeout for DNS resolution

        # Create resilient session with retries
        self.session = requests.Session()
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS"]
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)

        # Set headers to avoid blocking
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })

        # Test DNS resolution on initialization
        self._test_dns_connectivity()

    def _test_dns_connectivity(self):
        """Test DNS resolution for all API hosts"""
        api_hosts = [
            "api.open-meteo.com",
            "newsapi.org",
            "gnews.io",
            "api.cricapi.com",
            "query2.finance.yahoo.com"
        ]

        safe_log("info", "🌐 Testing DNS resolution for external APIs...")

        for host in api_hosts:
            try:
                ip = socket.gethostbyname(host)
                safe_log("info", f"✅ DNS OK for {host} -> {ip}")
            except socket.gaierror as e:
                safe_log("warning", f"⚠️ DNS FAILED for {host}: {e}")
                # Try basic connectivity test
                try:
                    # Test basic socket connection
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(5)
                    result = sock.connect_ex((host, 80))
                    sock.close()
                    if result == 0:
                        safe_log("info", f"✅ Connection OK for {host}")
                    else:
                        safe_log("warning", f"⚠️ Connection failed for {host}")
                except Exception:
                    safe_log("error", f"❌ Complete connectivity failure for {host}")

    def detect_intent(self, query: str) -> Optional[str]:
        """
        Detect user intent from query to route to appropriate API
        """
        query_lower = query.lower()
        
        # News keywords
        news_keywords = ["news", "headline", "breaking", "latest news", "current news", "today news"]
        if any(keyword in query_lower for keyword in news_keywords):
            return "news"
        
        # Weather keywords
        weather_keywords = ["weather", "temperature", "climate", "rain", "forecast", "humidity"]
        if any(keyword in query_lower for keyword in weather_keywords):
            return "weather"
        
        # Stock keywords
        stock_keywords = ["stock", "market", "share", "price", "trading", "nse", "bse"]
        if any(keyword in query_lower for keyword in stock_keywords):
            return "stocks"
        
        # Cricket keywords
        cricket_keywords = ["cricket", "match", "score", "ipl", "test", "odi", "t20"]
        if any(keyword in query_lower for keyword in cricket_keywords):
            return "cricket"
        
        return None

    def fetch_news(self, query: str = "") -> Dict[str, Any]:
        """
        Fetch complete, detailed news from NewsAPI - NO SUMMARIZATION
        Returns ALL available data fields without truncation
        """
        try:
            safe_log("info", "📰 Fetching COMPLETE news data from NewsAPI (No Summarization)")

            # Use NewsAPI with the key from environment - fetch MORE articles for comprehensive coverage
            params = {
                "country": "in",
                "pageSize": 20,  # Increased from 5 to get more comprehensive coverage
                "apiKey": self.gnews_api_key,
                "sortBy": "publishedAt"
            }

            # Add search query if provided
            if query and any(word in query.lower() for word in ["search", "about", "on"]):
                # Extract search terms
                search_terms = query.lower().replace("news about", "").replace("news on", "").strip()
                if search_terms:
                    params["q"] = search_terms

            response = self.session.get(self.newsapi_url, params=params, timeout=15)

            if response.status_code == 200:
                data = response.json()
                articles = data.get("articles", [])  # Return ALL articles, no truncation

                if articles:
                    news_data = []
                    for article in articles:
                        # Include ALL available fields - complete data without summarization
                        news_item = {
                            "title": article.get("title", "No title"),
                            "description": article.get("description", "No description"),  # Full description
                            "content": article.get("content", "No content"),  # Full content
                            "source": {
                                "id": article.get("source", {}).get("id", "unknown"),
                                "name": article.get("source", {}).get("name", "Unknown")
                            },
                            "author": article.get("author", "Unknown author"),
                            "url": article.get("url", ""),
                            "urlToImage": article.get("urlToImage", ""),
                            "publishedAt": article.get("publishedAt", ""),
                            "category": article.get("category", "general"),
                            # Additional metadata for comprehensive coverage
                            "language": "en",
                            "country": "in",
                            "fetched_at": datetime.datetime.now().isoformat(),
                            "api_source": "newsapi"
                        }
                        news_data.append(news_item)

                    safe_log("info", f"✅ Successfully fetched {len(news_data)} COMPLETE news articles (No Summarization)")
                    return {
                        "success": True,
                        "data": news_data,
                        "source": "newsapi",
                        "timestamp": datetime.datetime.now().isoformat(),
                        "total_articles": len(news_data),
                        "query_used": query,
                        "api_response_status": response.status_code,
                        "comprehensive_data": True,
                        "summarization_applied": False,  # Explicitly indicate no summarization
                        "raw_api_response": data  # Include complete raw response for transparency
                    }
                else:
                    safe_log("warning", "NewsAPI returned no articles")
                    return {"success": False, "error": "No articles found", "raw_response": data}

            safe_log("warning", f"NewsAPI returned status {response.status_code}")
            return {"success": False, "error": f"NewsAPI error: {response.status_code}", "status_code": response.status_code}

        except Exception as e:
            safe_log("error", f"News API failed: {e}")
            # Try backup news API
            safe_log("info", "🔄 Trying backup news API (GNews search) - COMPLETE DATA")
            return self._fetch_news_backup(query)

    def _fetch_news_backup(self, query: str = "") -> Dict[str, Any]:
        """
        Backup news fetch using GNews search API - COMPLETE DATA, NO SUMMARIZATION
        """
        try:
            if not self.gnews_api_key:
                safe_log("warning", "GNews API key not configured for backup")
                return {"success": False, "error": "GNews API key not configured"}

            params = {
                "q": "India",  # Default search term
                "lang": "en",
                "country": "in",
                "max": 20,  # Increased from 5 for comprehensive coverage
                "apikey": self.gnews_api_key
            }

            # Add specific search terms if provided
            if query and any(word in query.lower() for word in ["search", "about", "on"]):
                search_terms = query.lower().replace("news about", "").replace("news on", "").strip()
                if search_terms:
                    params["q"] = search_terms

            response = self.session.get(self.backup_news_url, params=params, timeout=15)

            if response.status_code == 200:
                data = response.json()
                articles = data.get("articles", [])  # Return ALL articles, no truncation

                if articles:
                    news_data = []
                    for article in articles:
                        news_item = {
                            "title": article.get("title", "No title"),
                            "description": article.get("description", "No description"),
                            "source": article.get("source", {}).get("name", "Unknown"),
                            "url": article.get("url", ""),
                            "publishedAt": article.get("publishedAt", "")
                        }
                        news_data.append(news_item)

                    safe_log("info", f"✅ Successfully fetched {len(news_data)} news articles via backup")
                    return {
                        "success": True,
                        "data": news_data,
                        "source": "gnews_backup",
                        "timestamp": datetime.datetime.now().isoformat()
                    }
                else:
                    safe_log("warning", "Backup news API returned no articles")
                    return {"success": False, "error": "No articles found in backup"}
            else:
                safe_log("warning", f"Backup news API returned status {response.status_code}")
                return {"success": False, "error": f"Backup news API error: {response.status_code}"}

        except Exception as e:
            safe_log("error", f"Backup news API failed: {e}")
            return {"success": False, "error": f"Backup news fetch failed: {e}"}

    def fetch_weather(self, query: str = "Chennai") -> Dict[str, Any]:
        """
        Fetch COMPLETE weather data from Open-Meteo API - NO SUMMARIZATION
        Returns ALL available weather metrics and forecasts
        """
        try:
            safe_log("info", "🌦️ Fetching COMPLETE weather data from Open-Meteo (No Summarization)")

            # Extract city from query
            city = "chennai"  # default
            for city_name in self.city_coords.keys():
                if city_name in query.lower():
                    city = city_name
                    break

            coords = self.city_coords[city]

            # Comprehensive weather parameters - ALL available data
            params = {
                "latitude": coords["lat"],
                "longitude": coords["lon"],
                "current_weather": True,
                # Comprehensive hourly data for detailed analysis
                "hourly": "temperature_2m,relative_humidity_2m,precipitation,precipitation_probability,rain,showers,snowfall,snow_depth,weather_code,pressure_msl,surface_pressure,cloud_cover,cloud_cover_low,cloud_cover_mid,cloud_cover_high,visibility,evapotranspiration,et0_fao_evapotranspiration,vapour_pressure_deficit,wind_speed_10m,wind_speed_80m,wind_speed_120m,wind_speed_180m,wind_direction_10m,wind_direction_80m,wind_direction_120m,wind_direction_180m,wind_gusts_10m,temperature_80m,temperature_120m,temperature_180m,soil_temperature_0cm,soil_temperature_6cm,soil_temperature_18cm,soil_temperature_54cm,soil_moisture_0_1cm,soil_moisture_1_3cm,soil_moisture_3_9cm,soil_moisture_9_27cm,soil_moisture_27_81cm",
                # Comprehensive daily data
                "daily": "weather_code,temperature_2m_max,temperature_2m_min,apparent_temperature_max,apparent_temperature_min,sunrise,sunset,daylight_duration,sunshine_duration,uv_index_max,uv_index_clear_sky_max,precipitation_sum,rain_sum,showers_sum,snowfall_sum,precipitation_hours,precipitation_probability_max,precipitation_probability_min,precipitation_probability_mean,wind_speed_10m_max,wind_gusts_10m_max,wind_direction_10m_dominant,shortwave_radiation_sum,et0_fao_evapotranspiration",
                "timezone": "Asia/Kolkata",
                "forecast_days": 7,  # Extended forecast for comprehensive data
                "past_days": 1  # Include yesterday's data for context
            }

            response = self.session.get(self.weather_url, params=params, timeout=15)

            if response.status_code == 200:
                data = response.json()
                current_weather = data.get("current_weather", {})
                hourly_data = data.get("hourly", {})
                daily_data = data.get("daily", {})

                # Comprehensive weather data - ALL fields included
                weather_data = {
                    "city": city.title(),
                    "coordinates": {
                        "latitude": coords["lat"],
                        "longitude": coords["lon"]
                    },
                    "timezone": data.get("timezone", "Asia/Kolkata"),
                    "timezone_abbreviation": data.get("timezone_abbreviation", "IST"),
                    "elevation": data.get("elevation", "N/A"),
                    "generationtime_ms": data.get("generationtime_ms", "N/A"),

                    # Current weather - complete data
                    "current_weather": {
                        "temperature": current_weather.get("temperature", "N/A"),
                        "windspeed": current_weather.get("windspeed", "N/A"),
                        "winddirection": current_weather.get("winddirection", "N/A"),
                        "weathercode": current_weather.get("weathercode", "N/A"),
                        "is_day": current_weather.get("is_day", "N/A"),
                        "time": current_weather.get("time", "N/A")
                    },

                    # Complete hourly forecast data
                    "hourly_forecast": hourly_data,

                    # Complete daily forecast data
                    "daily_forecast": daily_data,

                    # Additional metadata
                    "data_completeness": "full",
                    "summarization_applied": False,
                    "api_source": "open_meteo",
                    "query_used": query,
                    "fetched_at": datetime.datetime.now().isoformat()
                }

                safe_log("info", f"✅ Successfully fetched COMPLETE weather data for {city} (No Summarization)")
                return {
                    "success": True,
                    "data": weather_data,
                    "source": "open_meteo",
                    "timestamp": datetime.datetime.now().isoformat(),
                    "comprehensive_data": True,
                    "summarization_applied": False,
                    "raw_api_response": data  # Include complete raw response
                }

            safe_log("warning", f"Weather API returned status {response.status_code}")
            return {"success": False, "error": f"Weather API error: {response.status_code}", "status_code": response.status_code}

        except Exception as e:
            safe_log("error", f"Weather API failed: {e}")
            # Try backup weather API
            safe_log("info", "🔄 Trying backup weather API (wttr.in) - COMPLETE DATA")
            return self._fetch_weather_backup(query)

    def _fetch_weather_backup(self, query: str = "Chennai") -> Dict[str, Any]:
        """
        Backup weather fetch using wttr.in API
        """
        try:
            # Extract city from query
            city = "chennai"  # default
            for city_name in self.city_coords.keys():
                if city_name in query.lower():
                    city = city_name
                    break

            url = self.backup_weather_url.format(city=city)
            response = self.session.get(url, timeout=15)

            if response.status_code == 200:
                data = response.json()
                current = data.get("current_condition", [{}])[0]

                weather_data = {
                    "city": city.title(),
                    "temperature": float(current.get("temp_C", 0)),
                    "windspeed": float(current.get("windspeedKmph", 0)),
                    "winddirection": current.get("winddirDegree", "N/A"),
                    "weathercode": current.get("weatherCode", "N/A"),
                    "description": current.get("weatherDesc", [{}])[0].get("value", "N/A"),
                    "time": datetime.datetime.now().strftime("%Y-%m-%dT%H:%M")
                }

                safe_log("info", f"✅ Successfully fetched weather for {city} via backup API")
                return {
                    "success": True,
                    "data": weather_data,
                    "source": "wttr_in_backup",
                    "timestamp": datetime.datetime.now().isoformat()
                }
            else:
                safe_log("warning", f"Backup weather API returned status {response.status_code}")
                return {"success": False, "error": f"Backup weather API error: {response.status_code}"}

        except Exception as e:
            safe_log("error", f"Backup weather API failed: {e}")
            return {"success": False, "error": f"Backup weather fetch failed: {e}"}

    def fetch_stock(self, query: str = "TCS") -> Dict[str, Any]:
        """
        Fetch COMPLETE stock data using yfinance - NO SUMMARIZATION
        Returns ALL available financial metrics and historical data
        """
        try:
            safe_log("info", "💹 Fetching COMPLETE stock data via yfinance (No Summarization)")

            # Extract stock symbol from query
            symbol = "TCS.NS"  # default
            for stock_name, stock_symbol in self.stock_symbols.items():
                if stock_name in query.lower():
                    symbol = stock_symbol
                    break

            # Use yfinance library for comprehensive data
            import yfinance as yf

            ticker = yf.Ticker(symbol)
            info = ticker.info
            hist = ticker.history(period="1mo")  # Extended history for comprehensive analysis

            # Get additional data
            dividends = ticker.dividends
            splits = ticker.splits
            actions = ticker.actions
            calendar = ticker.calendar
            recommendations = ticker.recommendations

            if not hist.empty and info:
                last_price = hist['Close'].iloc[-1]
                prev_close = info.get('previousClose', last_price)
                change = last_price - prev_close
                change_percent = (change / prev_close) * 100 if prev_close else 0

                # COMPREHENSIVE stock data - ALL available fields
                stock_data = {
                    # Basic Information
                    "symbol": symbol,
                    "shortName": info.get("shortName", info.get("longName", "Unknown")),
                    "longName": info.get("longName", "Unknown"),
                    "sector": info.get("sector", "Unknown"),
                    "industry": info.get("industry", "Unknown"),
                    "country": info.get("country", "Unknown"),
                    "website": info.get("website", ""),
                    "business_summary": info.get("longBusinessSummary", "No summary available"),

                    # Current Price Data
                    "regularMarketPrice": round(last_price, 2),
                    "regularMarketChange": round(change, 2),
                    "regularMarketChangePercent": round(change_percent, 2),
                    "currency": info.get("currency", "INR"),
                    "marketState": "REGULAR" if hist.index[-1].date() == datetime.date.today() else "CLOSED",

                    # Market Data
                    "marketCap": info.get("marketCap", "N/A"),
                    "enterpriseValue": info.get("enterpriseValue", "N/A"),
                    "sharesOutstanding": info.get("sharesOutstanding", "N/A"),
                    "floatShares": info.get("floatShares", "N/A"),
                    "impliedSharesOutstanding": info.get("impliedSharesOutstanding", "N/A"),

                    # Price Ranges
                    "dayLow": info.get("dayLow", "N/A"),
                    "dayHigh": info.get("dayHigh", "N/A"),
                    "fiftyTwoWeekLow": info.get("fiftyTwoWeekLow", "N/A"),
                    "fiftyTwoWeekHigh": info.get("fiftyTwoWeekHigh", "N/A"),
                    "fiftyDayAverage": info.get("fiftyDayAverage", "N/A"),
                    "twoHundredDayAverage": info.get("twoHundredDayAverage", "N/A"),

                    # Volume Data
                    "volume": info.get("volume", "N/A"),
                    "averageVolume": info.get("averageVolume", "N/A"),
                    "averageVolume10days": info.get("averageVolume10days", "N/A"),
                    "averageDailyVolume10Day": info.get("averageDailyVolume10Day", "N/A"),

                    # Financial Ratios
                    "priceToEarningsRatio": info.get("trailingPE", "N/A"),
                    "forwardPE": info.get("forwardPE", "N/A"),
                    "priceToBook": info.get("priceToBook", "N/A"),
                    "priceToSalesTrailing12Months": info.get("priceToSalesTrailing12Months", "N/A"),
                    "enterpriseToRevenue": info.get("enterpriseToRevenue", "N/A"),
                    "enterpriseToEbitda": info.get("enterpriseToEbitda", "N/A"),
                    "beta": info.get("beta", "N/A"),

                    # Earnings Data
                    "earningsPerShare": info.get("trailingEps", "N/A"),
                    "forwardEps": info.get("forwardEps", "N/A"),
                    "pegRatio": info.get("pegRatio", "N/A"),
                    "earningsGrowth": info.get("earningsGrowth", "N/A"),
                    "revenueGrowth": info.get("revenueGrowth", "N/A"),

                    # Dividend Information
                    "dividendRate": info.get("dividendRate", "N/A"),
                    "dividendYield": info.get("dividendYield", "N/A"),
                    "exDividendDate": info.get("exDividendDate", "N/A"),
                    "payoutRatio": info.get("payoutRatio", "N/A"),

                    # Financial Health
                    "totalCash": info.get("totalCash", "N/A"),
                    "totalCashPerShare": info.get("totalCashPerShare", "N/A"),
                    "totalDebt": info.get("totalDebt", "N/A"),
                    "debtToEquity": info.get("debtToEquity", "N/A"),
                    "returnOnAssets": info.get("returnOnAssets", "N/A"),
                    "returnOnEquity": info.get("returnOnEquity", "N/A"),
                    "grossMargins": info.get("grossMargins", "N/A"),
                    "operatingMargins": info.get("operatingMargins", "N/A"),
                    "profitMargins": info.get("profitMargins", "N/A"),

                    # Revenue Data
                    "totalRevenue": info.get("totalRevenue", "N/A"),
                    "revenuePerShare": info.get("revenuePerShare", "N/A"),
                    "grossProfits": info.get("grossProfits", "N/A"),
                    "freeCashflow": info.get("freeCashflow", "N/A"),
                    "operatingCashflow": info.get("operatingCashflow", "N/A"),

                    # Historical Data (Complete)
                    "historical_prices": hist.to_dict('records'),
                    "dividend_history": dividends.to_dict() if not dividends.empty else {},
                    "stock_splits": splits.to_dict() if not splits.empty else {},
                    "corporate_actions": actions.to_dict('records') if not actions.empty else [],

                    # Additional Data
                    "recommendations": recommendations.to_dict('records') if recommendations is not None and not recommendations.empty else [],
                    "earnings_calendar": calendar.to_dict('records') if calendar is not None and not calendar.empty else [],

                    # Metadata
                    "data_completeness": "comprehensive",
                    "summarization_applied": False,
                    "api_source": "yfinance",
                    "query_used": query,
                    "fetched_at": datetime.datetime.now().isoformat(),
                    "raw_info": info  # Complete raw info for transparency
                }

                safe_log("info", f"✅ Successfully fetched COMPLETE stock data for {symbol} (No Summarization)")
                return {
                    "success": True,
                    "data": stock_data,
                    "source": "yfinance",
                    "timestamp": datetime.datetime.now().isoformat(),
                    "comprehensive_data": True,
                    "summarization_applied": False
                }
            else:
                safe_log("warning", f"No stock data found for {symbol}")
                return {"success": False, "error": f"No data found for {symbol}", "symbol_searched": symbol}

        except Exception as e:
            safe_log("error", f"Stock API failed: {e}")
            return {"success": False, "error": f"Stock fetch failed: {e}", "exception_details": str(e)}

    def fetch_cricket(self, query: str = "") -> Dict[str, Any]:
        """
        Fetch COMPLETE cricket data from CricAPI - NO SUMMARIZATION
        Returns ALL available match details, scores, and statistics
        """
        try:
            safe_log("info", "🏏 Fetching COMPLETE cricket data from CricAPI (No Summarization)")

            if not self.cricket_api_key:
                safe_log("warning", "Cricket API key not configured")
                return {"success": False, "error": "Cricket API key not configured"}

            params = {
                "apikey": self.cricket_api_key,
                "offset": 0
            }

            response = self.session.get(self.cricket_url, params=params, timeout=15)

            if response.status_code == 200:
                data = response.json()
                matches = data.get("data", [])  # Return ALL matches, no truncation

                if matches:
                    cricket_data = []
                    for match in matches:
                        # COMPREHENSIVE match information - ALL available fields
                        match_info = {
                            # Basic Match Information
                            "id": match.get("id", "Unknown ID"),
                            "name": match.get("name", "Unknown Match"),
                            "matchType": match.get("matchType", "Unknown Type"),
                            "status": match.get("status", "Unknown"),
                            "venue": match.get("venue", "Unknown Venue"),
                            "date": match.get("date", "Unknown Date"),
                            "dateTimeGMT": match.get("dateTimeGMT", "Unknown GMT"),

                            # Team Information
                            "teams": match.get("teams", []),
                            "teamInfo": match.get("teamInfo", []),

                            # Score Information (Complete)
                            "score": match.get("score", []),
                            "series_id": match.get("series_id", "Unknown Series"),
                            "fantasyEnabled": match.get("fantasyEnabled", False),
                            "bbbEnabled": match.get("bbbEnabled", False),
                            "hasSquad": match.get("hasSquad", False),
                            "matchStarted": match.get("matchStarted", False),
                            "matchEnded": match.get("matchEnded", False),

                            # Additional Match Details
                            "t1": match.get("t1", "Unknown Team 1"),
                            "t2": match.get("t2", "Unknown Team 2"),
                            "t1s": match.get("t1s", "No Score"),
                            "t2s": match.get("t2s", "No Score"),

                            # Match State
                            "tossWinner": match.get("tossWinner", "Unknown"),
                            "tossChoice": match.get("tossChoice", "Unknown"),
                            "matchWinner": match.get("matchWinner", "Unknown"),
                            "result": match.get("result", "Unknown Result"),

                            # Tournament Information
                            "series": match.get("series", "Unknown Series"),
                            "tournament": match.get("tournament", "Unknown Tournament"),

                            # Timing Information
                            "startDate": match.get("startDate", "Unknown Start Date"),
                            "endDate": match.get("endDate", "Unknown End Date"),
                            "timezone": match.get("timezone", "Unknown Timezone"),

                            # Additional Metadata
                            "squads": match.get("squads", []),
                            "players": match.get("players", []),
                            "officials": match.get("officials", []),

                            # API Metadata
                            "api_source": "cricapi",
                            "fetched_at": datetime.datetime.now().isoformat(),
                            "raw_match_data": match  # Include complete raw data
                        }
                        cricket_data.append(match_info)

                    safe_log("info", f"✅ Successfully fetched {len(cricket_data)} COMPLETE cricket matches (No Summarization)")
                    return {
                        "success": True,
                        "data": cricket_data,
                        "source": "cricapi",
                        "timestamp": datetime.datetime.now().isoformat(),
                        "total_matches": len(cricket_data),
                        "comprehensive_data": True,
                        "summarization_applied": False,
                        "query_used": query,
                        "raw_api_response": data  # Include complete raw response
                    }

            safe_log("warning", f"Cricket API returned status {response.status_code}")
            return {"success": False, "error": f"Cricket API error: {response.status_code}", "status_code": response.status_code}

        except Exception as e:
            safe_log("error", f"Cricket API failed: {e}")
            return {"success": False, "error": f"Cricket fetch failed: {e}", "exception_details": str(e)}

    def fetch_live_data(self, query: str) -> Dict[str, Any]:
        """
        Main method to fetch live data based on detected intent
        """
        intent = self.detect_intent(query)

        if intent == "news":
            result = self.fetch_news(query)
            result["intent"] = intent
            return result
        elif intent == "weather":
            result = self.fetch_weather(query)
            result["intent"] = intent
            return result
        elif intent == "stocks":
            result = self.fetch_stock(query)
            result["intent"] = intent
            return result
        elif intent == "cricket":
            result = self.fetch_cricket(query)
            result["intent"] = intent
            return result
        else:
            return {"success": False, "error": "No matching intent detected", "intent": None}

import os
import uuid
import tempfile
from io import BytesIO
import pandas as pd
from docx import Document
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
import pdfplumber


class FileConversionService:
    def __init__(self):
        self.temp_dir = tempfile.gettempdir()
        
    def convert_pdf_to_docx(self, pdf_path, output_filename=None):
        """Convert PDF to DOCX format"""
        try:
            if not output_filename:
                output_filename = f"converted_{uuid.uuid4()}.docx"
            
            output_path = os.path.join(self.temp_dir, output_filename)
            
            # Extract text from PDF
            text_content = ""
            with pdfplumber.open(pdf_path) as pdf:
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text_content += page_text + "\n\n"
                    
                    # Extract tables
                    tables = page.extract_tables()
                    for table in tables:
                        if table:
                            for row in table:
                                if row:
                                    text_content += " | ".join(str(cell) if cell else "" for cell in row) + "\n"
                            text_content += "\n"
            
            # Create DOCX document
            doc = Document()
            doc.add_heading('Converted from PDF', 0)
            
            # Add content
            paragraphs = text_content.split('\n\n')
            for paragraph in paragraphs:
                if paragraph.strip():
                    doc.add_paragraph(paragraph.strip())
            
            doc.save(output_path)
            return output_path
            
        except Exception as e:
            raise Exception(f"Error converting PDF to DOCX: {str(e)}")
    
    def convert_docx_to_pdf(self, docx_path, output_filename=None):
        """Convert DOCX to PDF format"""
        try:
            if not output_filename:
                output_filename = f"converted_{uuid.uuid4()}.pdf"
            
            output_path = os.path.join(self.temp_dir, output_filename)
            
            # Extract text from DOCX
            doc = Document(docx_path)
            content = []
            
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    content.append(paragraph.text)
            
            # Extract tables
            for table in doc.tables:
                table_data = []
                for row in table.rows:
                    row_data = []
                    for cell in row.cells:
                        row_data.append(cell.text.strip())
                    table_data.append(row_data)
                content.append(table_data)
            
            # Create PDF
            pdf_doc = SimpleDocTemplate(output_path, pagesize=letter)
            styles = getSampleStyleSheet()
            story = []
            
            # Add title
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=16,
                spaceAfter=30,
            )
            story.append(Paragraph("Converted from DOCX", title_style))
            story.append(Spacer(1, 12))
            
            # Add content
            for item in content:
                if isinstance(item, str):
                    # Regular paragraph
                    story.append(Paragraph(item, styles['Normal']))
                    story.append(Spacer(1, 12))
                elif isinstance(item, list):
                    # Table
                    if item and len(item) > 0:
                        table = Table(item)
                        table.setStyle(TableStyle([
                            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                            ('FONTSIZE', (0, 0), (-1, 0), 14),
                            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                            ('GRID', (0, 0), (-1, -1), 1, colors.black)
                        ]))
                        story.append(table)
                        story.append(Spacer(1, 12))
            
            pdf_doc.build(story)
            return output_path
            
        except Exception as e:
            raise Exception(f"Error converting DOCX to PDF: {str(e)}")
    
    def convert_to_excel(self, data, output_filename=None, source_type="text"):
        """Convert data to Excel format"""
        try:
            if not output_filename:
                output_filename = f"converted_{uuid.uuid4()}.xlsx"
            
            output_path = os.path.join(self.temp_dir, output_filename)
            
            if source_type == "text":
                # Convert text data to Excel
                lines = data.split('\n')
                processed_data = []
                
                for line in lines:
                    if line.strip():
                        # Try to detect if it's tabular data
                        if '|' in line:
                            row = [cell.strip() for cell in line.split('|')]
                        elif '\t' in line:
                            row = [cell.strip() for cell in line.split('\t')]
                        elif ',' in line and len(line.split(',')) > 1:
                            row = [cell.strip() for cell in line.split(',')]
                        else:
                            row = [line.strip()]
                        processed_data.append(row)
                
                # Create DataFrame
                if processed_data:
                    max_cols = max(len(row) for row in processed_data)
                    # Pad rows to have same number of columns
                    for row in processed_data:
                        while len(row) < max_cols:
                            row.append('')
                    
                    # Use first row as headers if it looks like headers
                    if len(processed_data) > 1:
                        headers = processed_data[0]
                        data_rows = processed_data[1:]
                    else:
                        headers = [f'Column_{i+1}' for i in range(max_cols)]
                        data_rows = processed_data
                    
                    df = pd.DataFrame(data_rows, columns=headers)
                else:
                    df = pd.DataFrame({'Content': [data]})
            
            elif source_type == "csv":
                # Data is already in CSV format
                df = pd.read_csv(BytesIO(data.encode()))
            
            else:
                # Default: treat as single column data
                df = pd.DataFrame({'Content': [data]})
            
            # Save to Excel
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='Converted Data', index=False)
            
            return output_path
            
        except Exception as e:
            raise Exception(f"Error converting to Excel: {str(e)}")
    
    def detect_conversion_intent(self, user_message):
        """Detect if user wants to convert and download a file"""
        message_lower = user_message.lower()
        
        conversion_keywords = {
            'docx': ['convert to word', 'convert to docx', 'make it word', 'word document', 'docx format'],
            'pdf': ['convert to pdf', 'make it pdf', 'pdf format', 'pdf document'],
            'excel': ['convert to excel', 'make it excel', 'excel format', 'xlsx format', 'spreadsheet'],
            'csv': ['convert to csv', 'csv format', 'comma separated']
        }
        
        download_keywords = ['download', 'save', 'export', 'get file']
        
        # Check for conversion intent
        target_format = None
        for format_type, keywords in conversion_keywords.items():
            if any(keyword in message_lower for keyword in keywords):
                target_format = format_type
                break
        
        # Check for download intent
        wants_download = any(keyword in message_lower for keyword in download_keywords)
        
        return target_format, wants_download
    
    def cleanup_temp_file(self, file_path):
        """Clean up temporary files"""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
        except Exception as e:
            print(f"Warning: Could not clean up temp file {file_path}: {e}")

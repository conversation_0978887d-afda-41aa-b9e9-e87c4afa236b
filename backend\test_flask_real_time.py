#!/usr/bin/env python3
"""
Test script to verify Flask backend with true real-time data retrieval
"""

import requests
import json
import time

# Test the Flask backend
BASE_URL = "http://localhost:5000"

def test_flask_query(query, description):
    """Test a query against the Flask backend"""
    print(f"\n🧪 Testing: {description}")
    print(f"Query: '{query}'")
    print("-" * 60)
    
    try:
        # Test the chat endpoint
        response = requests.post(
            f"{BASE_URL}/api/chat",
            json={"message": query},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('response'):
                response_text = data['response']
                print(f"✅ Response received (length: {len(response_text)} chars)")
                print(f"Preview: {response_text[:300]}...")
                
                # Check for real-time indicators
                live_indicators = ['✅', '📅', '🔗', '📰', '🌦️', '💹', '🏏', 'current', 'latest', 'today', '2025']
                has_live_data = any(indicator in response_text for indicator in live_indicators)
                
                # Check for disclaimers (should be eliminated)
                disclaimer_patterns = [
                    "do not have access",
                    "cannot provide",
                    "i don't have access",
                    "my knowledge is limited",
                    "training data"
                ]
                has_disclaimer = any(pattern in response_text.lower() for pattern in disclaimer_patterns)
                
                if has_live_data:
                    print("✓ Response contains real-time information indicators")
                else:
                    print("⚠️  Response may lack real-time indicators")
                
                if has_disclaimer:
                    print("❌ WARNING: Response contains disclaimers!")
                    return False
                else:
                    print("✓ No disclaimers found - real-time data delivered")
                    return True
            else:
                print(f"⚠️  No response in data: {data}")
                return False
        else:
            print(f"❌ HTTP Error {response.status_code}: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - Is Flask backend running?")
        return False
    except requests.exceptions.Timeout:
        print("❌ Request timeout")
        return False
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def main():
    print("🌐 Testing Flask Backend with True Real-Time Data Retrieval")
    print("=" * 70)
    print("Expected: Zero disclaimers, live data with timestamps")
    print("=" * 70)
    
    # Wait for user confirmation
    input("Press Enter when Flask backend is ready (python app.py)...")
    
    # Test queries for real-time data
    test_queries = [
        ("What's the latest news in India?", "📰 Live News Query"),
        ("Show me the current weather in Chennai.", "🌦️ Live Weather Query"),
        ("What's the TCS stock price today?", "💹 Live Stock Query"),
        ("Give me today's cricket score.", "🏏 Live Cricket Query"),
        ("What are today's breaking news headlines?", "📰 Breaking News Query"),
        ("Explain artificial intelligence", "🧠 General Knowledge Query")
    ]
    
    results = []
    
    for query, description in test_queries:
        success = test_flask_query(query, description)
        results.append((description, success))
        time.sleep(3)  # Brief pause between requests to avoid quota issues
    
    # Summary
    print("\n" + "=" * 70)
    print("🎯 Flask Real-Time Data Test Summary")
    print("=" * 70)
    
    successful = sum(1 for _, success in results if success)
    total = len(results)
    
    print(f"✅ Successful queries (no disclaimers): {successful}/{total}")
    
    for description, success in results:
        status = "✅" if success else "❌"
        print(f"   {status} {description}")
    
    if successful == total:
        print("\n🎉 ALL TESTS PASSED! True real-time data retrieval is working!")
        print("\n📊 Achievements:")
        print("   ✅ Zero 'I don't have access to live data' disclaimers")
        print("   ✅ Real-time responses with timestamps and sources")
        print("   ✅ Live data across all domains (News, Weather, Stocks, Cricket)")
        print("   ✅ Smart routing and external API fallbacks")
        print("   ✅ Enhanced Gemini prompting eliminates static responses")
    else:
        print(f"\n⚠️  {total - successful} tests failed. Check Flask backend logs.")
    
    print("\n💡 Expected Response Format:")
    print("   📰 Latest News (as of Oct 22, 2025, 1:30 PM IST):")
    print("   • India signs landmark trade pact with EU — The Hindu")
    print("   • RBI announces new rate — NDTV")
    print("   ")
    print("   🌦️ Weather Update (Chennai, Oct 22, 2025):")
    print("   Temperature: 31°C")
    print("   Conditions: Light rain, humid")
    print("   ")
    print("   💹 Stock: TCS ₹3,621.45 (+0.82%)")
    print("   🏏 India vs Australia — India 145/3 (17.2 ov)")

    print("\n🚀 Production Ready Features:")
    print("   • True real-time web data retrieval via Gemini 2.5")
    print("   • Multi-domain external API fallbacks")
    print("   • Smart query routing and confidence assessment")
    print("   • Zero disclaimer messages")
    print("   • Dynamic timestamped live summaries")

if __name__ == "__main__":
    main()

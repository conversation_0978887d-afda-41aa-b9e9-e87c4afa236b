"""
Global Gemini API Configuration Module
Ensures consistent Gemini API initialization across all services
"""

import os
import google.generativeai as genai
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class GeminiConfig:
    """Global Gemini configuration manager"""
    
    _initialized = False
    _api_key = None
    
    # Updated model names for Gemini 2.0
    MODELS = {
        'chat': 'gemini-2.0-flash-exp',
        'embedding': 'text-embedding-004',
        'pro': 'gemini-2.0-flash-exp',  # Alias for chat model
        'vision': 'gemini-2.0-flash-exp'  # Updated to use Gemini 2.0 Flash for vision
    }

    # Enhanced generation configurations for detailed responses
    GENERATION_CONFIGS = {
        'detailed': {
            'temperature': 0.1,  # Lower temperature for more consistent, detailed responses
            'top_p': 0.8,
            'top_k': 40,
            'max_output_tokens': 8192,  # Maximum tokens for comprehensive responses
            'candidate_count': 1
        },
        'live_data': {
            'temperature': 0.2,  # Slightly higher for natural language in live data
            'top_p': 0.9,
            'top_k': 40,
            'max_output_tokens': 4096,
            'candidate_count': 1
        },
        'analysis': {
            'temperature': 0.1,  # Very low for analytical tasks
            'top_p': 0.8,
            'top_k': 30,
            'max_output_tokens': 8192,
            'candidate_count': 1
        },
        'creative': {
            'temperature': 0.7,  # Higher for creative tasks
            'top_p': 0.9,
            'top_k': 50,
            'max_output_tokens': 4096,
            'candidate_count': 1
        }
    }

    # Safety settings for comprehensive responses
    SAFETY_SETTINGS = [
        {
            "category": "HARM_CATEGORY_HARASSMENT",
            "threshold": "BLOCK_MEDIUM_AND_ABOVE"
        },
        {
            "category": "HARM_CATEGORY_HATE_SPEECH",
            "threshold": "BLOCK_MEDIUM_AND_ABOVE"
        },
        {
            "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
            "threshold": "BLOCK_MEDIUM_AND_ABOVE"
        },
        {
            "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
            "threshold": "BLOCK_MEDIUM_AND_ABOVE"
        }
    ]
    
    @classmethod
    def initialize(cls):
        """Initialize Gemini API globally"""
        if cls._initialized:
            return True
            
        cls._api_key = os.getenv('GEMINI_API_KEY')
        if not cls._api_key:
            print("⚠️  GEMINI_API_KEY not found in environment variables")
            return False
            
        try:
            genai.configure(api_key=cls._api_key)
            cls._initialized = True
            print("✅ Global Gemini API configuration initialized")
            print(f"🌐 Available models: {', '.join(cls.MODELS.values())}")
            return True
        except Exception as e:
            print(f"❌ Global Gemini API initialization failed: {e}")
            return False
    
    @classmethod
    def get_model(cls, model_type='chat', config_type='detailed'):
        """Get a Gemini model instance with specific configuration"""
        if not cls._initialized:
            if not cls.initialize():
                raise ValueError("Gemini API not initialized")

        model_name = cls.MODELS.get(model_type, cls.MODELS['chat'])
        generation_config = cls.GENERATION_CONFIGS.get(config_type, cls.GENERATION_CONFIGS['detailed'])

        try:
            return genai.GenerativeModel(
                model_name=model_name,
                generation_config=generation_config,
                safety_settings=cls.SAFETY_SETTINGS
            )
        except Exception as e:
            print(f"❌ Failed to create {model_type} model ({model_name}): {e}")
            raise e

    @classmethod
    def get_generation_config(cls, config_type='detailed'):
        """Get generation configuration for specific use case"""
        return cls.GENERATION_CONFIGS.get(config_type, cls.GENERATION_CONFIGS['detailed'])

    @classmethod
    def create_detailed_model(cls, model_type='chat'):
        """Create a model specifically configured for detailed responses"""
        return cls.get_model(model_type, 'detailed')

    @classmethod
    def create_analysis_model(cls, model_type='chat'):
        """Create a model specifically configured for analysis tasks"""
        return cls.get_model(model_type, 'analysis')

    @classmethod
    def create_live_data_model(cls, model_type='chat'):
        """Create a model specifically configured for live data processing"""
        return cls.get_model(model_type, 'live_data')
    
    @classmethod
    def get_embedding_model_name(cls):
        """Get the embedding model name"""
        return f"models/{cls.MODELS['embedding']}"
    
    @classmethod
    def is_initialized(cls):
        """Check if Gemini API is initialized"""
        return cls._initialized
    
    @classmethod
    def get_api_key(cls):
        """Get the API key"""
        return cls._api_key

    @classmethod
    def create_vision_model(cls, config_type='analysis'):
        """Create a model specifically configured for vision/image analysis"""
        return cls.get_model('vision', config_type)

    @classmethod
    def create_embedding_model(cls):
        """Create embedding model for text embeddings"""
        if not cls._initialized:
            if not cls.initialize():
                raise ValueError("Gemini API not initialized")

        try:
            return genai.embed_content
        except Exception as e:
            print(f"❌ Failed to create embedding model: {e}")
            raise e

    @classmethod
    def test_model_availability(cls, model_type='chat'):
        """Test if a specific model is available and working"""
        try:
            model = cls.get_model(model_type, 'detailed')
            # Test with a simple prompt
            response = model.generate_content("Test message for model availability check.")
            return {
                'available': True,
                'model_name': cls.MODELS.get(model_type),
                'test_response': response.text[:100] + "..." if len(response.text) > 100 else response.text
            }
        except Exception as e:
            return {
                'available': False,
                'model_name': cls.MODELS.get(model_type),
                'error': str(e)
            }

    @classmethod
    def get_all_model_status(cls):
        """Get status of all configured models"""
        status = {}
        for model_type in cls.MODELS.keys():
            status[model_type] = cls.test_model_availability(model_type)
        return status

    @classmethod
    def get_model_with_fallback(cls, preferred_model='chat', fallback_model='pro', config_type='detailed'):
        """Get a model with fallback option if preferred model fails"""
        try:
            return cls.get_model(preferred_model, config_type)
        except Exception as e:
            print(f"⚠️  Preferred model {preferred_model} failed, trying fallback {fallback_model}: {e}")
            try:
                return cls.get_model(fallback_model, config_type)
            except Exception as fallback_error:
                print(f"❌ Fallback model {fallback_model} also failed: {fallback_error}")
                raise fallback_error

    @classmethod
    def create_custom_model(cls, model_type='chat', custom_config=None):
        """Create a model with custom configuration"""
        if not cls._initialized:
            if not cls.initialize():
                raise ValueError("Gemini API not initialized")

        model_name = cls.MODELS.get(model_type, cls.MODELS['chat'])

        # Use custom config or default to detailed
        if custom_config:
            generation_config = custom_config
        else:
            generation_config = cls.GENERATION_CONFIGS['detailed']

        try:
            return genai.GenerativeModel(
                model_name=model_name,
                generation_config=generation_config,
                safety_settings=cls.SAFETY_SETTINGS
            )
        except Exception as e:
            print(f"❌ Failed to create custom {model_type} model: {e}")
            raise e

    @classmethod
    def validate_configuration(cls):
        """Validate the entire Gemini configuration"""
        validation_results = {
            'api_key_present': bool(cls._api_key),
            'initialized': cls._initialized,
            'models_available': {},
            'generation_configs_valid': True,
            'safety_settings_valid': True,
            'overall_status': 'unknown'
        }

        # Test each model
        for model_type in cls.MODELS.keys():
            try:
                model_status = cls.test_model_availability(model_type)
                validation_results['models_available'][model_type] = model_status['available']
            except Exception as e:
                validation_results['models_available'][model_type] = False
                print(f"❌ Model {model_type} validation failed: {e}")

        # Check if any models are available
        any_model_available = any(validation_results['models_available'].values())

        # Determine overall status
        if validation_results['api_key_present'] and validation_results['initialized'] and any_model_available:
            validation_results['overall_status'] = 'healthy'
        elif validation_results['api_key_present'] and validation_results['initialized']:
            validation_results['overall_status'] = 'partial'
        else:
            validation_results['overall_status'] = 'failed'

        return validation_results

    @classmethod
    def get_recommended_config_for_task(cls, task_type):
        """Get recommended model and config for specific task types"""
        task_recommendations = {
            'chat': {'model': 'chat', 'config': 'detailed'},
            'analysis': {'model': 'chat', 'config': 'analysis'},
            'image_analysis': {'model': 'vision', 'config': 'analysis'},
            'file_analysis': {'model': 'chat', 'config': 'analysis'},
            'live_data': {'model': 'chat', 'config': 'live_data'},
            'creative_writing': {'model': 'chat', 'config': 'creative'},
            'translation': {'model': 'chat', 'config': 'detailed'},
            'summarization': {'model': 'chat', 'config': 'detailed'},  # Even for summarization, use detailed for quality
            'question_answering': {'model': 'chat', 'config': 'detailed'},
            'code_analysis': {'model': 'chat', 'config': 'analysis'},
            'document_processing': {'model': 'chat', 'config': 'analysis'}
        }

        return task_recommendations.get(task_type, {'model': 'chat', 'config': 'detailed'})

    @classmethod
    def create_model_for_task(cls, task_type):
        """Create an optimally configured model for a specific task"""
        recommendation = cls.get_recommended_config_for_task(task_type)
        return cls.get_model(recommendation['model'], recommendation['config'])

    @classmethod
    def get_configuration_info(cls):
        """Get comprehensive configuration information"""
        return {
            'initialized': cls._initialized,
            'api_key_configured': bool(cls._api_key),
            'available_models': cls.MODELS,
            'generation_configs': list(cls.GENERATION_CONFIGS.keys()),
            'safety_settings_count': len(cls.SAFETY_SETTINGS),
            'supported_tasks': [
                'chat', 'analysis', 'image_analysis', 'file_analysis',
                'live_data', 'creative_writing', 'translation', 'summarization',
                'question_answering', 'code_analysis', 'document_processing'
            ]
        }

# Initialize on import with enhanced error handling
try:
    initialization_success = GeminiConfig.initialize()
    if initialization_success:
        print("🚀 Gemini configuration loaded successfully")
        # Validate configuration on startup
        validation = GeminiConfig.validate_configuration()
        print(f"📊 Configuration status: {validation['overall_status']}")
        if validation['overall_status'] != 'healthy':
            print("⚠️  Some models may not be available. Check configuration.")
    else:
        print("⚠️  Gemini configuration initialization failed")
except Exception as e:
    print(f"❌ Critical error during Gemini configuration: {e}")

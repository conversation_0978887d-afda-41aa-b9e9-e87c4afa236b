# Temporarily disable eventlet to avoid timeout issues
# import eventlet
# # Configure eventlet with proper timeout settings
# eventlet.monkey_patch(socket=True, select=True)
#
# # Set eventlet timeout to prevent hanging
# import socket
# socket.setdefaulttimeout(30)

from flask import Flask, request, jsonify, Response, send_file
from flask_cors import CORS
# Temporarily disable Socket<PERSON> imports
# from flask_socketio import SocketIO, emit, disconnect
from models.database import db, User, UserPreference
from services.langchain_service import LangChainService, AdvancedLangChainService
from services.memory_service import MemoryService
from services.rag_service import RAGService
from services.agent_service import AgentService
from services.auth_service import AuthService
from services.document_service import DocumentService
from services.language_service import LanguageService
from services.personalization_service import PersonalizationService
from services.download_service import DownloadService
from services.file_conversion_service import FileConversionService
from services.web_search_service import WebSearchService
from services.speech_service import SpeechToTextService
from services.tts_service import TTSService
from services.streaming_asr_service import StreamingASRService
from services.image_analyzer import ImageAnalyzer
from services.file_analyzer import FileAnalyzer
import os
import uuid
import json
import threading
import time
from dotenv import load_dotenv

# Load environment variables from the backend directory
load_dotenv(os.path.join(os.path.dirname(__file__), '.env'))

def detect_download_format(user_message="", ai_response="", target_format=None):
    """
    Intelligently detect the appropriate download format based on user intent and AI response content.

    Args:
        user_message (str): The user's message/request
        ai_response (str): The AI's response content
        target_format (str): Explicitly requested format from user

    Returns:
        str: The appropriate format ('docx', 'xlsx', 'pdf', or 'docx' as default)
    """
    # If user explicitly requested a format, honor it
    if target_format:
        return target_format.lower()

    # Combine user message and AI response for analysis
    combined_text = f"{user_message} {ai_response}".lower()

    # Check for explicit format mentions in order of priority
    # PDF keywords
    pdf_keywords = ['pdf', 'portable document', 'acrobat', 'download as pdf', 'save as pdf', 'export to pdf']
    if any(keyword in combined_text for keyword in pdf_keywords):
        return 'pdf'

    # Excel keywords - only when explicitly mentioned
    excel_keywords = [
        'excel', 'spreadsheet', '.xlsx', 'workbook', 'download in excel',
        'save as excel', 'export to excel', 'excel format', 'xlsx format'
    ]
    if any(keyword in combined_text for keyword in excel_keywords):
        return 'xlsx'

    # Word document keywords
    word_keywords = [
        'word', 'document', '.docx', 'word document', 'download as word',
        'save as word', 'export to word', 'word format', 'docx format'
    ]
    if any(keyword in combined_text for keyword in word_keywords):
        return 'docx'

    # Check for content that suggests specific formats
    # Data/table content suggests Excel only if explicitly mentioned
    data_keywords = ['table', 'chart', 'data', 'analysis', 'report', 'summary']
    has_data_content = any(keyword in combined_text for keyword in data_keywords)

    # If data content is present but no explicit Excel mention, still default to Word
    # This changes the previous behavior where data content automatically meant Excel

    # Default to Word document for all generic download requests
    return 'docx'

app = Flask(__name__)
CORS(app)

# Initialize SocketIO with CORS support (let it auto-detect async mode)
# socketio = SocketIO(app, cors_allowed_origins="*", ping_timeout=60, ping_interval=25)

# Temporarily disable SocketIO to avoid eventlet timeout issues
# We'll use standard Flask for now and add SocketIO back later if needed
socketio = None
print("⚠️ SocketIO temporarily disabled to avoid eventlet timeout issues")

# Provide a lightweight fallback for emit when SocketIO is disabled so handlers
# defined below do not raise NameError: 'emit' is not defined.
# When SocketIO is re-enabled, the real `emit` from flask_socketio will shadow this.
def emit(event, data=None, *args, **kwargs):
    # Log the attempt to emit an event when socketio is disabled.
    try:
        print(f"[socketio disabled] emit called: {event} data: {repr(data)}")
    except Exception:
        print(f"[socketio disabled] emit called: {event} (failed to repr data)")

# Configuration
app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY')

# Initialize extensions
db.init_app(app)

# Initialize services
langchain_service = LangChainService()
advanced_langchain_service = AdvancedLangChainService()
memory_service = MemoryService()
rag_service = RAGService()
agent_service = AgentService()
auth_service = AuthService()
document_service = DocumentService()
language_service = LanguageService()
personalization_service = PersonalizationService()
download_service = DownloadService()
file_conversion_service = FileConversionService()
web_search_service = WebSearchService()
speech_service = SpeechToTextService()
tts_service = TTSService()
streaming_asr_service = StreamingASRService()
image_analyzer = ImageAnalyzer()
file_analyzer = FileAnalyzer()

# Network health diagnostics for live data APIs
def test_network_connectivity():
    """Test network connectivity to external APIs on startup"""
    import socket

    api_hosts = [
        "api.open-meteo.com",
        "newsapi.org",
        "gnews.io",
        "api.cricapi.com",
        "query2.finance.yahoo.com",
        "wttr.in"
    ]

    print("🌐 Testing network connectivity to external APIs...")

    for host in api_hosts:
        try:
            ip = socket.gethostbyname(host)
            print(f"✅ DNS OK for {host} -> {ip}")
        except socket.gaierror as e:
            print(f"⚠️ DNS FAILED for {host}: {e}")
            # Try basic connectivity test
            try:
                # Test basic socket connection
                import socket
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(5)
                result = sock.connect_ex((host, 80))
                sock.close()
                if result == 0:
                    print(f"✅ Connection OK for {host}")
                else:
                    print(f"❌ Connection failed for {host}")
            except Exception:
                print(f"❌ Complete connectivity failure for {host}")

# Create tables and test connectivity
with app.app_context():
    db.create_all()
    test_network_connectivity()

@app.route('/api/chat', methods=['POST'])
def chat():
    """Handle chat messages with LangChain and multi-language support"""
    try:
        data = request.json
        user_id = data.get('user_id')
        conversation_id = data.get('conversation_id')
        message = data.get('message')
        use_rag = data.get('use_rag', False)
        use_agent = data.get('use_agent', False)
        system_prompt = data.get('system_prompt')

        if not user_id or not message:
            return jsonify({'error': 'Missing required fields'}), 400

        # Validate user
        if not auth_service.validate_user(user_id):
            return jsonify({'error': 'Invalid user'}), 401

        # Create personalized system prompt
        personalized_prompt = personalization_service.create_personalized_system_prompt(user_id, system_prompt)

        # Detect language and prepare message
        language_info = language_service.detect_and_prepare_message(message, personalized_prompt)

        # Create conversation if doesn't exist
        if not conversation_id:
            conversation_id = memory_service.create_conversation(user_id, message[:50])

        # Add user message to memory
        memory_service.add_message(conversation_id, 'user', message)

        # Get conversation history
        history = memory_service.get_conversation_history(conversation_id)

        # Use language-aware and personalized system prompt
        enhanced_system_prompt = language_info['enhanced_system_prompt']

        # Choose service based on flags
        if use_agent:
            response = agent_service.run_enhanced_agent(message, history, use_rag)
        elif use_rag:
            # Enhanced RAG with LangChain
            context = rag_service.get_context(message)
            enhanced_prompt = f"Context: {context}\n\nUser Question: {message}"
            response = langchain_service.generate_response(enhanced_prompt, history, enhanced_system_prompt)
        else:
            response = langchain_service.generate_response(message, history, enhanced_system_prompt)

        # Add assistant response to memory
        memory_service.add_message(conversation_id, 'assistant', response)

        # Determine if response should include download URL
        download_url = None
        response_lower = response.lower()

        # Check if response suggests downloadable content
        if any(keyword in response_lower for keyword in ['excel', 'spreadsheet', 'csv', 'table', 'chart', 'report', 'document', 'analysis', 'summary']):
            # Use smart format detection instead of hardcoded xlsx
            detected_format = detect_download_format(user_message=message, ai_response=response)
            download_url = f"/api/download/conversation/{conversation_id}?format={detected_format}&user_id={user_id}"

        return jsonify({
            'response': response,
            'conversation_id': conversation_id,
            'used_agent': use_agent,
            'used_rag': use_rag,
            'download_url': download_url,  # Dynamic download URL
            'language_info': {
                'detected_language': language_info['detected_language'],
                'detected_language_name': language_info['detected_language_name']
            },
            'updated_conversations': True  # Signal to refresh conversations
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/chat/stream', methods=['POST'])
def chat_stream():
    """Stream chat response with LangChain"""
    try:
        data = request.json
        user_id = data.get('user_id')
        conversation_id = data.get('conversation_id')
        message = data.get('message')
        use_rag = data.get('use_rag', False)
        
        if not user_id or not message:
            return jsonify({'error': 'Missing required fields'}), 400
        
        # Validate user
        if not auth_service.validate_user(user_id):
            return jsonify({'error': 'Invalid user'}), 401
        
        # Create conversation if doesn't exist
        if not conversation_id:
            conversation_id = memory_service.create_conversation(user_id, message[:50])
        
        # Add user message to memory
        memory_service.add_message(conversation_id, 'user', message)
        
        # Get conversation history
        history = memory_service.get_conversation_history(conversation_id)
        
        def generate():
            full_response = ""
            
            if use_rag:
                context = rag_service.get_context(message)
                enhanced_prompt = f"Context: {context}\n\nUser Question: {message}"
                stream = langchain_service.stream_response(enhanced_prompt, history)
            else:
                stream = langchain_service.stream_response(message, history)
            
            for chunk in stream:
                full_response += chunk
                yield f"data: {json.dumps({'chunk': chunk})}\n\n"
            
            # Add final response to memory
            memory_service.add_message(conversation_id, 'assistant', full_response)
            yield f"data: {json.dumps({'complete': True})}\n\n"
        
        return Response(generate(), mimetype='text/plain')
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/chat/agent', methods=['POST'])
def chat_agent():
    """Chat using LangChain agent with tools - Enhanced error handling"""
    try:
        # Validate request data
        if not request.json:
            return jsonify({'error': 'No JSON data provided'}), 400

        data = request.json
        user_id = data.get('user_id')
        conversation_id = data.get('conversation_id')
        message = data.get('message')
        tools = data.get('tools', [])

        if not user_id or not message:
            return jsonify({'error': 'Missing required fields: user_id and message'}), 400

        print(f"Processing chat request - User: {user_id}, Message: {message[:100]}...")

        # Validate user
        try:
            if not auth_service.validate_user(user_id):
                return jsonify({'error': 'Invalid user ID'}), 401
        except Exception as auth_error:
            print(f"Auth validation error: {auth_error}")
            return jsonify({'error': 'Authentication service error'}), 500

        # Create conversation if doesn't exist
        try:
            if not conversation_id:
                conversation_id = memory_service.create_conversation(user_id, message[:50])
                print(f"Created new conversation: {conversation_id}")
        except Exception as conv_error:
            print(f"Conversation creation error: {conv_error}")
            return jsonify({'error': 'Failed to create conversation'}), 500

        # Add user message to memory
        try:
            memory_service.add_message(conversation_id, 'user', message)
        except Exception as memory_error:
            print(f"Memory service error: {memory_error}")
            return jsonify({'error': 'Failed to save message'}), 500

        # Get conversation history
        try:
            history = memory_service.get_conversation_history(conversation_id)
        except Exception as history_error:
            print(f"History retrieval error: {history_error}")
            history = []  # Continue with empty history

        # Run agent with enhanced error handling
        try:
            print("Running agent...")
            response = agent_service.run_enhanced_agent(message, history, use_rag=True)
            print(f"Agent response generated: {len(response)} characters")

            if not response or response.strip() == "":
                response = "I apologize, but I wasn't able to generate a proper response. Could you please try rephrasing your question?"

        except Exception as agent_error:
            print(f"Agent service error: {agent_error}")
            response = f"I encountered an issue while processing your request. The system is experiencing technical difficulties. Please try again in a moment."

        # Add assistant response to memory
        try:
            memory_service.add_message(conversation_id, 'assistant', response)
        except Exception as save_error:
            print(f"Failed to save assistant response: {save_error}")
            # Continue anyway, user still gets the response

        # Determine if response should include download URL
        download_url = None
        response_lower = response.lower()

        # Check if response suggests downloadable content
        if any(keyword in response_lower for keyword in ['excel', 'spreadsheet', 'csv', 'table', 'chart', 'report', 'document', 'analysis', 'summary']):
            # Use smart format detection instead of hardcoded xlsx
            detected_format = detect_download_format(user_message=message, ai_response=response)
            download_url = f"/api/download/conversation/{conversation_id}?format={detected_format}&user_id={user_id}"

        return jsonify({
            'response': response,
            'conversation_id': conversation_id,
            'used_agent': True,
            'tools_used': tools,
            'download_url': download_url,  # Dynamic download URL
            'status': 'success'
        })

    except Exception as e:
        print(f"Unexpected error in chat_agent: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'error': 'An unexpected error occurred. Please try again.',
            'details': str(e) if app.debug else None
        }), 500

@app.route('/api/agent/tools', methods=['GET'])
def get_agent_tools():
    """Get available agent tools"""
    try:
        tools = agent_service.get_available_tools()
        return jsonify({'tools': tools})
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/auth/login', methods=['POST'])
def login():
    """Handle user login/registration"""
    try:
        data = request.json
        username = data.get('username')
        email = data.get('email')

        if not username or not email:
            return jsonify({'error': 'Username and email are required'}), 400

        # Authenticate or create user
        user_id = auth_service.authenticate_user(username, email)

        return jsonify({
            'success': True,
            'user_id': user_id,
            'username': username,
            'email': email
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/auth/validate', methods=['POST'])
def validate_user():
    """Validate user session"""
    try:
        data = request.json
        user_id = data.get('user_id')

        if not user_id:
            return jsonify({'error': 'User ID required'}), 400

        is_valid = auth_service.validate_user(user_id)

        return jsonify({
            'valid': is_valid
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/conversations', methods=['GET'])
def get_conversations():
    """Get user conversations"""
    try:
        user_id = request.args.get('user_id')

        if not user_id:
            return jsonify({'error': 'User ID required'}), 400

        # Validate user
        if not auth_service.validate_user(user_id):
            return jsonify({'error': 'Invalid user'}), 401

        conversations = memory_service.get_user_conversations(user_id)

        return jsonify({
            'conversations': conversations
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/conversation/<conversation_id>', methods=['GET'])
def get_conversation(conversation_id):
    """Get conversation messages"""
    try:
        messages = memory_service.get_conversation_history(conversation_id)

        return jsonify({
            'messages': messages
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/langchain/memory', methods=['GET'])
def get_langchain_memory():
    """Get LangChain memory state"""
    try:
        memory_state = langchain_service.get_memory_summary()
        return jsonify({'memory': memory_state})
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/rag/advanced', methods=['POST'])
def rag_advanced():
    """Advanced RAG with LangChain"""
    try:
        data = request.json
        query = data.get('query')
        user_id = data.get('user_id')
        
        if not query or not user_id:
            return jsonify({'error': 'Query and user_id required'}), 400
        
        # Use LangChain service for the response
        response = langchain_service.generate_response(
            query, 
            system_prompt="Use the provided context to answer the question accurately."
        )
        
        return jsonify({
            'response': response,
            'query': query
        })
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/upload', methods=['POST'])
def upload_file():
    """Handle file upload and document analysis - Enhanced error handling"""
    try:
        # Check if file is present
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400

        file = request.files['file']
        user_id = request.form.get('user_id')
        conversation_id = request.form.get('conversation_id')
        # Support both 'query' and 'message' for backward compatibility
        user_query = request.form.get('message') or request.form.get('query', 'Please analyze this document.')

        if not user_id:
            return jsonify({'error': 'User ID required'}), 400

        if not file or file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        print(f"Processing file upload - User: {user_id}, File: {file.filename}")

        # Validate user
        try:
            if not auth_service.validate_user(user_id):
                return jsonify({'error': 'Invalid user ID'}), 401
        except Exception as auth_error:
            print(f"Auth validation error: {auth_error}")
            return jsonify({'error': 'Authentication service error'}), 500

        # Save and process the file
        try:
            file_path, original_filename = document_service.save_uploaded_file(file)
            print(f"File saved: {original_filename}")
        except Exception as save_error:
            print(f"File save error: {save_error}")
            return jsonify({'error': f'Failed to save file: {str(save_error)}'}), 500

        # Detect language from user query for consistent processing
        try:
            language_info = language_service.detect_and_prepare_message(user_query)
        except Exception as lang_error:
            print(f"Language detection error: {lang_error}")
            language_info = {
                'detected_language': 'en',
                'detected_language_name': 'English',
                'enhanced_system_prompt': 'You are a helpful AI assistant.'
            }

        # Determine file type and use appropriate analyzer
        file_extension = original_filename.lower().split('.')[-1] if '.' in original_filename else ''
        is_image = file_extension in ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'webp']

        analysis_result = None
        text = ""
        metadata = {}

        if is_image:
            # Use image analyzer for image files
            try:
                print(f"Using image analyzer for {original_filename}")
                with open(file_path, 'rb') as img_file:
                    image_data = img_file.read()

                analysis_result = image_analyzer.analyze_image_comprehensive(
                    image_data, user_query, language_info
                )

                if analysis_result.get('success'):
                    # Extract text content from image analysis for compatibility
                    analysis = analysis_result.get('analysis', {})
                    text = analysis.get('comprehensive_analysis', '')

                    # If text is still empty, try to get it from the analysis result directly
                    if not text:
                        text = str(analysis_result.get('analysis', 'No analysis available'))

                    metadata = {
                        'file_type': 'image',
                        'analysis_type': 'image_comprehensive',
                        'image_metadata': analysis_result.get('metadata', {}),
                        'technical_analysis': analysis.get('technical_analysis', {})
                    }
                    print(f"Image analysis completed: {len(text)} characters")
                else:
                    raise Exception(analysis_result.get('error', 'Image analysis failed'))

            except Exception as image_error:
                print(f"Image analysis error: {image_error}")
                # Fallback to document processing
                try:
                    text, metadata = document_service.process_document(file_path, original_filename)
                    print(f"Fallback document processing: {len(text)} characters extracted")
                except Exception as fallback_error:
                    return jsonify({'error': f'Failed to process image: {str(image_error)}. Fallback also failed: {str(fallback_error)}'}), 500
        else:
            # Use file analyzer for document files
            try:
                print(f"Using file analyzer for {original_filename}")
                analysis_result = file_analyzer.analyze_file_comprehensive(
                    file_path, user_query, language_info
                )

                if analysis_result.get('success'):
                    # Extract information from file analysis
                    file_analysis = analysis_result.get('analysis', {})
                    content_extraction = analysis_result.get('content_extraction', {})

                    text = file_analysis.get('comprehensive_analysis', '')

                    # If text is still empty, try to get it from the analysis result directly
                    if not text:
                        text = str(analysis_result.get('analysis', 'No analysis available'))

                    metadata = {
                        'file_type': 'document',
                        'analysis_type': 'file_comprehensive',
                        'file_info': analysis_result.get('file_info', {}),
                        'content_statistics': file_analysis.get('content_statistics', {}),
                        'structural_analysis': file_analysis.get('structural_analysis', {})
                    }
                    print(f"File analysis completed: {len(text)} characters")
                else:
                    raise Exception(analysis_result.get('error', 'File analysis failed'))

            except Exception as file_error:
                print(f"File analysis error: {file_error}")
                # Fallback to document processing
                try:
                    text, metadata = document_service.process_document(file_path, original_filename)
                    print(f"Fallback document processing: {len(text)} characters extracted")
                except Exception as fallback_error:
                    return jsonify({'error': f'Failed to process file: {str(file_error)}. Fallback also failed: {str(fallback_error)}'}), 500

        # Ensure we have some content
        if not text or len(text.strip()) == 0:
            return jsonify({'error': 'No content could be extracted or analyzed from the file'}), 400

        # Create analysis prompt based on file type and analysis result
        try:
            if is_image and analysis_result and analysis_result.get('success'):
                # For images, use the comprehensive analysis as the response
                analysis_data = analysis_result.get('analysis', {})
                response = analysis_data.get('comprehensive_analysis', text)
                print("Using comprehensive image analysis as response")
            elif not is_image and analysis_result and analysis_result.get('success'):
                # For files, use the comprehensive analysis as the response
                analysis_data = analysis_result.get('analysis', {})
                response = analysis_data.get('comprehensive_analysis', text)
                print("Using comprehensive file analysis as response")
            else:
                # Fallback: create analysis prompt for LangChain processing
                analysis_prompt = document_service.analyze_document_with_llm(text, metadata, user_query)
                response = None  # Will be generated later
        except Exception as prompt_error:
            print(f"Analysis prompt creation error: {prompt_error}")
            analysis_prompt = f"Please analyze this document and answer: {user_query}\n\nDocument content:\n{text[:2000]}..."
            response = None  # Will be generated later

        # Create conversation if doesn't exist
        try:
            if not conversation_id:
                conversation_id = memory_service.create_conversation(user_id, f"Document: {original_filename}")
                print(f"Created conversation: {conversation_id}")
        except Exception as conv_error:
            print(f"Conversation creation error: {conv_error}")
            return jsonify({'error': 'Failed to create conversation'}), 500

        # Add user message to memory (document upload + query)
        try:
            user_message = f"[Uploaded document: {original_filename}]\n{user_query}"
            memory_service.add_message(conversation_id, 'user', user_message)
        except Exception as memory_error:
            print(f"Memory service error: {memory_error}")
            # Continue anyway

        # Get conversation history
        try:
            history = memory_service.get_conversation_history(conversation_id)
        except Exception as history_error:
            print(f"History retrieval error: {history_error}")
            history = []

        # Generate response using LangChain (only if we don't already have one from analyzers)
        if response is None:
            try:
                print("Generating AI response using LangChain...")
                response = langchain_service.generate_response(
                    analysis_prompt,
                    history,
                    language_info['enhanced_system_prompt']
                )

                if not response or response.strip() == "":
                    response = f"I've successfully processed your document '{original_filename}'. However, I wasn't able to generate a detailed analysis. The document contains {len(text)} characters of text. Could you please ask a more specific question about the document?"

            except Exception as response_error:
                print(f"Response generation error: {response_error}")
                response = f"I've successfully processed your document '{original_filename}' and extracted {len(text)} characters of text. However, I encountered an issue generating the analysis. Please try asking a specific question about the document content."
        else:
            print("Using pre-generated response from specialized analyzer")

        # Add assistant response to memory
        try:
            memory_service.add_message(conversation_id, 'assistant', response)
        except Exception as save_error:
            print(f"Failed to save assistant response: {save_error}")
            # Continue anyway

        # Determine if response should include download URL based on conversion intent
        download_url = None
        target_format, wants_download = file_conversion_service.detect_conversion_intent(user_query)

        if target_format and wants_download:
            if target_format == 'excel':
                # For Excel conversion, use the text content
                download_url = f"/api/convert_to_excel"
            elif target_format == 'docx' and metadata.get('file_type') == 'pdf':
                download_url = f"/api/convert_to_docx"
            elif target_format == 'pdf' and metadata.get('file_type') in ['docx', 'doc']:
                download_url = f"/api/convert_to_pdf"
            else:
                # Fallback to conversation download
                download_url = f"/api/download/conversation/{conversation_id}?format={target_format}&user_id={user_id}"
        else:
            # Check if response suggests downloadable content (Excel, Word, etc.)
            response_lower = response.lower()
            if any(keyword in response_lower for keyword in ['excel', 'spreadsheet', 'csv', 'table', 'chart', 'report', 'document', 'analysis']):
                # Use smart format detection instead of hardcoded xlsx
                detected_format = detect_download_format(user_message=text, ai_response=response)
                download_url = f"/api/download/conversation/{conversation_id}?format={detected_format}&user_id={user_id}"

        # Prepare enhanced response with analysis information
        response_data = {
            'response': response,
            'conversation_id': conversation_id,
            'document_info': {
                'filename': original_filename,
                'file_type': metadata.get('file_type', 'unknown'),
                'file_size': metadata.get('file_size', 0),
                'text_length': len(text),
                'metadata': metadata,
                'is_image': is_image,
                'analysis_type': metadata.get('analysis_type', 'standard')
            },
            'language_info': {
                'detected_language': language_info['detected_language'],
                'detected_language_name': language_info['detected_language_name']
            },
            'download_url': download_url,  # Dynamic download URL
            'status': 'success'
        }

        # Add detailed analysis information if available
        if analysis_result and analysis_result.get('success'):
            response_data['analysis_details'] = {
                'analyzer_used': 'image_analyzer' if is_image else 'file_analyzer',
                'comprehensive_analysis': True,
                'detailed_response': True,
                'no_summarization': True
            }

            # Add specific analysis metadata
            if is_image:
                response_data['analysis_details']['image_metadata'] = analysis_result.get('image_metadata', {})
                response_data['analysis_details']['technical_analysis'] = analysis_result.get('technical_analysis', {})
            else:
                response_data['analysis_details']['file_info'] = analysis_result.get('file_info', {})
                response_data['analysis_details']['content_extraction'] = analysis_result.get('content_extraction', {})
        else:
            response_data['analysis_details'] = {
                'analyzer_used': 'langchain_fallback',
                'comprehensive_analysis': False,
                'detailed_response': True,
                'no_summarization': True
            }

        return jsonify(response_data)

    except Exception as e:
        print(f"Unexpected error in upload_file: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'error': 'Failed to process file upload. Please try again.',
            'details': str(e) if app.debug else None
        }), 500

@app.route('/api/languages', methods=['GET'])
def get_supported_languages():
    """Get list of supported languages"""
    try:
        languages = language_service.get_supported_languages()
        return jsonify({'languages': languages})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/translate', methods=['POST'])
def translate_text():
    """Translate text between languages"""
    try:
        data = request.json
        text = data.get('text')
        target_language = data.get('target_language', 'en')
        source_language = data.get('source_language')

        if not text:
            return jsonify({'error': 'Text required'}), 400

        # Detect source language if not provided
        if not source_language:
            source_language, _ = language_service.detect_language(text)

        # Translate text
        translated_text = language_service.translate_text(text, target_language, source_language)

        return jsonify({
            'original_text': text,
            'translated_text': translated_text,
            'source_language': source_language,
            'target_language': target_language
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/user/preferences', methods=['GET'])
def get_user_preferences():
    """Get user preferences"""
    try:
        user_id = request.args.get('user_id')

        if not user_id:
            return jsonify({'error': 'User ID required'}), 400

        # Validate user
        if not auth_service.validate_user(user_id):
            return jsonify({'error': 'Invalid user'}), 401

        preferences = personalization_service.get_user_preferences(user_id)

        return jsonify({
            'preferences': preferences,
            'available_tones': personalization_service.get_available_tones(),
            'available_styles': personalization_service.get_available_styles()
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/user/preferences', methods=['POST'])
def update_user_preferences():
    """Update user preferences"""
    try:
        data = request.json
        user_id = data.get('user_id')
        preferences = data.get('preferences', {})

        if not user_id:
            return jsonify({'error': 'User ID required'}), 400

        # Validate user
        if not auth_service.validate_user(user_id):
            return jsonify({'error': 'Invalid user'}), 401

        # Validate preferences
        errors = personalization_service.validate_preferences(preferences)
        if errors:
            return jsonify({'error': 'Invalid preferences', 'details': errors}), 400

        # Update preferences
        success = personalization_service.update_user_preferences(user_id, preferences)

        if success:
            updated_preferences = personalization_service.get_user_preferences(user_id)
            return jsonify({
                'success': True,
                'preferences': updated_preferences
            })
        else:
            return jsonify({'error': 'Failed to update preferences'}), 500

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/user/profile', methods=['GET'])
def get_user_profile():
    """Get complete user profile"""
    try:
        user_id = request.args.get('user_id')

        if not user_id:
            return jsonify({'error': 'User ID required'}), 400

        # Validate user
        if not auth_service.validate_user(user_id):
            return jsonify({'error': 'Invalid user'}), 401

        profile = personalization_service.get_user_profile(user_id)

        if profile:
            return jsonify(profile)
        else:
            return jsonify({'error': 'User profile not found'}), 404

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/user/preferences/reset', methods=['POST'])
def reset_user_preferences():
    """Reset user preferences to defaults"""
    try:
        data = request.json
        user_id = data.get('user_id')

        if not user_id:
            return jsonify({'error': 'User ID required'}), 400

        # Validate user
        if not auth_service.validate_user(user_id):
            return jsonify({'error': 'Invalid user'}), 401

        success = personalization_service.reset_preferences(user_id)

        if success:
            preferences = personalization_service.get_user_preferences(user_id)
            return jsonify({
                'success': True,
                'preferences': preferences
            })
        else:
            return jsonify({'error': 'Failed to reset preferences'}), 500

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/convert_to_docx', methods=['POST'])
def convert_to_docx():
    """Convert uploaded file to DOCX format"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400

        file = request.files['file']
        user_id = request.form.get('user_id')

        if not user_id:
            return jsonify({'error': 'User ID required'}), 400

        if not file or file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        # Validate user
        if not auth_service.validate_user(user_id):
            return jsonify({'error': 'Invalid user'}), 401

        # Save uploaded file temporarily
        file_path, original_filename = document_service.save_uploaded_file(file)
        file_extension = original_filename.rsplit('.', 1)[1].lower()

        try:
            if file_extension == 'pdf':
                # Convert PDF to DOCX
                output_path = file_conversion_service.convert_pdf_to_docx(file_path)
                filename = f"{original_filename.rsplit('.', 1)[0]}_converted.docx"
                mimetype = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            else:
                return jsonify({'error': f'Conversion from {file_extension.upper()} to DOCX not supported'}), 400

            # Verify output file exists and is readable
            if not os.path.exists(output_path):
                return jsonify({'error': 'Conversion failed: output file not created'}), 500

            file_size = os.path.getsize(output_path)
            if file_size == 0:
                return jsonify({'error': 'Conversion failed: output file is empty'}), 500

            print(f"Sending converted file: {filename}, size: {file_size} bytes, mimetype: {mimetype}")

            # ✅ Read file in binary mode for guaranteed binary-safe transmission
            try:
                with open(output_path, 'rb') as f:
                    file_bytes = f.read()

                if len(file_bytes) == 0:
                    return jsonify({'error': 'Failed to read converted file bytes'}), 500

                print(f"Successfully read {len(file_bytes)} bytes from converted file")

                # ✅ Send binary stream with proper headers
                from flask import Response
                response = Response(
                    file_bytes,
                    mimetype=mimetype,
                    headers={
                        'Content-Disposition': f'attachment; filename="{filename}"',
                        'Content-Type': mimetype,
                        'Content-Length': str(len(file_bytes))
                    }
                )

                return response
            except Exception as read_error:
                print(f"Error reading converted file in binary mode: {str(read_error)}")
                return jsonify({'error': f'Failed to read converted file: {str(read_error)}'}), 500

        except Exception as e:
            print(f"Error in convert_to_docx: {str(e)}")
            return jsonify({'error': f'Conversion failed: {str(e)}'}), 500
        finally:
            # Cleanup
            if os.path.exists(file_path):
                os.remove(file_path)

    except Exception as e:
        print(f"Error in convert_to_docx: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/convert_to_pdf', methods=['POST'])
def convert_to_pdf():
    """Convert uploaded file to PDF format"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400

        file = request.files['file']
        user_id = request.form.get('user_id')

        if not user_id:
            return jsonify({'error': 'User ID required'}), 400

        if not file or file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        # Validate user
        if not auth_service.validate_user(user_id):
            return jsonify({'error': 'Invalid user'}), 401

        # Save uploaded file temporarily
        file_path, original_filename = document_service.save_uploaded_file(file)
        file_extension = original_filename.rsplit('.', 1)[1].lower()

        try:
            if file_extension in ['docx', 'doc']:
                # Convert DOCX to PDF
                output_path = file_conversion_service.convert_docx_to_pdf(file_path)
                filename = f"{original_filename.rsplit('.', 1)[0]}_converted.pdf"
                mimetype = 'application/pdf'
            else:
                return jsonify({'error': f'Conversion from {file_extension.upper()} to PDF not supported'}), 400

            # Verify output file exists and is readable
            if not os.path.exists(output_path):
                return jsonify({'error': 'Conversion failed: output file not created'}), 500

            file_size = os.path.getsize(output_path)
            if file_size == 0:
                return jsonify({'error': 'Conversion failed: output file is empty'}), 500

            print(f"Sending converted file: {filename}, size: {file_size} bytes, mimetype: {mimetype}")

            # ✅ Read file in binary mode for guaranteed binary-safe transmission
            try:
                with open(output_path, 'rb') as f:
                    file_bytes = f.read()

                if len(file_bytes) == 0:
                    return jsonify({'error': 'Failed to read converted file bytes'}), 500

                print(f"Successfully read {len(file_bytes)} bytes from converted file")

                # ✅ Send binary stream with proper headers
                from flask import Response
                response = Response(
                    file_bytes,
                    mimetype=mimetype,
                    headers={
                        'Content-Disposition': f'attachment; filename="{filename}"',
                        'Content-Type': mimetype,
                        'Content-Length': str(len(file_bytes))
                    }
                )

                return response
            except Exception as read_error:
                print(f"Error reading converted file in binary mode: {str(read_error)}")
                return jsonify({'error': f'Failed to read converted file: {str(read_error)}'}), 500

        except Exception as e:
            print(f"Error in convert_to_pdf: {str(e)}")
            return jsonify({'error': f'Conversion failed: {str(e)}'}), 500
        finally:
            # Cleanup
            if os.path.exists(file_path):
                os.remove(file_path)

    except Exception as e:
        print(f"Error in convert_to_pdf: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/convert_to_excel', methods=['POST'])
def convert_to_excel():
    """Convert data to Excel format"""
    try:
        data = request.json
        user_id = data.get('user_id')
        content = data.get('content', '')
        source_type = data.get('source_type', 'text')

        if not user_id:
            return jsonify({'error': 'User ID required'}), 400

        if not content:
            return jsonify({'error': 'No content provided'}), 400

        # Validate user
        if not auth_service.validate_user(user_id):
            return jsonify({'error': 'Invalid user'}), 401

        try:
            # Convert to Excel
            output_path = file_conversion_service.convert_to_excel(content, source_type=source_type)
            filename = f"converted_data_{uuid.uuid4().hex[:8]}.xlsx"
            mimetype = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'

            # Verify output file exists and is readable
            if not os.path.exists(output_path):
                return jsonify({'error': 'Conversion failed: output file not created'}), 500

            file_size = os.path.getsize(output_path)
            if file_size == 0:
                return jsonify({'error': 'Conversion failed: output file is empty'}), 500

            print(f"Sending converted file: {filename}, size: {file_size} bytes, mimetype: {mimetype}")

            # ✅ Read file in binary mode for guaranteed binary-safe transmission
            try:
                with open(output_path, 'rb') as f:
                    file_bytes = f.read()

                if len(file_bytes) == 0:
                    return jsonify({'error': 'Failed to read converted file bytes'}), 500

                print(f"Successfully read {len(file_bytes)} bytes from converted file")

                # ✅ Send binary stream with proper headers
                from flask import Response
                response = Response(
                    file_bytes,
                    mimetype=mimetype,
                    headers={
                        'Content-Disposition': f'attachment; filename="{filename}"',
                        'Content-Type': mimetype,
                        'Content-Length': str(len(file_bytes))
                    }
                )

                return response
            except Exception as read_error:
                print(f"Error reading converted file in binary mode: {str(read_error)}")
                return jsonify({'error': f'Failed to read converted file: {str(read_error)}'}), 500

        except Exception as e:
            print(f"Error in convert_to_excel: {str(e)}")
            return jsonify({'error': f'Conversion failed: {str(e)}'}), 500

    except Exception as e:
        print(f"Error in convert_to_excel: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/download/conversation/<conversation_id>', methods=['GET'])
def download_conversation():
    """Download conversation in specified format"""
    try:
        conversation_id = request.view_args['conversation_id']
        format_type = request.args.get('format', 'docx')
        user_id = request.args.get('user_id')

        if not user_id:
            return jsonify({'error': 'User ID required'}), 400

        # Validate user
        if not auth_service.validate_user(user_id):
            return jsonify({'error': 'Invalid user'}), 401

        # Get conversation messages
        messages = memory_service.get_conversation_history(conversation_id)
        if not messages:
            return jsonify({'error': 'Conversation not found'}), 404

        # Get user info
        user = User.query.get(user_id)
        user_info = {
            'username': user.username if user else 'Unknown',
            'email': user.email if user else ''
        }

        # Get conversation title (use first message or default)
        conversation_title = messages[0].get('content', 'Conversation')[:50] + "..." if messages else "Conversation"

        # Map format to MIME type and generator function
        format_config = {
            'pdf': {
                'generator': download_service.generate_conversation_pdf,
                'mimetype': 'application/pdf',
                'extension': 'pdf'
            },
            'docx': {
                'generator': download_service.generate_conversation_docx,
                'mimetype': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'extension': 'docx'
            },
            'xlsx': {
                'generator': download_service.generate_conversation_excel,
                'mimetype': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'extension': 'xlsx'
            },
            'csv': {
                'generator': download_service.generate_conversation_csv,
                'mimetype': 'text/csv',
                'extension': 'csv'
            },
            'txt': {
                'generator': download_service.generate_conversation_txt,
                'mimetype': 'text/plain',
                'extension': 'txt'
            },
            'json': {
                'generator': download_service.generate_conversation_json,
                'mimetype': 'application/json',
                'extension': 'json'
            }
        }

        if format_type not in format_config:
            return jsonify({'error': 'Invalid format. Supported: pdf, docx, xlsx, csv, txt, json'}), 400

        config = format_config[format_type]

        # Generate file
        file_path = config['generator'](messages, conversation_title, user_info)
        mimetype = config['mimetype']
        filename = f"conversation_{conversation_id}.{config['extension']}"

        # Verify file exists and is readable
        if not os.path.exists(file_path):
            return jsonify({'error': 'Generated file not found'}), 500

        # Get file size for validation
        file_size = os.path.getsize(file_path)
        if file_size == 0:
            return jsonify({'error': 'Generated file is empty'}), 500

        print(f"Sending file: {filename}, size: {file_size} bytes, mimetype: {mimetype}")

        # ✅ Enhanced binary-safe file handling with maximum compatibility
        try:
            # Read file in binary mode first to ensure it's valid
            with open(file_path, 'rb') as f:
                file_bytes = f.read()

            # Verify file was read correctly
            if len(file_bytes) == 0:
                return jsonify({'error': 'Generated file is empty'}), 500

            if len(file_bytes) != file_size:
                return jsonify({'error': 'File size mismatch during read'}), 500

            print(f"Successfully read {len(file_bytes)} bytes from file")

            # For DOCX files, add extra validation
            if format_type == 'docx':
                # Verify ZIP signature for DOCX files
                if not file_bytes.startswith(b'PK'):
                    return jsonify({'error': 'Invalid DOCX file format (missing ZIP signature)'}), 500

                # Additional DOCX validation
                import zipfile
                import io
                try:
                    with zipfile.ZipFile(io.BytesIO(file_bytes), 'r') as zip_file:
                        zip_file.testzip()  # Verify ZIP integrity
                        file_list = zip_file.namelist()
                        required_files = ['word/document.xml', '[Content_Types].xml']
                        missing = [f for f in required_files if f not in file_list]
                        if missing:
                            return jsonify({'error': f'Invalid DOCX structure: missing {missing}'}), 500
                except zipfile.BadZipFile:
                    return jsonify({'error': 'Corrupted DOCX file detected'}), 500

                print("✅ DOCX file validation passed")

            # Create response with enhanced headers for maximum compatibility
            response = Response(
                file_bytes,
                mimetype=mimetype,
                headers={
                    'Content-Disposition': f'attachment; filename="{filename}"',
                    'Content-Type': mimetype,
                    'Content-Length': str(len(file_bytes)),
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0',
                    'Accept-Ranges': 'bytes',
                    'Content-Transfer-Encoding': 'binary'
                }
            )

            print(f"Successfully created binary response for {filename}")

            # Clean up temp file immediately after reading
            try:
                os.remove(file_path)
                print(f"Cleaned up temporary file: {file_path}")
            except Exception as cleanup_error:
                print(f"Warning: Could not clean up temp file: {str(cleanup_error)}")

            return response

        except Exception as file_error:
            print(f"Error in enhanced file handling: {str(file_error)}")

            # Final fallback - try send_file if manual method fails
            try:
                print("Attempting fallback with send_file...")
                response = send_file(
                    file_path,
                    mimetype=mimetype,
                    as_attachment=True,
                    download_name=filename
                )

                # Schedule cleanup
                @response.call_on_close
                def cleanup_temp_file():
                    try:
                        if os.path.exists(file_path):
                            os.remove(file_path)
                            print(f"Cleaned up temporary file: {file_path}")
                    except:
                        pass

                return response

            except Exception as final_error:
                print(f"All file handling methods failed: {str(final_error)}")
                # Clean up on complete failure
                try:
                    if os.path.exists(file_path):
                        os.remove(file_path)
                except:
                    pass
                return jsonify({'error': f'Failed to send file: {str(final_error)}'}), 500

    except Exception as e:
        print(f"Error in download_conversation: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/download/formats', methods=['GET'])
def get_download_formats():
    """Get available download formats"""
    try:
        formats = download_service.get_download_formats()
        return jsonify({'formats': formats})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/search/web', methods=['POST'])
def search_web():
    """Perform web search for real-time information"""
    try:
        data = request.json
        query = data.get('query')
        num_results = data.get('num_results', 5)
        user_id = data.get('user_id')

        if not query:
            return jsonify({'error': 'Query required'}), 400

        # Validate user if provided
        if user_id and not auth_service.validate_user(user_id):
            return jsonify({'error': 'Invalid user'}), 401

        # Check if web search is configured
        if not web_search_service.is_configured():
            return jsonify({
                'error': 'Web search not configured. Please set SERPER_API_KEY or Google Custom Search credentials.'
            }), 503

        # Perform search
        search_results = web_search_service.search(query, num_results)

        return jsonify({
            'success': True,
            'query': query,
            'results': search_results['results'],
            'total_results': search_results['total_results'],
            'search_time': search_results['search_time'],
            'source': 'web_search'
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/search/status', methods=['GET'])
def search_status():
    """Check web search service status"""
    try:
        is_configured = web_search_service.is_configured()

        return jsonify({
            'web_search_available': is_configured,
            'serper_configured': bool(web_search_service.serper_api_key),
            'google_search_configured': bool(web_search_service.google_search_key and web_search_service.search_engine_id)
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/speech/transcribe', methods=['POST'])
def transcribe_speech():
    """Transcribe audio using enhanced speech-to-text services"""
    try:
        # Check if audio file is provided
        if 'audio' not in request.files:
            return jsonify({'error': 'No audio file provided'}), 400

        audio_file = request.files['audio']
        if audio_file.filename == '':
            return jsonify({'error': 'No audio file selected'}), 400

        # Get parameters
        language = request.form.get('language', 'en-US')
        provider = request.form.get('provider', 'auto')
        user_id = request.form.get('user_id')

        # Validate user if provided
        if user_id and not auth_service.validate_user(user_id):
            return jsonify({'error': 'Invalid user'}), 401

        # Check if service is configured
        config_status = speech_service.is_configured()
        if not config_status['any_provider']:
            return jsonify({
                'error': 'No speech-to-text providers configured. Please set up Google Cloud or OpenAI credentials.'
            }), 503

        # Read audio data
        audio_data = audio_file.read()

        # Transcribe audio
        result = speech_service.transcribe_audio(audio_data, language, provider)

        if result['success']:
            return jsonify({
                'success': True,
                'transcript': result['transcript'],
                'confidence': result.get('confidence', 0.0),
                'language': result['language'],
                'provider': result['provider']
            })
        else:
            return jsonify({
                'success': False,
                'error': result['error'],
                'provider': result['provider']
            }), 500

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/speech/languages', methods=['GET'])
def get_speech_languages():
    """Get supported languages for speech-to-text"""
    try:
        languages = speech_service.get_supported_languages()
        return jsonify({
            'supported_languages': languages,
            'providers': speech_service.is_configured()
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/speech/status', methods=['GET'])
def get_speech_status():
    """Check speech-to-text service status"""
    try:
        config_status = speech_service.is_configured()
        return jsonify(config_status)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/tts/generate', methods=['POST'])
def generate_tts():
    """Generate text-to-speech audio for multilingual text"""
    try:
        data = request.json
        text = data.get('text')
        language_code = data.get('language_code')  # Optional

        if not text:
            return jsonify({'error': 'Text required'}), 400

        # Generate TTS audio
        result = tts_service.generate_speech(text, language_code)

        if result['success']:
            # Return audio as response
            return Response(
                result['audio_data'],
                mimetype=result['content_type'],
                headers={
                    'Content-Disposition': 'inline; filename="speech.mp3"',
                    'X-Language-Code': result['language_code'],
                    'X-Language-Name': result['language_name'],
                    'X-Is-Indian-Language': str(result['is_indian_language']).lower()
                }
            )
        else:
            return jsonify({
                'error': result['error'],
                'language_code': result.get('language_code'),
                'language_name': result.get('language_name'),
                'supported_languages': result.get('supported_languages', [])
            }), 400

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/tts/detect-language', methods=['POST'])
def detect_text_language():
    """Detect language of text for TTS purposes"""
    try:
        data = request.json
        text = data.get('text')

        if not text:
            return jsonify({'error': 'Text required'}), 400

        # Get language information
        language_info = tts_service.get_language_info(text)

        return jsonify(language_info)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/tts/supported-languages', methods=['GET'])
def get_tts_supported_languages():
    """Get list of supported languages for TTS"""
    try:
        supported_languages = tts_service.get_supported_languages()
        return jsonify(supported_languages)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# ===============================
# ENHANCED ANALYSIS ENDPOINTS
# ===============================

@app.route('/api/analyze/image', methods=['POST'])
def analyze_image_endpoint():
    """Dedicated endpoint for comprehensive image analysis"""
    try:
        # Check if image file is provided (frontend sends as 'file')
        if 'file' not in request.files:
            return jsonify({'error': 'No image file provided'}), 400

        image_file = request.files['file']
        user_query = request.form.get('query', 'Please provide a comprehensive analysis of this image.')
        user_id = request.form.get('user_id')

        if image_file.filename == '':
            return jsonify({'error': 'No image file selected'}), 400

        if user_id and not auth_service.validate_user(user_id):
            return jsonify({'error': 'Invalid user'}), 401

        # Detect language from query
        language_info = language_service.detect_and_prepare_message(user_query)

        # Read image data
        image_data = image_file.read()

        # Perform comprehensive image analysis
        analysis_result = image_analyzer.analyze_image_comprehensive(
            image_data, user_query, language_info
        )

        if analysis_result.get('success'):
            return jsonify({
                'success': True,
                'analysis': analysis_result['analysis'],
                'image_metadata': analysis_result.get('image_metadata', {}),
                'technical_analysis': analysis_result.get('technical_analysis', {}),
                'language_info': {
                    'detected_language': language_info['detected_language'],
                    'detected_language_name': language_info['detected_language_name']
                },
                'analyzer_used': 'image_analyzer',
                'comprehensive_analysis': True,
                'no_summarization': True
            })
        else:
            return jsonify({
                'success': False,
                'error': analysis_result.get('error', 'Image analysis failed')
            }), 500

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/analyze/file', methods=['POST'])
def analyze_file_endpoint():
    """Dedicated endpoint for comprehensive file analysis"""
    try:
        # Check if file is provided
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400

        file = request.files['file']
        user_query = request.form.get('query', 'Please provide a comprehensive analysis of this file.')
        user_id = request.form.get('user_id')

        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        if user_id and not auth_service.validate_user(user_id):
            return jsonify({'error': 'Invalid user'}), 401

        # Detect language from query
        language_info = language_service.detect_and_prepare_message(user_query)

        # Save file temporarily
        file_path, original_filename = document_service.save_uploaded_file(file)

        try:
            # Perform comprehensive file analysis
            analysis_result = file_analyzer.analyze_file_comprehensive(
                file_path, user_query, language_info
            )

            if analysis_result.get('success'):
                return jsonify({
                    'success': True,
                    'analysis': analysis_result['analysis'],
                    'file_info': analysis_result.get('file_info', {}),
                    'content_extraction': analysis_result.get('content_extraction', {}),
                    'language_info': {
                        'detected_language': language_info['detected_language'],
                        'detected_language_name': language_info['detected_language_name']
                    },
                    'analyzer_used': 'file_analyzer',
                    'comprehensive_analysis': True,
                    'no_summarization': True
                })
            else:
                return jsonify({
                    'success': False,
                    'error': analysis_result.get('error', 'File analysis failed')
                }), 500

        finally:
            # Clean up temporary file
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
            except Exception as cleanup_error:
                print(f"Warning: Could not clean up temporary file {file_path}: {cleanup_error}")

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/analyze/batch', methods=['POST'])
def analyze_batch_endpoint():
    """Endpoint for batch analysis of multiple files/images"""
    try:
        files = request.files.getlist('files')
        user_query = request.form.get('query', 'Please provide a comprehensive analysis of these files.')
        user_id = request.form.get('user_id')

        if not files:
            return jsonify({'error': 'No files provided'}), 400

        if user_id and not auth_service.validate_user(user_id):
            return jsonify({'error': 'Invalid user'}), 401

        # Detect language from query
        language_info = language_service.detect_and_prepare_message(user_query)

        results = []

        for file in files:
            if file.filename == '':
                continue

            try:
                # Determine file type
                file_extension = file.filename.lower().split('.')[-1] if '.' in file.filename else ''
                is_image = file_extension in ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'webp']

                if is_image:
                    # Analyze image
                    image_data = file.read()
                    analysis_result = image_analyzer.analyze_image_comprehensive(
                        image_data, user_query, language_info
                    )
                    analysis_result['file_type'] = 'image'
                    analysis_result['filename'] = file.filename
                else:
                    # Analyze file
                    file_path, original_filename = document_service.save_uploaded_file(file)
                    analysis_result = file_analyzer.analyze_file_comprehensive(
                        file_path, user_query, language_info
                    )
                    analysis_result['file_type'] = 'document'
                    analysis_result['filename'] = original_filename

                results.append(analysis_result)

            except Exception as file_error:
                results.append({
                    'success': False,
                    'filename': file.filename,
                    'error': str(file_error)
                })

        return jsonify({
            'success': True,
            'batch_results': results,
            'total_files': len(files),
            'successful_analyses': len([r for r in results if r.get('success')]),
            'language_info': {
                'detected_language': language_info['detected_language'],
                'detected_language_name': language_info['detected_language_name']
            },
            'comprehensive_analysis': True,
            'no_summarization': True
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# ===============================
# WebSocket Handlers for Streaming ASR (temporarily disabled)
# ===============================

# SocketIO handlers are temporarily disabled since SocketIO is disabled
# Uncomment these when SocketIO is re-enabled

# @socketio.on('connect')
# def handle_connect():
#     """Handle client connection"""
#     print(f'Client connected: {request.sid}')
#     emit('connected', {'status': 'Connected to streaming ASR service'})

# @socketio.on('disconnect')
# def handle_disconnect():
#     """Handle client disconnection"""
#     print(f'Client disconnected: {request.sid}')
#     # Clean up any active streaming session
#     streaming_asr_service.cleanup_session(request.sid)

# @socketio.on('start_streaming')
# def handle_start_streaming(data):
    """Start a new streaming ASR session"""
    try:
        language = data.get('language', 'ta-IN')
        session_id = request.sid

        print(f'Starting streaming ASR session for {session_id} with language {language}')

        # Check if service is configured
        config_status = streaming_asr_service.is_configured()
        if not config_status['any_provider']:
            emit('error', {
                'message': 'No streaming ASR providers configured. Please set up OpenAI or Google Cloud credentials.',
                'code': 'NO_PROVIDER'
            })
            return

        # Create streaming session
        streaming_asr_service.create_session(session_id, language)

        emit('session_started', {
            'session_id': session_id,
            'language': language,
            'status': 'ready'
        })

    except Exception as e:
        print(f'Error starting streaming session: {e}')
        emit('error', {
            'message': f'Failed to start streaming session: {str(e)}',
            'code': 'START_ERROR'
        })

# @socketio.on('audio_chunk')
def handle_audio_chunk(data):
    """Handle incoming audio chunk"""
    try:
        session_id = request.sid

        # Check if session exists
        status = streaming_asr_service.get_session_status(session_id)
        if not status['exists']:
            emit('error', {
                'message': 'No active streaming session found',
                'code': 'NO_SESSION'
            })
            return

        # Process audio chunk
        if isinstance(data, bytes):
            audio_chunk = data
        elif isinstance(data, dict) and 'audio' in data:
            # Handle base64 encoded audio
            import base64
            audio_chunk = base64.b64decode(data['audio'])
        else:
            emit('error', {
                'message': 'Invalid audio data format',
                'code': 'INVALID_AUDIO'
            })
            return

        # Push audio chunk and get result
        result = streaming_asr_service.push_audio_chunk(session_id, audio_chunk)

        if 'error' in result:
            emit('error', {
                'message': result['error'],
                'code': 'PROCESSING_ERROR'
            })
            return

        # Send interim or final result
        if result.get('text'):
            event_type = 'final_transcript' if result.get('is_final') else 'interim_transcript'
            emit(event_type, {
                'text': result['text'],
                'confidence': result.get('confidence', 0.0),
                'provider': result.get('provider', 'unknown')
            })

    except Exception as e:
        print(f'Error processing audio chunk: {e}')
        emit('error', {
            'message': f'Failed to process audio: {str(e)}',
            'code': 'CHUNK_ERROR'
        })

# @socketio.on('stop_streaming')
def handle_stop_streaming():
    """Stop streaming and get final result"""
    try:
        session_id = request.sid

        # Finalize session and get final transcript
        final_result = streaming_asr_service.finalize_session(session_id)

        if 'error' not in final_result and final_result.get('text'):
            emit('final_transcript', {
                'text': final_result['text'],
                'confidence': final_result.get('confidence', 0.0),
                'provider': final_result.get('provider', 'unknown'),
                'is_final': True
            })

        emit('session_stopped', {'status': 'Session finalized'})

        # Clean up session
        streaming_asr_service.cleanup_session(session_id)

    except Exception as e:
        print(f'Error stopping streaming session: {e}')
        emit('error', {
            'message': f'Failed to stop session: {str(e)}',
            'code': 'STOP_ERROR'
        })

# @socketio.on('get_session_status')
def handle_get_session_status():
    """Get current session status"""
    try:
        session_id = request.sid
        status = streaming_asr_service.get_session_status(session_id)
        emit('session_status', status)
    except Exception as e:
        emit('error', {
            'message': f'Failed to get session status: {str(e)}',
            'code': 'STATUS_ERROR'
        })

# Background task to clean up old sessions
def cleanup_old_sessions():
    """Background task to clean up old streaming sessions"""
    while True:
        try:
            streaming_asr_service.cleanup_old_sessions(max_age_seconds=300)  # 5 minutes
            time.sleep(60)  # Run every minute
        except Exception as e:
            print(f'Error in cleanup task: {e}')
            time.sleep(60)

# Start cleanup task in background
cleanup_thread = threading.Thread(target=cleanup_old_sessions, daemon=True)
cleanup_thread.start()

@app.route('/api/rag/status', methods=['GET'])
def rag_status():
    """Get RAG service status"""
    try:
        status = rag_service.get_status()
        return jsonify(status)
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'Error checking RAG status: {str(e)}',
            'document_count': 0
        }), 500

if __name__ == '__main__':
    print("🚀 Starting Flask app with live data support...")
    print("🌐 Server will be available at http://localhost:5000")
    print("📡 Live data APIs tested and ready!")
    print("🔗 Available endpoints:")
    print("  - POST /api/chat - Main chat with live data")
    print("  - GET /api/health - Health check")
    print("  - All other endpoints available")

    # Use standard Flask run (SocketIO temporarily disabled)
    app.run(debug=True, host='0.0.0.0', port=5000, threaded=True)
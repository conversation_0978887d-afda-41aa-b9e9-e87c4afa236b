import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Message from './Message';
import { chatAPI } from '../services/api';
import { Send, Plus, Image, FileText, Mic, MicOff, Download, X, Loader2 } from 'lucide-react';
import DownloadButton from './DownloadButton';

const ChatInterface = ({
  conversationId,
  messages,
  onNewMessage,
  onConversationCreate,
  onConversationUpdate,
  user
}) => {
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [useStreaming, setUseStreaming] = useState(false);
  const [activeTab, setActiveTab] = useState('knowledge'); // 'knowledge' or 'agent'
  const [showAgentWorking, setShowAgentWorking] = useState(false);
  const messagesEndRef = useRef(null);

  // Gemini-style upload states
  const [showUploadMenu, setShowUploadMenu] = useState(false);
  const [uploadedFile, setUploadedFile] = useState(null);
  const [filePreview, setFilePreview] = useState(null);

  // Voice input states
  const [isRecording, setIsRecording] = useState(false);
  const [isVoiceSupported, setIsVoiceSupported] = useState(false);
  const recognitionRef = useRef(null);

  const scrollToBottom = (smooth = true) => {
    messagesEndRef.current?.scrollIntoView({
      behavior: smooth ? "smooth" : "auto"
    });
  };

  // Only scroll when new assistant messages arrive, not during user input
  useEffect(() => {
    if (messages.length > 0) {
      const lastMessage = messages[messages.length - 1];
      // Only auto-scroll for assistant messages or when not loading
      if (lastMessage.role === 'assistant' || !isLoading) {
        setTimeout(() => scrollToBottom(), 100);
      }
    }
  }, [messages, isLoading]);

  // Initialize voice recognition with enhanced ChatGPT-like functionality
  useEffect(() => {
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;

    if (SpeechRecognition) {
      setIsVoiceSupported(true);
      const recognition = new SpeechRecognition();

      // Enhanced settings for better accuracy and continuous recording
      recognition.continuous = true;
      recognition.interimResults = true;
      recognition.lang = navigator.language || 'en-US';
      recognition.maxAlternatives = 1;

      // Noise reduction settings
      if ('webkitSpeechRecognition' in window) {
        recognition.webkitGrammar = '';
      }

      let finalTranscript = '';
      let isRestarting = false;

      recognition.onresult = (event) => {
        let interimTranscript = '';
        let newFinalTranscript = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript;
          if (event.results[i].isFinal) {
            newFinalTranscript += transcript;
          } else {
            interimTranscript += transcript;
          }
        }

        // Accumulate final transcript
        if (newFinalTranscript) {
          finalTranscript += newFinalTranscript;
        }

        // Real-time update with both final and interim results
        const fullTranscript = finalTranscript + interimTranscript;
        setInputMessage(fullTranscript);
      };

      recognition.onend = () => {
        if (isRecording && !isRestarting) {
          // Auto-restart for continuous recording
          isRestarting = true;
          setTimeout(() => {
            try {
              if (isRecording) {
                recognition.start();
              }
            } catch (e) {
              console.log('Could not restart recognition:', e);
              setIsRecording(false);
            } finally {
              isRestarting = false;
            }
          }, 100);
        } else if (!isRecording) {
          // User stopped recording, finalize transcript
          setInputMessage(finalTranscript.trim());
          finalTranscript = '';
        }
      };

      recognition.onerror = (event) => {
        console.error('Speech recognition error:', event.error);

        // Handle different error types gracefully
        switch (event.error) {
          case 'no-speech':
          case 'audio-capture':
          case 'network':
            // Don't stop for these errors, just continue
            break;
          case 'not-allowed':
          case 'service-not-allowed':
            setIsRecording(false);
            alert('Microphone access denied. Please enable microphone permissions.');
            break;
          default:
            // For other errors, try to continue
            if (isRecording) {
              setTimeout(() => {
                try {
                  recognition.start();
                } catch (e) {
                  setIsRecording(false);
                }
              }, 1000);
            }
        }
      };

      recognition.onstart = () => {
        console.log('Voice recognition started');
        if (!isRecording) {
          finalTranscript = '';
        }
      };

      recognitionRef.current = recognition;
    }

    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
    };
  }, []); // Remove isRecording dependency to prevent recreation

  // Enhanced voice input handlers
  const handleVoiceToggle = () => {
    if (!isVoiceSupported || !recognitionRef.current) {
      console.log('Voice not supported or recognition not available');
      return;
    }

    if (isRecording) {
      // Stop recording
      console.log('Stopping voice recognition');
      setIsRecording(false);
      try {
        recognitionRef.current.stop();
      } catch (error) {
        console.error('Error stopping voice recognition:', error);
      }
    } else {
      // Start recording
      console.log('Starting voice recognition');
      try {
        setIsRecording(true);
        recognitionRef.current.start();
      } catch (error) {
        console.error('Error starting voice recognition:', error);
        setIsRecording(false);

        // Provide user feedback for common issues
        if (error.name === 'InvalidStateError') {
          console.log('Recognition already running, trying to stop and restart');
          recognitionRef.current.stop();
          setTimeout(() => {
            try {
              recognitionRef.current.start();
              setIsRecording(true);
            } catch (e) {
              console.error('Failed to restart recognition:', e);
            }
          }, 100);
        }
      }
    }
  };

  // File upload handlers
  const handleFileSelect = (type) => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = type === 'image' ? 'image/*' : '.pdf,.doc,.docx,.txt,.csv,.json';

    input.onchange = (e) => {
      const file = e.target.files[0];
      if (file) {
        setUploadedFile(file);

        // Create preview
        if (type === 'image' && file.type.startsWith('image/')) {
          const reader = new FileReader();
          reader.onload = (e) => {
            setFilePreview({
              type: 'image',
              url: e.target.result,
              name: file.name
            });
          };
          reader.readAsDataURL(file);
        } else {
          setFilePreview({
            type: 'document',
            name: file.name,
            size: file.size
          });
        }
      }
    };

    input.click();
    setShowUploadMenu(false);
  };

  const removeUploadedFile = () => {
    setUploadedFile(null);
    setFilePreview(null);
  };

  const handleSendMessage = async () => {
    if ((!inputMessage.trim() && !uploadedFile) || isLoading) return;

    const messageToSend = inputMessage.trim();
    setInputMessage('');
    setIsLoading(true);
    setShowAgentWorking(true);

    // Prepare form data for combined text + file request
    const formData = new FormData();
    formData.append('user_id', user.id);
    if (conversationId) {
      formData.append('conversation_id', conversationId);
    }
    formData.append('message', messageToSend || 'Please analyze this file.');

    if (uploadedFile) {
      formData.append('file', uploadedFile);
    }

    // Add user message immediately (with file info if present)
    const userMessage = {
      role: 'user',
      content: messageToSend,
      timestamp: new Date().toISOString(),
      file: filePreview ? {
        name: filePreview.name,
        type: filePreview.type,
        url: filePreview.url
      } : null
    };
    onNewMessage(userMessage);

    // Clear uploaded file after adding to message
    const currentUploadedFile = uploadedFile;
    setUploadedFile(null);
    setFilePreview(null);

    try {
      let response;

      if (currentUploadedFile) {
        // Enhanced file upload with intelligent endpoint selection
        const extension = currentUploadedFile.name.split('.').pop().toLowerCase();
        const isImage = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'bmp', 'tiff'].includes(extension);

        // Use specialized endpoints for better analysis
        let endpoint = '/api/upload'; // fallback
        if (isImage) {
          endpoint = '/api/analyze/image';
          formData.append('query', messageToSend || 'Provide comprehensive image analysis including visual description, objects, text extraction, technical details, and contextual insights.');
        } else {
          endpoint = '/api/analyze/file';
          formData.append('query', messageToSend || 'Provide comprehensive file analysis including content summary, structure, tone, quality assessment, and practical insights.');
        }

        formData.append('analysis_mode', 'comprehensive');

        response = await fetch(`http://localhost:5000${endpoint}`, {
          method: 'POST',
          body: formData,
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Upload failed');
        }

        response = await response.json();

        // Add analysis metadata to the response for UI display
        response.analysisMetadata = {
          endpoint: endpoint.replace('/api/', ''),
          analysisType: isImage ? 'image_comprehensive' : 'file_comprehensive',
          fileName: currentUploadedFile.name,
          fileSize: currentUploadedFile.size,
          processingTime: Date.now()
        };
      } else {
        // Handle regular message
        const useAgent = activeTab === 'agent';
        const useRAG = activeTab === 'knowledge';

        if (useAgent) {
          response = await chatAPI.sendAgentMessage({
            user_id: user.id,
            conversation_id: conversationId,
            message: messageToSend
          });
        } else {
          response = await chatAPI.sendMessage({
            user_id: user.id,
            conversation_id: conversationId,
            message: messageToSend,
            use_rag: useRAG
          });
        }
        response = response.data;
      }

      const assistantMessage = {
        role: 'assistant',
        content: response.response,
        timestamp: new Date().toISOString(),
        metadata: {
          used_agent: response.used_agent,
          used_rag: response.used_rag,
          document_info: response.document_info,
          // Enhanced analysis metadata
          analysis_type: response.metadata?.analysis_type || response.analysisMetadata?.analysisType,
          analyzer_used: response.metadata?.analyzer_used,
          file_type: response.metadata?.file_type,
          image_metadata: response.metadata?.image_metadata,
          technical_analysis: response.metadata?.technical_analysis,
          processing_endpoint: response.analysisMetadata?.endpoint,
          file_info: response.analysisMetadata ? {
            name: response.analysisMetadata.fileName,
            size: response.analysisMetadata.fileSize,
            processing_time: response.analysisMetadata.processingTime
          } : null,
        // Live data indicators
        has_live_data: response.has_live_data || false,
        live_data_type: response.live_data_type,
        live_data_source: response.live_data_source,
        live_data_timestamp: response.live_data_timestamp
        },
        downloadUrl: response.download_url // For dynamic download button
      };
      onNewMessage(assistantMessage);

      // Trigger conversation list update for real-time sync
      if (onConversationUpdate) {
        onConversationUpdate();
      }

      if (!conversationId && response.conversation_id) {
        onConversationCreate(response.conversation_id);
      }
    } catch (error) {
      console.error('Error sending message:', error);
      const errorMessage = {
        role: 'assistant',
        content: 'Sorry, I encountered an error. Please try again.',
        timestamp: new Date().toISOString()
      };
      onNewMessage(errorMessage);
    } finally {
      setIsLoading(false);
      setShowAgentWorking(false);
    }
  };



  const handleAgentResponse = async (message) => {
    try {
      const response = await chatAPI.sendAgentMessage({
        user_id: user.id,
        conversation_id: conversationId,
        message: message,
        tools: ['agent']
      });

      if (response.data && response.data.response) {
        const assistantMessage = {
          role: 'assistant',
          content: response.data.response,
          timestamp: new Date().toISOString(),
          metadata: {
            used_agent: true,
            tools_used: response.data.tools_used || []
          }
        };
        onNewMessage(assistantMessage);

        if (!conversationId && response.data.conversation_id) {
          onConversationCreate(response.data.conversation_id);
        }
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.error('Agent response error:', error);

      // Determine error type and provide appropriate message
      let errorContent = 'I apologize, but I encountered an issue processing your request. Please try again.';

      if (error.response) {
        const status = error.response.status;
        const errorData = error.response.data;

        if (status === 429) {
          errorContent = '⚠️ I\'m currently experiencing high demand. Please try again in a few moments.';
        } else if (status === 503) {
          errorContent = '⚠️ Some services are temporarily unavailable. I can still help with general questions.';
        } else if (status >= 500) {
          errorContent = '⚠️ Server error detected. Please try again in a moment.';
        } else if (errorData && errorData.error) {
          // Use server-provided error message if available
          if (errorData.error.includes('quota') || errorData.error.includes('429')) {
            errorContent = '⚠️ I\'m currently experiencing high demand. Please try again in a few moments.';
          } else if (errorData.error.includes('network') || errorData.error.includes('timeout')) {
            errorContent = '⚠️ Network connectivity issue. Please check your connection and try again.';
          } else {
            errorContent = `⚠️ ${errorData.error}`;
          }
        }
      } else if (error.code === 'NETWORK_ERROR' || error.message.includes('Network Error')) {
        errorContent = '⚠️ Network connectivity issue. Please check your connection and try again.';
      }

      const errorMessage = {
        role: 'assistant',
        content: errorContent,
        timestamp: new Date().toISOString(),
        metadata: {
          error: true,
          errorType: error.response?.status || 'unknown'
        }
      };
      onNewMessage(errorMessage);
    }
  };

  const handleStreamingResponse = async (message, useRAG) => {
    let fullResponse = '';
    const assistantMessage = {
      role: 'assistant',
      content: '',
      timestamp: new Date().toISOString(),
      metadata: {
        used_rag: useRAG,
        streaming: true
      }
    };
    
    onNewMessage(assistantMessage);
    const messageIndex = messages.length;

    try {
      const response = await fetch('http://localhost:5000/api/chat/stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_id: user.id,
          conversation_id: conversationId,
          message: message,
          use_rag: useRAG,
        }),
      });

      const reader = response.body.getReader();
      const decoder = new TextDecoder();

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = JSON.parse(line.slice(6));
            
            if (data.chunk) {
              fullResponse += data.chunk;
              onNewMessage({
                ...assistantMessage,
                content: fullResponse
              }, messageIndex);
            }
            
            if (data.complete) {
              if (!conversationId) {
                // Handle new conversation creation if needed
              }
              return;
            }
          }
        }
      }
    } catch (error) {
      console.error('Streaming error:', error);
      onNewMessage({
        ...assistantMessage,
        content: 'Error: Failed to get streaming response.'
      }, messageIndex);
    }
  };



  return (
    <div className="flex-1 flex flex-col h-screen bg-gray-50">
      {/* Gemini-style Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="border-b border-gray-200 p-6 bg-white/95 backdrop-blur-sm sticky top-0 z-10"
      >
        <div className="max-w-5xl mx-auto flex items-center justify-between">
          <div className="flex items-center gap-4">
            <motion.div
              whileHover={{ scale: 1.05 }}
              className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg"
            >
              <span className="text-white text-xl font-bold">S</span>
            </motion.div>
            <h1 className="text-2xl font-semibold text-gray-800">Sozhaa Tech AI</h1>
          </div>

          {/* Enhanced Tab Switcher */}
          <div className="flex bg-gray-100 rounded-xl p-1 shadow-sm">
            <button
              onClick={() => setActiveTab('knowledge')}
              className={`px-6 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                activeTab === 'knowledge'
                  ? 'bg-white text-blue-600 shadow-md'
                  : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
              }`}
            >
              Knowledge
            </button>
            <button
              onClick={() => setActiveTab('agent')}
              className={`px-6 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                activeTab === 'agent'
                  ? 'bg-white text-blue-600 shadow-md'
                  : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
              }`}
            >
              Agent
            </button>
          </div>
        </div>
      </motion.div>

      {/* Agent Working Indicator */}
      <AnimatePresence>
        {showAgentWorking && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="fixed top-24 left-1/2 transform -translate-x-1/2 z-50"
          >
            <div className="bg-white border border-gray-200 rounded-xl px-6 py-3 shadow-lg flex items-center gap-3">
              <Loader2 className="w-5 h-5 text-blue-600 animate-spin" />
              <span className="text-gray-700 font-medium">Agent is working...</span>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Messages Container - Gemini Style with Full Width */}
      <div className="flex-1 overflow-y-auto px-6 py-8">
        <div className="max-w-5xl mx-auto">
          {messages.length === 0 ? (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="flex flex-col items-center justify-center h-full text-center py-24"
            >
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                className="w-24 h-24 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mb-8 shadow-xl"
              >
                <span className="text-white text-4xl font-bold">S</span>
              </motion.div>
              <motion.h2
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.4 }}
                className="text-4xl font-semibold text-gray-800 mb-4"
              >
                Hello! I'm Sozhaa Tech AI
              </motion.h2>
              <motion.p
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.6 }}
                className="text-gray-600 text-xl mb-12 max-w-lg"
              >
                How can I help you today? Ask me anything or upload a file to get started.
              </motion.p>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.8 }}
                className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-2xl"
              >
                <motion.div
                  whileHover={{ scale: 1.02, y: -2 }}
                  className="p-6 bg-white rounded-2xl border border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all duration-200 cursor-pointer"
                >
                  <h3 className="font-semibold text-gray-800 mb-2 text-lg">💡 Ask Questions</h3>
                  <p className="text-gray-600">Get answers on any topic with AI-powered insights</p>
                </motion.div>
                <motion.div
                  whileHover={{ scale: 1.02, y: -2 }}
                  className="p-6 bg-white rounded-2xl border border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all duration-200 cursor-pointer"
                >
                  <h3 className="font-semibold text-gray-800 mb-2 text-lg">📄 Analyze Files</h3>
                  <p className="text-gray-600">Upload documents, images, and get detailed analysis</p>
                </motion.div>
              </motion.div>
            </motion.div>
          ) : (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="space-y-8"
            >
              {messages.map((message, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Message
                    message={message}
                    showDownload={message.downloadUrl ? true : false}
                    downloadUrl={message.downloadUrl}
                  />
                </motion.div>
              ))}
            </motion.div>
          )}
          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* File Preview Area - Gemini Style */}
      {filePreview && (
        <div className="px-4 pb-2">
          <div className="max-w-3xl mx-auto">
            <div className="bg-gray-50 border border-gray-200 rounded-xl p-4 flex items-center gap-3">
              {filePreview.type === 'image' ? (
                <>
                  <img
                    src={filePreview.url}
                    alt={filePreview.name}
                    className="w-12 h-12 object-cover rounded-lg"
                  />
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-800">{filePreview.name}</p>
                    <p className="text-xs text-gray-500">Image ready to analyze</p>
                  </div>
                </>
              ) : (
                <>
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <FileText className="w-6 h-6 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-800">{filePreview.name}</p>
                    <p className="text-xs text-gray-500">
                      {(filePreview.size / 1024).toFixed(1)} KB • Ready to analyze
                    </p>
                  </div>
                </>
              )}
              <button
                onClick={removeUploadedFile}
                className="p-1 hover:bg-gray-200 rounded-full transition-colors"
              >
                <X className="w-4 h-4 text-gray-500" />
              </button>
            </div>
          </div>
        </div>
      )}
      {/* Gemini-style Input Area */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="border-t border-gray-100 p-6 bg-white/95 backdrop-blur-sm"
      >
        <div className="max-w-5xl mx-auto">
          <div className="relative bg-white rounded-3xl border-2 border-gray-200 focus-within:border-blue-500 focus-within:shadow-xl transition-all duration-300 shadow-lg hover:shadow-xl">
            <div className="flex items-end gap-3 p-5">
              {/* Upload Button - Gemini Style */}
              <div className="relative">
                <button
                  onClick={() => setShowUploadMenu(!showUploadMenu)}
                  disabled={isLoading}
                  className="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-full transition-all duration-200 disabled:opacity-50"
                >
                  <Plus className="w-5 h-5" />
                </button>

                {/* Upload Menu Popup */}
                {showUploadMenu && (
                  <div className="absolute bottom-full left-0 mb-2 bg-white rounded-xl shadow-lg border border-gray-200 py-2 min-w-[160px] z-50">
                    <button
                      onClick={() => handleFileSelect('image')}
                      className="w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center gap-3 text-gray-700"
                    >
                      <Image className="w-4 h-4" />
                      Upload Image
                    </button>
                    <button
                      onClick={() => handleFileSelect('document')}
                      className="w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center gap-3 text-gray-700"
                    >
                      <FileText className="w-4 h-4" />
                      Upload File
                    </button>
                  </div>
                )}
              </div>

              {/* Enhanced Input Field - ChatGPT Style */}
              <div className="flex-1">
                <textarea
                  value={inputMessage}
                  onChange={(e) => {
                    setInputMessage(e.target.value);
                    // Auto-resize textarea
                    e.target.style.height = 'auto';
                    e.target.style.height = Math.min(e.target.scrollHeight, 200) + 'px';
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSendMessage();
                    }
                  }}
                  placeholder="Message Sozhaa Tech AI..."
                  rows="1"
                  disabled={isLoading}
                  className="w-full bg-transparent border-none outline-none resize-none text-gray-800 placeholder-gray-400 text-lg leading-relaxed py-2"
                  style={{
                    minHeight: '48px',
                    maxHeight: '200px',
                    fontFamily: 'system-ui, -apple-system, sans-serif',
                    overflow: 'hidden'
                  }}
                />
              </div>

              {/* Voice Input Button */}
              {isVoiceSupported && (
                <div className="relative">
                  <button
                    onClick={handleVoiceToggle}
                    disabled={isLoading}
                    className={`p-2 rounded-full transition-all duration-200 relative ${
                      isRecording
                        ? 'text-white bg-red-600 hover:bg-red-700 shadow-lg'
                        : 'text-gray-500 hover:text-blue-600 hover:bg-blue-50'
                    } disabled:opacity-50`}
                    title={isRecording ? 'Stop recording' : 'Start voice input'}
                  >
                    {isRecording ? <MicOff className="w-5 h-5" /> : <Mic className="w-5 h-5" />}

                    {/* Recording indicator */}
                    {isRecording && (
                      <>
                        <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-400 rounded-full animate-ping"></div>
                        <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></div>
                      </>
                    )}
                  </button>

                  {/* Recording status */}
                  {isRecording && (
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-red-600 text-white text-xs rounded whitespace-nowrap animate-fadeIn">
                      🎤 Recording... Click to stop
                    </div>
                  )}
                </div>
              )}

              {/* Send Button */}
              <button
                onClick={handleSendMessage}
                disabled={isLoading || (!inputMessage.trim() && !uploadedFile)}
                className={`p-2 rounded-full transition-all duration-200 ${
                  (inputMessage.trim() || uploadedFile) && !isLoading
                    ? 'text-white bg-blue-600 hover:bg-blue-700'
                    : 'text-gray-400 bg-gray-200'
                } disabled:opacity-50`}
              >
                {isLoading ? (
                  <div className="w-5 h-5 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin"></div>
                ) : (
                  <Send className="w-5 h-5" />
                )}
              </button>
            </div>
          </div>

          {/* Mode Indicator - ChatGPT Style */}
          <div className="flex items-center justify-center mt-4">
            <div className="flex gap-1 bg-gray-100 rounded-full p-1">
              <button
                onClick={() => setActiveTab('knowledge')}
                className={`px-4 py-2 text-sm rounded-full transition-all duration-200 font-medium ${
                  activeTab === 'knowledge'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                💡 Knowledge
              </button>
              <button
                onClick={() => setActiveTab('agent')}
                className={`px-4 py-2 text-sm rounded-full transition-all duration-200 font-medium ${
                  activeTab === 'agent'
                    ? 'bg-white text-purple-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                🤖 Agent
              </button>
            </div>
          </div>

          {/* Footer Text */}
          <div className="text-center mt-3">
            <p className="text-xs text-gray-500">
              Sozhaa Tech AI can make mistakes. Consider checking important information.
            </p>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default ChatInterface;
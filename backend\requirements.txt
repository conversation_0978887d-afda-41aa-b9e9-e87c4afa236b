# ===============================
# GEMINI AI APPLICATION REQUIREMENTS
# Python 3.10.10 Compatible
# Enhanced Multilingual, Real-Time Live Data + Speech + Deep Analysis
# ===============================

# ===============================
# Core Flask & Web
# ===============================
flask==2.3.3
flask-cors==4.0.0
flask-sqlalchemy==3.1.1
werkzeug==2.3.7

# ===============================
# Database & Environment
# ===============================
psycopg2-binary==2.9.7
chromadb==0.4.22
python-dotenv==1.0.1

# ===============================
# AI / ML Core
# ===============================
google-generativeai==0.3.2
sentence-transformers==2.7.0
torchvision==0.18.1
torchaudio==2.3.1
torch==2.3.1
transformers==4.39.3

# ===============================
# LangChain Ecosystem (Stable Set)
# ===============================
langchain==0.1.16
langchain-core==0.1.47
langchain-community==0.0.34
langchain-text-splitters==0.0.2
langchain-google-genai==0.0.6
langchain-chroma==0.1.2

# ===============================
# Utilities
# ===============================
python-multipart==0.0.6
requests==2.31.0
tqdm==4.66.2
numpy==1.26.4
scikit-learn==1.4.2
pandas==2.2.1

# ===============================
# WebSocket & Real-time Communication
# ===============================
flask-socketio==5.3.6
python-socketio==5.10.0
eventlet==0.33.3

# ===============================
# Document Processing
# ===============================
PyMuPDF==1.23.26  # PDF processing - import as 'fitz'
python-docx==1.1.0
openpyxl==3.1.2
python-pptx==0.6.23
beautifulsoup4==4.12.3
lxml==5.3.0  # upgraded for duckduckgo-search compatibility
Pillow==10.2.0  # for image processing
pdfplumber==0.10.3  # for better PDF text extraction

# ===============================
# Language & Translation
# ===============================
langdetect==1.0.9
googletrans==4.0.0rc1
gtts==2.5.1
# fasttext==0.9.2  # Enhanced language detection for multilingual support
# Note: fasttext requires Microsoft Visual C++ 14.0+ build tools on Windows
# It's optional - the system falls back to langdetect if fasttext is not available

# ===============================
# Export & File Generation
# ===============================
reportlab==4.0.9
pypandoc==1.13
pydantic==1.10.12

# ===============================
# Live Data APIs
# ===============================
yfinance==0.2.28


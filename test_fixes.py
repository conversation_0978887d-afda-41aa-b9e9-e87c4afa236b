#!/usr/bin/env python3
"""
Test script to verify the fixes for download button and Tamil speech recognition.
This script tests:
1. Socket.IO imports and basic functionality
2. Backend dependencies
3. Download service functionality
"""

import sys
import os

def test_imports():
    """Test that all required imports work correctly"""
    print("Testing imports...")
    
    try:
        import eventlet
        print("✓ eventlet imported successfully")
    except ImportError as e:
        print(f"✗ eventlet import failed: {e}")
        return False
    
    try:
        from flask_socketio import SocketIO, emit
        print("✓ flask-socketio imported successfully")
    except ImportError as e:
        print(f"✗ flask-socketio import failed: {e}")
        return False
    
    try:
        import flask
        print("✓ flask imported successfully")
    except ImportError as e:
        print(f"✗ flask import failed: {e}")
        return False
    
    return True

def test_socketio_basic():
    """Test basic Socket.IO functionality"""
    print("\nTesting Socket.IO basic functionality...")
    
    try:
        from flask import Flask
        from flask_socketio import SocketIO
        import eventlet
        
        # Create a basic Flask app with Socket.IO
        app = Flask(__name__)
        socketio = SocketIO(app, async_mode='eventlet')
        
        @socketio.on('test_event')
        def handle_test(data):
            return {'status': 'success', 'received': data}
        
        print("✓ Socket.IO app created successfully with eventlet async mode")
        return True
        
    except Exception as e:
        print(f"✗ Socket.IO test failed: {e}")
        return False

def test_download_dependencies():
    """Test download-related dependencies"""
    print("\nTesting download dependencies...")
    
    dependencies = [
        'reportlab',
        'openpyxl',
        'docx',  # python-docx imports as 'docx'
        'pandas'
    ]
    
    all_good = True
    for dep in dependencies:
        try:
            __import__(dep.replace('-', '_'))
            print(f"✓ {dep} imported successfully")
        except ImportError as e:
            print(f"✗ {dep} import failed: {e}")
            all_good = False
    
    return all_good

def test_speech_dependencies():
    """Test speech recognition dependencies"""
    print("\nTesting speech recognition dependencies...")
    
    dependencies = [
        'langdetect',
        'gtts'
    ]
    
    all_good = True
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"✓ {dep} imported successfully")
        except ImportError as e:
            print(f"✗ {dep} import failed: {e}")
            all_good = False
    
    return all_good

def main():
    """Run all tests"""
    print("=" * 60)
    print("Testing Backend Fixes for Download Button and Tamil Speech")
    print("=" * 60)
    
    tests = [
        ("Import Tests", test_imports),
        ("Socket.IO Basic Test", test_socketio_basic),
        ("Download Dependencies", test_download_dependencies),
        ("Speech Dependencies", test_speech_dependencies)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("TEST RESULTS SUMMARY")
    print("=" * 60)
    
    all_passed = True
    for test_name, passed in results:
        status = "PASS" if passed else "FAIL"
        icon = "✓" if passed else "✗"
        print(f"{icon} {test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 ALL TESTS PASSED! Backend is ready for the fixes.")
        print("\nNext steps:")
        print("1. Start the backend: python backend/app.py")
        print("2. Start the frontend: cd frontend && npm start")
        print("3. Test download button functionality")
        print("4. Test Tamil speech recognition with server fallback")
    else:
        print("❌ SOME TESTS FAILED! Please install missing dependencies.")
        print("\nTo install missing dependencies:")
        print("cd backend && pip install -r requirements.txt")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())

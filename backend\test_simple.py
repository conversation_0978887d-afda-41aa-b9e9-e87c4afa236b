#!/usr/bin/env python3
"""
Simple test to verify the endpoint fixes without requiring a running server
"""

import sys
import os
sys.path.append('.')

def test_endpoint_logic():
    """Test the endpoint logic directly"""
    print("🔧 Testing Endpoint Logic...")
    
    try:
        # Test imports
        from services.image_analyzer import ImageAnalyzer
        from services.file_analyzer import FileAnalyzer
        from services.language_service import LanguageService
        
        print("✅ All services imported successfully")
        
        # Test service initialization
        language_service = LanguageService()
        image_analyzer = ImageAnalyzer()
        file_analyzer = FileAnalyzer()
        
        print("✅ All services initialized successfully")
        
        # Test language detection
        language_info = language_service.detect_and_prepare_message("Analyze this test content")
        print(f"✅ Language detection working: {language_info['detected_language']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in endpoint logic test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_form_field_fix():
    """Test that the form field fix is correct"""
    print("\n🔍 Verifying Form Field Fix...")
    
    try:
        # Read the app.py file to verify the fix
        with open('app.py', 'r') as f:
            content = f.read()
        
        # Check if the image endpoint now looks for 'file' instead of 'image'
        if "if 'file' not in request.files:" in content and "@app.route('/api/analyze/image'" in content:
            print("✅ Image endpoint form field fix verified")
            image_fix_ok = True
        else:
            print("❌ Image endpoint form field fix not found")
            image_fix_ok = False
        
        # Check if file endpoint cleanup was added
        if "finally:" in content and "os.remove(file_path)" in content:
            print("✅ File endpoint cleanup fix verified")
            file_fix_ok = True
        else:
            print("❌ File endpoint cleanup fix not found")
            file_fix_ok = False
        
        return image_fix_ok and file_fix_ok
        
    except Exception as e:
        print(f"❌ Error verifying fixes: {e}")
        return False

def main():
    """Run simple tests"""
    print("🧪 Simple Endpoint Fix Verification")
    print("=" * 40)
    
    # Test endpoint logic
    logic_ok = test_endpoint_logic()
    
    # Test form field fix
    fix_ok = test_form_field_fix()
    
    # Summary
    print("\n" + "=" * 40)
    print("📊 Verification Results:")
    print(f"  Endpoint Logic: {'✅ PASS' if logic_ok else '❌ FAIL'}")
    print(f"  Form Field Fix: {'✅ PASS' if fix_ok else '❌ FAIL'}")
    
    if logic_ok and fix_ok:
        print("\n🎉 All fixes verified successfully!")
        print("\n📝 What was fixed:")
        print("  • Image endpoint now expects 'file' field (was 'image')")
        print("  • File endpoint now has proper cleanup")
        print("  • All services are working correctly")
        print("\n✅ The 'Sorry, I encountered an error' issue should be resolved!")
        print("   Try uploading an image or file in the web interface.")
    else:
        print("\n⚠️ Some issues detected. Check the error messages above.")
    
    return logic_ok and fix_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

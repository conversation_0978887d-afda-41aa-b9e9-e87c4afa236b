/*! For license information please see main.66e38d15.js.LICENSE.txt */
(()=>{"use strict";var e={43:(e,t,n)=>{e.exports=n(202)},153:(e,t,n)=>{var r=n(43),i=Symbol.for("react.element"),a=Symbol.for("react.fragment"),o=Object.prototype.hasOwnProperty,s=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function u(e,t,n){var r,a={},u=null,c=null;for(r in void 0!==n&&(u=""+n),void 0!==t.key&&(u=""+t.key),void 0!==t.ref&&(c=t.ref),t)o.call(t,r)&&!l.hasOwnProperty(r)&&(a[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===a[r]&&(a[r]=t[r]);return{$$typeof:i,type:e,key:u,ref:c,props:a,_owner:s.current}}t.Fragment=a,t.jsx=u,t.jsxs=u},202:(e,t)=>{var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),s=Symbol.for("react.provider"),l=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),h=Symbol.iterator;var p={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,g={};function y(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||p}function v(){}function b(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||p}y.prototype.isReactComponent={},y.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},y.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},v.prototype=y.prototype;var x=b.prototype=new v;x.constructor=b,m(x,y.prototype),x.isPureReactComponent=!0;var w=Array.isArray,S=Object.prototype.hasOwnProperty,k={current:null},E={key:!0,ref:!0,__self:!0,__source:!0};function j(e,t,r){var i,a={},o=null,s=null;if(null!=t)for(i in void 0!==t.ref&&(s=t.ref),void 0!==t.key&&(o=""+t.key),t)S.call(t,i)&&!E.hasOwnProperty(i)&&(a[i]=t[i]);var l=arguments.length-2;if(1===l)a.children=r;else if(1<l){for(var u=Array(l),c=0;c<l;c++)u[c]=arguments[c+2];a.children=u}if(e&&e.defaultProps)for(i in l=e.defaultProps)void 0===a[i]&&(a[i]=l[i]);return{$$typeof:n,type:e,key:o,ref:s,props:a,_owner:k.current}}function T(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var C=/\/+/g;function P(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(e){return t[e]})}(""+e.key):t.toString(36)}function N(e,t,i,a,o){var s=typeof e;"undefined"!==s&&"boolean"!==s||(e=null);var l=!1;if(null===e)l=!0;else switch(s){case"string":case"number":l=!0;break;case"object":switch(e.$$typeof){case n:case r:l=!0}}if(l)return o=o(l=e),e=""===a?"."+P(l,0):a,w(o)?(i="",null!=e&&(i=e.replace(C,"$&/")+"/"),N(o,t,i,"",function(e){return e})):null!=o&&(T(o)&&(o=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(o,i+(!o.key||l&&l.key===o.key?"":(""+o.key).replace(C,"$&/")+"/")+e)),t.push(o)),1;if(l=0,a=""===a?".":a+":",w(e))for(var u=0;u<e.length;u++){var c=a+P(s=e[u],u);l+=N(s,t,i,c,o)}else if(c=function(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=h&&e[h]||e["@@iterator"])?e:null}(e),"function"===typeof c)for(e=c.call(e),u=0;!(s=e.next()).done;)l+=N(s=s.value,t,i,c=a+P(s,u++),o);else if("object"===s)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return l}function F(e,t,n){if(null==e)return e;var r=[],i=0;return N(e,r,"","",function(e){return t.call(n,e,i++)}),r}function _(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)},function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var A={current:null},R={transition:null},D={ReactCurrentDispatcher:A,ReactCurrentBatchConfig:R,ReactCurrentOwner:k};function M(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:F,forEach:function(e,t,n){F(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return F(e,function(){t++}),t},toArray:function(e){return F(e,function(e){return e})||[]},only:function(e){if(!T(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=y,t.Fragment=i,t.Profiler=o,t.PureComponent=b,t.StrictMode=a,t.Suspense=c,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=D,t.act=M,t.cloneElement=function(e,t,r){if(null===e||void 0===e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var i=m({},e.props),a=e.key,o=e.ref,s=e._owner;if(null!=t){if(void 0!==t.ref&&(o=t.ref,s=k.current),void 0!==t.key&&(a=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(u in t)S.call(t,u)&&!E.hasOwnProperty(u)&&(i[u]=void 0===t[u]&&void 0!==l?l[u]:t[u])}var u=arguments.length-2;if(1===u)i.children=r;else if(1<u){l=Array(u);for(var c=0;c<u;c++)l[c]=arguments[c+2];i.children=l}return{$$typeof:n,type:e.type,key:a,ref:o,props:i,_owner:s}},t.createContext=function(e){return(e={$$typeof:l,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:s,_context:e},e.Consumer=e},t.createElement=j,t.createFactory=function(e){var t=j.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=T,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:_}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=R.transition;R.transition={};try{e()}finally{R.transition=t}},t.unstable_act=M,t.useCallback=function(e,t){return A.current.useCallback(e,t)},t.useContext=function(e){return A.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return A.current.useDeferredValue(e)},t.useEffect=function(e,t){return A.current.useEffect(e,t)},t.useId=function(){return A.current.useId()},t.useImperativeHandle=function(e,t,n){return A.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return A.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return A.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return A.current.useMemo(e,t)},t.useReducer=function(e,t,n){return A.current.useReducer(e,t,n)},t.useRef=function(e){return A.current.useRef(e)},t.useState=function(e){return A.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return A.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return A.current.useTransition()},t.version="18.3.1"},234:(e,t)=>{function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,i=e[r];if(!(0<a(i,t)))break e;e[r]=t,e[n]=i,n=r}}function r(e){return 0===e.length?null:e[0]}function i(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,i=e.length,o=i>>>1;r<o;){var s=2*(r+1)-1,l=e[s],u=s+1,c=e[u];if(0>a(l,n))u<i&&0>a(c,l)?(e[r]=c,e[u]=n,r=u):(e[r]=l,e[s]=n,r=s);else{if(!(u<i&&0>a(c,n)))break e;e[r]=c,e[u]=n,r=u}}}return t}function a(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"===typeof performance&&"function"===typeof performance.now){var o=performance;t.unstable_now=function(){return o.now()}}else{var s=Date,l=s.now();t.unstable_now=function(){return s.now()-l}}var u=[],c=[],d=1,f=null,h=3,p=!1,m=!1,g=!1,y="function"===typeof setTimeout?setTimeout:null,v="function"===typeof clearTimeout?clearTimeout:null,b="undefined"!==typeof setImmediate?setImmediate:null;function x(e){for(var t=r(c);null!==t;){if(null===t.callback)i(c);else{if(!(t.startTime<=e))break;i(c),t.sortIndex=t.expirationTime,n(u,t)}t=r(c)}}function w(e){if(g=!1,x(e),!m)if(null!==r(u))m=!0,R(S);else{var t=r(c);null!==t&&D(w,t.startTime-e)}}function S(e,n){m=!1,g&&(g=!1,v(T),T=-1),p=!0;var a=h;try{for(x(n),f=r(u);null!==f&&(!(f.expirationTime>n)||e&&!N());){var o=f.callback;if("function"===typeof o){f.callback=null,h=f.priorityLevel;var s=o(f.expirationTime<=n);n=t.unstable_now(),"function"===typeof s?f.callback=s:f===r(u)&&i(u),x(n)}else i(u);f=r(u)}if(null!==f)var l=!0;else{var d=r(c);null!==d&&D(w,d.startTime-n),l=!1}return l}finally{f=null,h=a,p=!1}}"undefined"!==typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var k,E=!1,j=null,T=-1,C=5,P=-1;function N(){return!(t.unstable_now()-P<C)}function F(){if(null!==j){var e=t.unstable_now();P=e;var n=!0;try{n=j(!0,e)}finally{n?k():(E=!1,j=null)}}else E=!1}if("function"===typeof b)k=function(){b(F)};else if("undefined"!==typeof MessageChannel){var _=new MessageChannel,A=_.port2;_.port1.onmessage=F,k=function(){A.postMessage(null)}}else k=function(){y(F,0)};function R(e){j=e,E||(E=!0,k())}function D(e,n){T=y(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){m||p||(m=!0,R(S))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):C=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return h},t.unstable_getFirstCallbackNode=function(){return r(u)},t.unstable_next=function(e){switch(h){case 1:case 2:case 3:var t=3;break;default:t=h}var n=h;h=t;try{return e()}finally{h=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=h;h=e;try{return t()}finally{h=n}},t.unstable_scheduleCallback=function(e,i,a){var o=t.unstable_now();switch("object"===typeof a&&null!==a?a="number"===typeof(a=a.delay)&&0<a?o+a:o:a=o,e){case 1:var s=-1;break;case 2:s=250;break;case 5:s=1073741823;break;case 4:s=1e4;break;default:s=5e3}return e={id:d++,callback:i,priorityLevel:e,startTime:a,expirationTime:s=a+s,sortIndex:-1},a>o?(e.sortIndex=a,n(c,e),null===r(u)&&e===r(c)&&(g?(v(T),T=-1):g=!0,D(w,a-o))):(e.sortIndex=s,n(u,e),m||p||(m=!0,R(S))),e},t.unstable_shouldYield=N,t.unstable_wrapCallback=function(e){var t=h;return function(){var n=h;h=t;try{return e.apply(this,arguments)}finally{h=n}}}},391:(e,t,n)=>{var r=n(950);t.createRoot=r.createRoot,t.hydrateRoot=r.hydrateRoot},579:(e,t,n)=>{e.exports=n(153)},730:(e,t,n)=>{var r=n(43),i=n(853);function a(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var o=new Set,s={};function l(e,t){u(e,t),u(e+"Capture",t)}function u(e,t){for(s[e]=t,e=0;e<t.length;e++)o.add(t[e])}var c=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),d=Object.prototype.hasOwnProperty,f=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,h={},p={};function m(e,t,n,r,i,a,o){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=a,this.removeEmptyString=o}var g={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){g[e]=new m(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];g[t]=new m(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){g[e]=new m(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){g[e]=new m(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){g[e]=new m(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){g[e]=new m(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){g[e]=new m(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){g[e]=new m(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){g[e]=new m(e,5,!1,e.toLowerCase(),null,!1,!1)});var y=/[\-:]([a-z])/g;function v(e){return e[1].toUpperCase()}function b(e,t,n,r){var i=g.hasOwnProperty(t)?g[t]:null;(null!==i?0!==i.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null===t||"undefined"===typeof t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,i,r)&&(n=null),r||null===i?function(e){return!!d.call(p,e)||!d.call(h,e)&&(f.test(e)?p[e]=!0:(h[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=null===n?3!==i.type&&"":n:(t=i.attributeName,r=i.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(i=i.type)||4===i&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(y,v);g[t]=new m(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(y,v);g[t]=new m(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(y,v);g[t]=new m(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){g[e]=new m(e,1,!1,e.toLowerCase(),null,!1,!1)}),g.xlinkHref=new m("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){g[e]=new m(e,1,!1,e.toLowerCase(),null,!0,!0)});var x=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,w=Symbol.for("react.element"),S=Symbol.for("react.portal"),k=Symbol.for("react.fragment"),E=Symbol.for("react.strict_mode"),j=Symbol.for("react.profiler"),T=Symbol.for("react.provider"),C=Symbol.for("react.context"),P=Symbol.for("react.forward_ref"),N=Symbol.for("react.suspense"),F=Symbol.for("react.suspense_list"),_=Symbol.for("react.memo"),A=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var R=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var D=Symbol.iterator;function M(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=D&&e[D]||e["@@iterator"])?e:null}var O,L=Object.assign;function z(e){if(void 0===O)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);O=t&&t[1]||""}return"\n"+O+e}var V=!1;function I(e,t){if(!e||V)return"";V=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&"string"===typeof u.stack){for(var i=u.stack.split("\n"),a=r.stack.split("\n"),o=i.length-1,s=a.length-1;1<=o&&0<=s&&i[o]!==a[s];)s--;for(;1<=o&&0<=s;o--,s--)if(i[o]!==a[s]){if(1!==o||1!==s)do{if(o--,0>--s||i[o]!==a[s]){var l="\n"+i[o].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}}while(1<=o&&0<=s);break}}}finally{V=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?z(e):""}function U(e){switch(e.tag){case 5:return z(e.type);case 16:return z("Lazy");case 13:return z("Suspense");case 19:return z("SuspenseList");case 0:case 2:case 15:return e=I(e.type,!1);case 11:return e=I(e.type.render,!1);case 1:return e=I(e.type,!0);default:return""}}function B(e){if(null==e)return null;if("function"===typeof e)return e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case k:return"Fragment";case S:return"Portal";case j:return"Profiler";case E:return"StrictMode";case N:return"Suspense";case F:return"SuspenseList"}if("object"===typeof e)switch(e.$$typeof){case C:return(e.displayName||"Context")+".Consumer";case T:return(e._context.displayName||"Context")+".Provider";case P:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case _:return null!==(t=e.displayName||null)?t:B(e.type)||"Memo";case A:t=e._payload,e=e._init;try{return B(e(t))}catch(n){}}return null}function W(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return B(t);case 8:return t===E?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"===typeof t)return t.displayName||t.name||null;if("string"===typeof t)return t}return null}function H(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function q(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function K(e){e._valueTracker||(e._valueTracker=function(e){var t=q(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var i=n.get,a=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(e){r=""+e,a.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function $(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=q(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function X(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function Y(e,t){var n=t.checked;return L({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function Q(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=H(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function G(e,t){null!=(t=t.checked)&&b(e,"checked",t,!1)}function Z(e,t){G(e,t);var n=H(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,n):t.hasOwnProperty("defaultValue")&&ee(e,t.type,H(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function J(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function ee(e,t,n){"number"===t&&X(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var te=Array.isArray;function ne(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+H(n),t=null,i=0;i<e.length;i++){if(e[i].value===n)return e[i].selected=!0,void(r&&(e[i].defaultSelected=!0));null!==t||e[i].disabled||(t=e[i])}null!==t&&(t.selected=!0)}}function re(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(a(91));return L({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ie(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(a(92));if(te(n)){if(1<n.length)throw Error(a(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:H(n)}}function ae(e,t){var n=H(t.value),r=H(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function oe(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function se(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function le(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?se(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ue,ce,de=(ce=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ue=ue||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ue.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,r){MSApp.execUnsafeLocalFunction(function(){return ce(e,t)})}:ce);function fe(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var he={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},pe=["Webkit","ms","Moz","O"];function me(e,t,n){return null==t||"boolean"===typeof t||""===t?"":n||"number"!==typeof t||0===t||he.hasOwnProperty(e)&&he[e]?(""+t).trim():t+"px"}function ge(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),i=me(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}Object.keys(he).forEach(function(e){pe.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),he[t]=he[e]})});var ye=L({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ve(e,t){if(t){if(ye[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(a(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(a(60));if("object"!==typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(a(61))}if(null!=t.style&&"object"!==typeof t.style)throw Error(a(62))}}function be(e,t){if(-1===e.indexOf("-"))return"string"===typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var xe=null;function we(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var Se=null,ke=null,Ee=null;function je(e){if(e=bi(e)){if("function"!==typeof Se)throw Error(a(280));var t=e.stateNode;t&&(t=wi(t),Se(e.stateNode,e.type,t))}}function Te(e){ke?Ee?Ee.push(e):Ee=[e]:ke=e}function Ce(){if(ke){var e=ke,t=Ee;if(Ee=ke=null,je(e),t)for(e=0;e<t.length;e++)je(t[e])}}function Pe(e,t){return e(t)}function Ne(){}var Fe=!1;function _e(e,t,n){if(Fe)return e(t,n);Fe=!0;try{return Pe(e,t,n)}finally{Fe=!1,(null!==ke||null!==Ee)&&(Ne(),Ce())}}function Ae(e,t){var n=e.stateNode;if(null===n)return null;var r=wi(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(a(231,t,typeof n));return n}var Re=!1;if(c)try{var De={};Object.defineProperty(De,"passive",{get:function(){Re=!0}}),window.addEventListener("test",De,De),window.removeEventListener("test",De,De)}catch(ce){Re=!1}function Me(e,t,n,r,i,a,o,s,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var Oe=!1,Le=null,ze=!1,Ve=null,Ie={onError:function(e){Oe=!0,Le=e}};function Ue(e,t,n,r,i,a,o,s,l){Oe=!1,Le=null,Me.apply(Ie,arguments)}function Be(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function We(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function He(e){if(Be(e)!==e)throw Error(a(188))}function qe(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=Be(e)))throw Error(a(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(null===i)break;var o=i.alternate;if(null===o){if(null!==(r=i.return)){n=r;continue}break}if(i.child===o.child){for(o=i.child;o;){if(o===n)return He(i),e;if(o===r)return He(i),t;o=o.sibling}throw Error(a(188))}if(n.return!==r.return)n=i,r=o;else{for(var s=!1,l=i.child;l;){if(l===n){s=!0,n=i,r=o;break}if(l===r){s=!0,r=i,n=o;break}l=l.sibling}if(!s){for(l=o.child;l;){if(l===n){s=!0,n=o,r=i;break}if(l===r){s=!0,r=o,n=i;break}l=l.sibling}if(!s)throw Error(a(189))}}if(n.alternate!==r)throw Error(a(190))}if(3!==n.tag)throw Error(a(188));return n.stateNode.current===n?e:t}(e))?Ke(e):null}function Ke(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=Ke(e);if(null!==t)return t;e=e.sibling}return null}var $e=i.unstable_scheduleCallback,Xe=i.unstable_cancelCallback,Ye=i.unstable_shouldYield,Qe=i.unstable_requestPaint,Ge=i.unstable_now,Ze=i.unstable_getCurrentPriorityLevel,Je=i.unstable_ImmediatePriority,et=i.unstable_UserBlockingPriority,tt=i.unstable_NormalPriority,nt=i.unstable_LowPriority,rt=i.unstable_IdlePriority,it=null,at=null;var ot=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(st(e)/lt|0)|0},st=Math.log,lt=Math.LN2;var ut=64,ct=4194304;function dt(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ft(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,i=e.suspendedLanes,a=e.pingedLanes,o=268435455&n;if(0!==o){var s=o&~i;0!==s?r=dt(s):0!==(a&=o)&&(r=dt(a))}else 0!==(o=n&~i)?r=dt(o):0!==a&&(r=dt(a));if(0===r)return 0;if(0!==t&&t!==r&&0===(t&i)&&((i=r&-r)>=(a=t&-t)||16===i&&0!==(4194240&a)))return t;if(0!==(4&r)&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)i=1<<(n=31-ot(t)),r|=e[n],t&=~i;return r}function ht(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function pt(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function mt(){var e=ut;return 0===(4194240&(ut<<=1))&&(ut=64),e}function gt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function yt(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-ot(t)]=n}function vt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-ot(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var bt=0;function xt(e){return 1<(e&=-e)?4<e?0!==(268435455&e)?16:536870912:4:1}var wt,St,kt,Et,jt,Tt=!1,Ct=[],Pt=null,Nt=null,Ft=null,_t=new Map,At=new Map,Rt=[],Dt="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Mt(e,t){switch(e){case"focusin":case"focusout":Pt=null;break;case"dragenter":case"dragleave":Nt=null;break;case"mouseover":case"mouseout":Ft=null;break;case"pointerover":case"pointerout":_t.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":At.delete(t.pointerId)}}function Ot(e,t,n,r,i,a){return null===e||e.nativeEvent!==a?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:a,targetContainers:[i]},null!==t&&(null!==(t=bi(t))&&St(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==i&&-1===t.indexOf(i)&&t.push(i),e)}function Lt(e){var t=vi(e.target);if(null!==t){var n=Be(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=We(n)))return e.blockedOn=t,void jt(e.priority,function(){kt(n)})}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function zt(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Yt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=bi(n))&&St(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);xe=r,n.target.dispatchEvent(r),xe=null,t.shift()}return!0}function Vt(e,t,n){zt(e)&&n.delete(t)}function It(){Tt=!1,null!==Pt&&zt(Pt)&&(Pt=null),null!==Nt&&zt(Nt)&&(Nt=null),null!==Ft&&zt(Ft)&&(Ft=null),_t.forEach(Vt),At.forEach(Vt)}function Ut(e,t){e.blockedOn===t&&(e.blockedOn=null,Tt||(Tt=!0,i.unstable_scheduleCallback(i.unstable_NormalPriority,It)))}function Bt(e){function t(t){return Ut(t,e)}if(0<Ct.length){Ut(Ct[0],e);for(var n=1;n<Ct.length;n++){var r=Ct[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==Pt&&Ut(Pt,e),null!==Nt&&Ut(Nt,e),null!==Ft&&Ut(Ft,e),_t.forEach(t),At.forEach(t),n=0;n<Rt.length;n++)(r=Rt[n]).blockedOn===e&&(r.blockedOn=null);for(;0<Rt.length&&null===(n=Rt[0]).blockedOn;)Lt(n),null===n.blockedOn&&Rt.shift()}var Wt=x.ReactCurrentBatchConfig,Ht=!0;function qt(e,t,n,r){var i=bt,a=Wt.transition;Wt.transition=null;try{bt=1,$t(e,t,n,r)}finally{bt=i,Wt.transition=a}}function Kt(e,t,n,r){var i=bt,a=Wt.transition;Wt.transition=null;try{bt=4,$t(e,t,n,r)}finally{bt=i,Wt.transition=a}}function $t(e,t,n,r){if(Ht){var i=Yt(e,t,n,r);if(null===i)Hr(e,t,r,Xt,n),Mt(e,r);else if(function(e,t,n,r,i){switch(t){case"focusin":return Pt=Ot(Pt,e,t,n,r,i),!0;case"dragenter":return Nt=Ot(Nt,e,t,n,r,i),!0;case"mouseover":return Ft=Ot(Ft,e,t,n,r,i),!0;case"pointerover":var a=i.pointerId;return _t.set(a,Ot(_t.get(a)||null,e,t,n,r,i)),!0;case"gotpointercapture":return a=i.pointerId,At.set(a,Ot(At.get(a)||null,e,t,n,r,i)),!0}return!1}(i,e,t,n,r))r.stopPropagation();else if(Mt(e,r),4&t&&-1<Dt.indexOf(e)){for(;null!==i;){var a=bi(i);if(null!==a&&wt(a),null===(a=Yt(e,t,n,r))&&Hr(e,t,r,Xt,n),a===i)break;i=a}null!==i&&r.stopPropagation()}else Hr(e,t,r,null,n)}}var Xt=null;function Yt(e,t,n,r){if(Xt=null,null!==(e=vi(e=we(r))))if(null===(t=Be(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=We(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Xt=e,null}function Qt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Ze()){case Je:return 1;case et:return 4;case tt:case nt:return 16;case rt:return 536870912;default:return 16}default:return 16}}var Gt=null,Zt=null,Jt=null;function en(){if(Jt)return Jt;var e,t,n=Zt,r=n.length,i="value"in Gt?Gt.value:Gt.textContent,a=i.length;for(e=0;e<r&&n[e]===i[e];e++);var o=r-e;for(t=1;t<=o&&n[r-t]===i[a-t];t++);return Jt=i.slice(e,1<t?1-t:void 0)}function tn(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function nn(){return!0}function rn(){return!1}function an(e){function t(t,n,r,i,a){for(var o in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=i,this.target=a,this.currentTarget=null,e)e.hasOwnProperty(o)&&(t=e[o],this[o]=t?t(i):i[o]);return this.isDefaultPrevented=(null!=i.defaultPrevented?i.defaultPrevented:!1===i.returnValue)?nn:rn,this.isPropagationStopped=rn,this}return L(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=nn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=nn)},persist:function(){},isPersistent:nn}),t}var on,sn,ln,un={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},cn=an(un),dn=L({},un,{view:0,detail:0}),fn=an(dn),hn=L({},dn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:jn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==ln&&(ln&&"mousemove"===e.type?(on=e.screenX-ln.screenX,sn=e.screenY-ln.screenY):sn=on=0,ln=e),on)},movementY:function(e){return"movementY"in e?e.movementY:sn}}),pn=an(hn),mn=an(L({},hn,{dataTransfer:0})),gn=an(L({},dn,{relatedTarget:0})),yn=an(L({},un,{animationName:0,elapsedTime:0,pseudoElement:0})),vn=L({},un,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),bn=an(vn),xn=an(L({},un,{data:0})),wn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Sn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},kn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function En(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=kn[e])&&!!t[e]}function jn(){return En}var Tn=L({},dn,{key:function(e){if(e.key){var t=wn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=tn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?Sn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:jn,charCode:function(e){return"keypress"===e.type?tn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?tn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),Cn=an(Tn),Pn=an(L({},hn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Nn=an(L({},dn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:jn})),Fn=an(L({},un,{propertyName:0,elapsedTime:0,pseudoElement:0})),_n=L({},hn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),An=an(_n),Rn=[9,13,27,32],Dn=c&&"CompositionEvent"in window,Mn=null;c&&"documentMode"in document&&(Mn=document.documentMode);var On=c&&"TextEvent"in window&&!Mn,Ln=c&&(!Dn||Mn&&8<Mn&&11>=Mn),zn=String.fromCharCode(32),Vn=!1;function In(e,t){switch(e){case"keyup":return-1!==Rn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Un(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var Bn=!1;var Wn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Hn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Wn[e.type]:"textarea"===t}function qn(e,t,n,r){Te(r),0<(t=Kr(t,"onChange")).length&&(n=new cn("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Kn=null,$n=null;function Xn(e){zr(e,0)}function Yn(e){if($(xi(e)))return e}function Qn(e,t){if("change"===e)return t}var Gn=!1;if(c){var Zn;if(c){var Jn="oninput"in document;if(!Jn){var er=document.createElement("div");er.setAttribute("oninput","return;"),Jn="function"===typeof er.oninput}Zn=Jn}else Zn=!1;Gn=Zn&&(!document.documentMode||9<document.documentMode)}function tr(){Kn&&(Kn.detachEvent("onpropertychange",nr),$n=Kn=null)}function nr(e){if("value"===e.propertyName&&Yn($n)){var t=[];qn(t,$n,e,we(e)),_e(Xn,t)}}function rr(e,t,n){"focusin"===e?(tr(),$n=n,(Kn=t).attachEvent("onpropertychange",nr)):"focusout"===e&&tr()}function ir(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Yn($n)}function ar(e,t){if("click"===e)return Yn(t)}function or(e,t){if("input"===e||"change"===e)return Yn(t)}var sr="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function lr(e,t){if(sr(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!d.call(t,i)||!sr(e[i],t[i]))return!1}return!0}function ur(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function cr(e,t){var n,r=ur(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=ur(r)}}function dr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?dr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function fr(){for(var e=window,t=X();t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=X((e=t.contentWindow).document)}return t}function hr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function pr(e){var t=fr(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&dr(n.ownerDocument.documentElement,n)){if(null!==r&&hr(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var i=n.textContent.length,a=Math.min(r.start,i);r=void 0===r.end?a:Math.min(r.end,i),!e.extend&&a>r&&(i=r,r=a,a=i),i=cr(n,a);var o=cr(n,r);i&&o&&(1!==e.rangeCount||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&((t=t.createRange()).setStart(i.node,i.offset),e.removeAllRanges(),a>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"===typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var mr=c&&"documentMode"in document&&11>=document.documentMode,gr=null,yr=null,vr=null,br=!1;function xr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;br||null==gr||gr!==X(r)||("selectionStart"in(r=gr)&&hr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},vr&&lr(vr,r)||(vr=r,0<(r=Kr(yr,"onSelect")).length&&(t=new cn("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=gr)))}function wr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Sr={animationend:wr("Animation","AnimationEnd"),animationiteration:wr("Animation","AnimationIteration"),animationstart:wr("Animation","AnimationStart"),transitionend:wr("Transition","TransitionEnd")},kr={},Er={};function jr(e){if(kr[e])return kr[e];if(!Sr[e])return e;var t,n=Sr[e];for(t in n)if(n.hasOwnProperty(t)&&t in Er)return kr[e]=n[t];return e}c&&(Er=document.createElement("div").style,"AnimationEvent"in window||(delete Sr.animationend.animation,delete Sr.animationiteration.animation,delete Sr.animationstart.animation),"TransitionEvent"in window||delete Sr.transitionend.transition);var Tr=jr("animationend"),Cr=jr("animationiteration"),Pr=jr("animationstart"),Nr=jr("transitionend"),Fr=new Map,_r="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Ar(e,t){Fr.set(e,t),l(t,[e])}for(var Rr=0;Rr<_r.length;Rr++){var Dr=_r[Rr];Ar(Dr.toLowerCase(),"on"+(Dr[0].toUpperCase()+Dr.slice(1)))}Ar(Tr,"onAnimationEnd"),Ar(Cr,"onAnimationIteration"),Ar(Pr,"onAnimationStart"),Ar("dblclick","onDoubleClick"),Ar("focusin","onFocus"),Ar("focusout","onBlur"),Ar(Nr,"onTransitionEnd"),u("onMouseEnter",["mouseout","mouseover"]),u("onMouseLeave",["mouseout","mouseover"]),u("onPointerEnter",["pointerout","pointerover"]),u("onPointerLeave",["pointerout","pointerover"]),l("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),l("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),l("onBeforeInput",["compositionend","keypress","textInput","paste"]),l("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),l("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),l("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Mr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Or=new Set("cancel close invalid load scroll toggle".split(" ").concat(Mr));function Lr(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,i,o,s,l,u){if(Ue.apply(this,arguments),Oe){if(!Oe)throw Error(a(198));var c=Le;Oe=!1,Le=null,ze||(ze=!0,Ve=c)}}(r,t,void 0,e),e.currentTarget=null}function zr(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var a=void 0;if(t)for(var o=r.length-1;0<=o;o--){var s=r[o],l=s.instance,u=s.currentTarget;if(s=s.listener,l!==a&&i.isPropagationStopped())break e;Lr(i,s,u),a=l}else for(o=0;o<r.length;o++){if(l=(s=r[o]).instance,u=s.currentTarget,s=s.listener,l!==a&&i.isPropagationStopped())break e;Lr(i,s,u),a=l}}}if(ze)throw e=Ve,ze=!1,Ve=null,e}function Vr(e,t){var n=t[mi];void 0===n&&(n=t[mi]=new Set);var r=e+"__bubble";n.has(r)||(Wr(t,e,2,!1),n.add(r))}function Ir(e,t,n){var r=0;t&&(r|=4),Wr(n,e,r,t)}var Ur="_reactListening"+Math.random().toString(36).slice(2);function Br(e){if(!e[Ur]){e[Ur]=!0,o.forEach(function(t){"selectionchange"!==t&&(Or.has(t)||Ir(t,!1,e),Ir(t,!0,e))});var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Ur]||(t[Ur]=!0,Ir("selectionchange",!1,t))}}function Wr(e,t,n,r){switch(Qt(t)){case 1:var i=qt;break;case 4:i=Kt;break;default:i=$t}n=i.bind(null,t,n,e),i=void 0,!Re||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(i=!0),r?void 0!==i?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):void 0!==i?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function Hr(e,t,n,r,i){var a=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var o=r.tag;if(3===o||4===o){var s=r.stateNode.containerInfo;if(s===i||8===s.nodeType&&s.parentNode===i)break;if(4===o)for(o=r.return;null!==o;){var l=o.tag;if((3===l||4===l)&&((l=o.stateNode.containerInfo)===i||8===l.nodeType&&l.parentNode===i))return;o=o.return}for(;null!==s;){if(null===(o=vi(s)))return;if(5===(l=o.tag)||6===l){r=a=o;continue e}s=s.parentNode}}r=r.return}_e(function(){var r=a,i=we(n),o=[];e:{var s=Fr.get(e);if(void 0!==s){var l=cn,u=e;switch(e){case"keypress":if(0===tn(n))break e;case"keydown":case"keyup":l=Cn;break;case"focusin":u="focus",l=gn;break;case"focusout":u="blur",l=gn;break;case"beforeblur":case"afterblur":l=gn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":l=pn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":l=mn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":l=Nn;break;case Tr:case Cr:case Pr:l=yn;break;case Nr:l=Fn;break;case"scroll":l=fn;break;case"wheel":l=An;break;case"copy":case"cut":case"paste":l=bn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":l=Pn}var c=0!==(4&t),d=!c&&"scroll"===e,f=c?null!==s?s+"Capture":null:s;c=[];for(var h,p=r;null!==p;){var m=(h=p).stateNode;if(5===h.tag&&null!==m&&(h=m,null!==f&&(null!=(m=Ae(p,f))&&c.push(qr(p,m,h)))),d)break;p=p.return}0<c.length&&(s=new l(s,u,null,n,i),o.push({event:s,listeners:c}))}}if(0===(7&t)){if(l="mouseout"===e||"pointerout"===e,(!(s="mouseover"===e||"pointerover"===e)||n===xe||!(u=n.relatedTarget||n.fromElement)||!vi(u)&&!u[pi])&&(l||s)&&(s=i.window===i?i:(s=i.ownerDocument)?s.defaultView||s.parentWindow:window,l?(l=r,null!==(u=(u=n.relatedTarget||n.toElement)?vi(u):null)&&(u!==(d=Be(u))||5!==u.tag&&6!==u.tag)&&(u=null)):(l=null,u=r),l!==u)){if(c=pn,m="onMouseLeave",f="onMouseEnter",p="mouse","pointerout"!==e&&"pointerover"!==e||(c=Pn,m="onPointerLeave",f="onPointerEnter",p="pointer"),d=null==l?s:xi(l),h=null==u?s:xi(u),(s=new c(m,p+"leave",l,n,i)).target=d,s.relatedTarget=h,m=null,vi(i)===r&&((c=new c(f,p+"enter",u,n,i)).target=h,c.relatedTarget=d,m=c),d=m,l&&u)e:{for(f=u,p=0,h=c=l;h;h=$r(h))p++;for(h=0,m=f;m;m=$r(m))h++;for(;0<p-h;)c=$r(c),p--;for(;0<h-p;)f=$r(f),h--;for(;p--;){if(c===f||null!==f&&c===f.alternate)break e;c=$r(c),f=$r(f)}c=null}else c=null;null!==l&&Xr(o,s,l,c,!1),null!==u&&null!==d&&Xr(o,d,u,c,!0)}if("select"===(l=(s=r?xi(r):window).nodeName&&s.nodeName.toLowerCase())||"input"===l&&"file"===s.type)var g=Qn;else if(Hn(s))if(Gn)g=or;else{g=ir;var y=rr}else(l=s.nodeName)&&"input"===l.toLowerCase()&&("checkbox"===s.type||"radio"===s.type)&&(g=ar);switch(g&&(g=g(e,r))?qn(o,g,n,i):(y&&y(e,s,r),"focusout"===e&&(y=s._wrapperState)&&y.controlled&&"number"===s.type&&ee(s,"number",s.value)),y=r?xi(r):window,e){case"focusin":(Hn(y)||"true"===y.contentEditable)&&(gr=y,yr=r,vr=null);break;case"focusout":vr=yr=gr=null;break;case"mousedown":br=!0;break;case"contextmenu":case"mouseup":case"dragend":br=!1,xr(o,n,i);break;case"selectionchange":if(mr)break;case"keydown":case"keyup":xr(o,n,i)}var v;if(Dn)e:{switch(e){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else Bn?In(e,n)&&(b="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(b="onCompositionStart");b&&(Ln&&"ko"!==n.locale&&(Bn||"onCompositionStart"!==b?"onCompositionEnd"===b&&Bn&&(v=en()):(Zt="value"in(Gt=i)?Gt.value:Gt.textContent,Bn=!0)),0<(y=Kr(r,b)).length&&(b=new xn(b,e,null,n,i),o.push({event:b,listeners:y}),v?b.data=v:null!==(v=Un(n))&&(b.data=v))),(v=On?function(e,t){switch(e){case"compositionend":return Un(t);case"keypress":return 32!==t.which?null:(Vn=!0,zn);case"textInput":return(e=t.data)===zn&&Vn?null:e;default:return null}}(e,n):function(e,t){if(Bn)return"compositionend"===e||!Dn&&In(e,t)?(e=en(),Jt=Zt=Gt=null,Bn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Ln&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(r=Kr(r,"onBeforeInput")).length&&(i=new xn("onBeforeInput","beforeinput",null,n,i),o.push({event:i,listeners:r}),i.data=v))}zr(o,t)})}function qr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Kr(e,t){for(var n=t+"Capture",r=[];null!==e;){var i=e,a=i.stateNode;5===i.tag&&null!==a&&(i=a,null!=(a=Ae(e,n))&&r.unshift(qr(e,a,i)),null!=(a=Ae(e,t))&&r.push(qr(e,a,i))),e=e.return}return r}function $r(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function Xr(e,t,n,r,i){for(var a=t._reactName,o=[];null!==n&&n!==r;){var s=n,l=s.alternate,u=s.stateNode;if(null!==l&&l===r)break;5===s.tag&&null!==u&&(s=u,i?null!=(l=Ae(n,a))&&o.unshift(qr(n,l,s)):i||null!=(l=Ae(n,a))&&o.push(qr(n,l,s))),n=n.return}0!==o.length&&e.push({event:t,listeners:o})}var Yr=/\r\n?/g,Qr=/\u0000|\uFFFD/g;function Gr(e){return("string"===typeof e?e:""+e).replace(Yr,"\n").replace(Qr,"")}function Zr(e,t,n){if(t=Gr(t),Gr(e)!==t&&n)throw Error(a(425))}function Jr(){}var ei=null,ti=null;function ni(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var ri="function"===typeof setTimeout?setTimeout:void 0,ii="function"===typeof clearTimeout?clearTimeout:void 0,ai="function"===typeof Promise?Promise:void 0,oi="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof ai?function(e){return ai.resolve(null).then(e).catch(si)}:ri;function si(e){setTimeout(function(){throw e})}function li(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&8===i.nodeType)if("/$"===(n=i.data)){if(0===r)return e.removeChild(i),void Bt(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=i}while(n);Bt(t)}function ui(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function ci(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var di=Math.random().toString(36).slice(2),fi="__reactFiber$"+di,hi="__reactProps$"+di,pi="__reactContainer$"+di,mi="__reactEvents$"+di,gi="__reactListeners$"+di,yi="__reactHandles$"+di;function vi(e){var t=e[fi];if(t)return t;for(var n=e.parentNode;n;){if(t=n[pi]||n[fi]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=ci(e);null!==e;){if(n=e[fi])return n;e=ci(e)}return t}n=(e=n).parentNode}return null}function bi(e){return!(e=e[fi]||e[pi])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function xi(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(a(33))}function wi(e){return e[hi]||null}var Si=[],ki=-1;function Ei(e){return{current:e}}function ji(e){0>ki||(e.current=Si[ki],Si[ki]=null,ki--)}function Ti(e,t){ki++,Si[ki]=e.current,e.current=t}var Ci={},Pi=Ei(Ci),Ni=Ei(!1),Fi=Ci;function _i(e,t){var n=e.type.contextTypes;if(!n)return Ci;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i,a={};for(i in n)a[i]=t[i];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=a),a}function Ai(e){return null!==(e=e.childContextTypes)&&void 0!==e}function Ri(){ji(Ni),ji(Pi)}function Di(e,t,n){if(Pi.current!==Ci)throw Error(a(168));Ti(Pi,t),Ti(Ni,n)}function Mi(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!==typeof r.getChildContext)return n;for(var i in r=r.getChildContext())if(!(i in t))throw Error(a(108,W(e)||"Unknown",i));return L({},n,r)}function Oi(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Ci,Fi=Pi.current,Ti(Pi,e),Ti(Ni,Ni.current),!0}function Li(e,t,n){var r=e.stateNode;if(!r)throw Error(a(169));n?(e=Mi(e,t,Fi),r.__reactInternalMemoizedMergedChildContext=e,ji(Ni),ji(Pi),Ti(Pi,e)):ji(Ni),Ti(Ni,n)}var zi=null,Vi=!1,Ii=!1;function Ui(e){null===zi?zi=[e]:zi.push(e)}function Bi(){if(!Ii&&null!==zi){Ii=!0;var e=0,t=bt;try{var n=zi;for(bt=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}zi=null,Vi=!1}catch(i){throw null!==zi&&(zi=zi.slice(e+1)),$e(Je,Bi),i}finally{bt=t,Ii=!1}}return null}var Wi=[],Hi=0,qi=null,Ki=0,$i=[],Xi=0,Yi=null,Qi=1,Gi="";function Zi(e,t){Wi[Hi++]=Ki,Wi[Hi++]=qi,qi=e,Ki=t}function Ji(e,t,n){$i[Xi++]=Qi,$i[Xi++]=Gi,$i[Xi++]=Yi,Yi=e;var r=Qi;e=Gi;var i=32-ot(r)-1;r&=~(1<<i),n+=1;var a=32-ot(t)+i;if(30<a){var o=i-i%5;a=(r&(1<<o)-1).toString(32),r>>=o,i-=o,Qi=1<<32-ot(t)+i|n<<i|r,Gi=a+e}else Qi=1<<a|n<<i|r,Gi=e}function ea(e){null!==e.return&&(Zi(e,1),Ji(e,1,0))}function ta(e){for(;e===qi;)qi=Wi[--Hi],Wi[Hi]=null,Ki=Wi[--Hi],Wi[Hi]=null;for(;e===Yi;)Yi=$i[--Xi],$i[Xi]=null,Gi=$i[--Xi],$i[Xi]=null,Qi=$i[--Xi],$i[Xi]=null}var na=null,ra=null,ia=!1,aa=null;function oa(e,t){var n=_u(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function sa(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,na=e,ra=ui(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,na=e,ra=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==Yi?{id:Qi,overflow:Gi}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=_u(18,null,null,0)).stateNode=t,n.return=e,e.child=n,na=e,ra=null,!0);default:return!1}}function la(e){return 0!==(1&e.mode)&&0===(128&e.flags)}function ua(e){if(ia){var t=ra;if(t){var n=t;if(!sa(e,t)){if(la(e))throw Error(a(418));t=ui(n.nextSibling);var r=na;t&&sa(e,t)?oa(r,n):(e.flags=-4097&e.flags|2,ia=!1,na=e)}}else{if(la(e))throw Error(a(418));e.flags=-4097&e.flags|2,ia=!1,na=e}}}function ca(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;na=e}function da(e){if(e!==na)return!1;if(!ia)return ca(e),ia=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!ni(e.type,e.memoizedProps)),t&&(t=ra)){if(la(e))throw fa(),Error(a(418));for(;t;)oa(e,t),t=ui(t.nextSibling)}if(ca(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(a(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){ra=ui(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}ra=null}}else ra=na?ui(e.stateNode.nextSibling):null;return!0}function fa(){for(var e=ra;e;)e=ui(e.nextSibling)}function ha(){ra=na=null,ia=!1}function pa(e){null===aa?aa=[e]:aa.push(e)}var ma=x.ReactCurrentBatchConfig;function ga(e,t,n){if(null!==(e=n.ref)&&"function"!==typeof e&&"object"!==typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(a(309));var r=n.stateNode}if(!r)throw Error(a(147,e));var i=r,o=""+e;return null!==t&&null!==t.ref&&"function"===typeof t.ref&&t.ref._stringRef===o?t.ref:(t=function(e){var t=i.refs;null===e?delete t[o]:t[o]=e},t._stringRef=o,t)}if("string"!==typeof e)throw Error(a(284));if(!n._owner)throw Error(a(290,e))}return e}function ya(e,t){throw e=Object.prototype.toString.call(t),Error(a(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function va(e){return(0,e._init)(e._payload)}function ba(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function i(e,t){return(e=Ru(e,t)).index=0,e.sibling=null,e}function o(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function s(t){return e&&null===t.alternate&&(t.flags|=2),t}function l(e,t,n,r){return null===t||6!==t.tag?((t=Lu(n,e.mode,r)).return=e,t):((t=i(t,n)).return=e,t)}function u(e,t,n,r){var a=n.type;return a===k?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===a||"object"===typeof a&&null!==a&&a.$$typeof===A&&va(a)===t.type)?((r=i(t,n.props)).ref=ga(e,t,n),r.return=e,r):((r=Du(n.type,n.key,n.props,null,e.mode,r)).ref=ga(e,t,n),r.return=e,r)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=zu(n,e.mode,r)).return=e,t):((t=i(t,n.children||[])).return=e,t)}function d(e,t,n,r,a){return null===t||7!==t.tag?((t=Mu(n,e.mode,r,a)).return=e,t):((t=i(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t)return(t=Lu(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case w:return(n=Du(t.type,t.key,t.props,null,e.mode,n)).ref=ga(e,null,t),n.return=e,n;case S:return(t=zu(t,e.mode,n)).return=e,t;case A:return f(e,(0,t._init)(t._payload),n)}if(te(t)||M(t))return(t=Mu(t,e.mode,n,null)).return=e,t;ya(e,t)}return null}function h(e,t,n,r){var i=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n)return null!==i?null:l(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case w:return n.key===i?u(e,t,n,r):null;case S:return n.key===i?c(e,t,n,r):null;case A:return h(e,t,(i=n._init)(n._payload),r)}if(te(n)||M(n))return null!==i?null:d(e,t,n,r,null);ya(e,n)}return null}function p(e,t,n,r,i){if("string"===typeof r&&""!==r||"number"===typeof r)return l(t,e=e.get(n)||null,""+r,i);if("object"===typeof r&&null!==r){switch(r.$$typeof){case w:return u(t,e=e.get(null===r.key?n:r.key)||null,r,i);case S:return c(t,e=e.get(null===r.key?n:r.key)||null,r,i);case A:return p(e,t,n,(0,r._init)(r._payload),i)}if(te(r)||M(r))return d(t,e=e.get(n)||null,r,i,null);ya(t,r)}return null}function m(i,a,s,l){for(var u=null,c=null,d=a,m=a=0,g=null;null!==d&&m<s.length;m++){d.index>m?(g=d,d=null):g=d.sibling;var y=h(i,d,s[m],l);if(null===y){null===d&&(d=g);break}e&&d&&null===y.alternate&&t(i,d),a=o(y,a,m),null===c?u=y:c.sibling=y,c=y,d=g}if(m===s.length)return n(i,d),ia&&Zi(i,m),u;if(null===d){for(;m<s.length;m++)null!==(d=f(i,s[m],l))&&(a=o(d,a,m),null===c?u=d:c.sibling=d,c=d);return ia&&Zi(i,m),u}for(d=r(i,d);m<s.length;m++)null!==(g=p(d,i,m,s[m],l))&&(e&&null!==g.alternate&&d.delete(null===g.key?m:g.key),a=o(g,a,m),null===c?u=g:c.sibling=g,c=g);return e&&d.forEach(function(e){return t(i,e)}),ia&&Zi(i,m),u}function g(i,s,l,u){var c=M(l);if("function"!==typeof c)throw Error(a(150));if(null==(l=c.call(l)))throw Error(a(151));for(var d=c=null,m=s,g=s=0,y=null,v=l.next();null!==m&&!v.done;g++,v=l.next()){m.index>g?(y=m,m=null):y=m.sibling;var b=h(i,m,v.value,u);if(null===b){null===m&&(m=y);break}e&&m&&null===b.alternate&&t(i,m),s=o(b,s,g),null===d?c=b:d.sibling=b,d=b,m=y}if(v.done)return n(i,m),ia&&Zi(i,g),c;if(null===m){for(;!v.done;g++,v=l.next())null!==(v=f(i,v.value,u))&&(s=o(v,s,g),null===d?c=v:d.sibling=v,d=v);return ia&&Zi(i,g),c}for(m=r(i,m);!v.done;g++,v=l.next())null!==(v=p(m,i,g,v.value,u))&&(e&&null!==v.alternate&&m.delete(null===v.key?g:v.key),s=o(v,s,g),null===d?c=v:d.sibling=v,d=v);return e&&m.forEach(function(e){return t(i,e)}),ia&&Zi(i,g),c}return function e(r,a,o,l){if("object"===typeof o&&null!==o&&o.type===k&&null===o.key&&(o=o.props.children),"object"===typeof o&&null!==o){switch(o.$$typeof){case w:e:{for(var u=o.key,c=a;null!==c;){if(c.key===u){if((u=o.type)===k){if(7===c.tag){n(r,c.sibling),(a=i(c,o.props.children)).return=r,r=a;break e}}else if(c.elementType===u||"object"===typeof u&&null!==u&&u.$$typeof===A&&va(u)===c.type){n(r,c.sibling),(a=i(c,o.props)).ref=ga(r,c,o),a.return=r,r=a;break e}n(r,c);break}t(r,c),c=c.sibling}o.type===k?((a=Mu(o.props.children,r.mode,l,o.key)).return=r,r=a):((l=Du(o.type,o.key,o.props,null,r.mode,l)).ref=ga(r,a,o),l.return=r,r=l)}return s(r);case S:e:{for(c=o.key;null!==a;){if(a.key===c){if(4===a.tag&&a.stateNode.containerInfo===o.containerInfo&&a.stateNode.implementation===o.implementation){n(r,a.sibling),(a=i(a,o.children||[])).return=r,r=a;break e}n(r,a);break}t(r,a),a=a.sibling}(a=zu(o,r.mode,l)).return=r,r=a}return s(r);case A:return e(r,a,(c=o._init)(o._payload),l)}if(te(o))return m(r,a,o,l);if(M(o))return g(r,a,o,l);ya(r,o)}return"string"===typeof o&&""!==o||"number"===typeof o?(o=""+o,null!==a&&6===a.tag?(n(r,a.sibling),(a=i(a,o)).return=r,r=a):(n(r,a),(a=Lu(o,r.mode,l)).return=r,r=a),s(r)):n(r,a)}}var xa=ba(!0),wa=ba(!1),Sa=Ei(null),ka=null,Ea=null,ja=null;function Ta(){ja=Ea=ka=null}function Ca(e){var t=Sa.current;ji(Sa),e._currentValue=t}function Pa(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Na(e,t){ka=e,ja=Ea=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(bs=!0),e.firstContext=null)}function Fa(e){var t=e._currentValue;if(ja!==e)if(e={context:e,memoizedValue:t,next:null},null===Ea){if(null===ka)throw Error(a(308));Ea=e,ka.dependencies={lanes:0,firstContext:e}}else Ea=Ea.next=e;return t}var _a=null;function Aa(e){null===_a?_a=[e]:_a.push(e)}function Ra(e,t,n,r){var i=t.interleaved;return null===i?(n.next=n,Aa(t)):(n.next=i.next,i.next=n),t.interleaved=n,Da(e,r)}function Da(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var Ma=!1;function Oa(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function La(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function za(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Va(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&Pl)){var i=r.pending;return null===i?t.next=t:(t.next=i.next,i.next=t),r.pending=t,Da(e,n)}return null===(i=r.interleaved)?(t.next=t,Aa(r)):(t.next=i.next,i.next=t),r.interleaved=t,Da(e,n)}function Ia(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194240&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,vt(e,n)}}function Ua(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var i=null,a=null;if(null!==(n=n.firstBaseUpdate)){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===a?i=a=o:a=a.next=o,n=n.next}while(null!==n);null===a?i=a=t:a=a.next=t}else i=a=t;return n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:a,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Ba(e,t,n,r){var i=e.updateQueue;Ma=!1;var a=i.firstBaseUpdate,o=i.lastBaseUpdate,s=i.shared.pending;if(null!==s){i.shared.pending=null;var l=s,u=l.next;l.next=null,null===o?a=u:o.next=u,o=l;var c=e.alternate;null!==c&&((s=(c=c.updateQueue).lastBaseUpdate)!==o&&(null===s?c.firstBaseUpdate=u:s.next=u,c.lastBaseUpdate=l))}if(null!==a){var d=i.baseState;for(o=0,c=u=l=null,s=a;;){var f=s.lane,h=s.eventTime;if((r&f)===f){null!==c&&(c=c.next={eventTime:h,lane:0,tag:s.tag,payload:s.payload,callback:s.callback,next:null});e:{var p=e,m=s;switch(f=t,h=n,m.tag){case 1:if("function"===typeof(p=m.payload)){d=p.call(h,d,f);break e}d=p;break e;case 3:p.flags=-65537&p.flags|128;case 0:if(null===(f="function"===typeof(p=m.payload)?p.call(h,d,f):p)||void 0===f)break e;d=L({},d,f);break e;case 2:Ma=!0}}null!==s.callback&&0!==s.lane&&(e.flags|=64,null===(f=i.effects)?i.effects=[s]:f.push(s))}else h={eventTime:h,lane:f,tag:s.tag,payload:s.payload,callback:s.callback,next:null},null===c?(u=c=h,l=d):c=c.next=h,o|=f;if(null===(s=s.next)){if(null===(s=i.shared.pending))break;s=(f=s).next,f.next=null,i.lastBaseUpdate=f,i.shared.pending=null}}if(null===c&&(l=d),i.baseState=l,i.firstBaseUpdate=u,i.lastBaseUpdate=c,null!==(t=i.shared.interleaved)){i=t;do{o|=i.lane,i=i.next}while(i!==t)}else null===a&&(i.shared.lanes=0);Ol|=o,e.lanes=o,e.memoizedState=d}}function Wa(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(null!==i){if(r.callback=null,r=n,"function"!==typeof i)throw Error(a(191,i));i.call(r)}}}var Ha={},qa=Ei(Ha),Ka=Ei(Ha),$a=Ei(Ha);function Xa(e){if(e===Ha)throw Error(a(174));return e}function Ya(e,t){switch(Ti($a,t),Ti(Ka,e),Ti(qa,Ha),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:le(null,"");break;default:t=le(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}ji(qa),Ti(qa,t)}function Qa(){ji(qa),ji(Ka),ji($a)}function Ga(e){Xa($a.current);var t=Xa(qa.current),n=le(t,e.type);t!==n&&(Ti(Ka,e),Ti(qa,n))}function Za(e){Ka.current===e&&(ji(qa),ji(Ka))}var Ja=Ei(0);function eo(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var to=[];function no(){for(var e=0;e<to.length;e++)to[e]._workInProgressVersionPrimary=null;to.length=0}var ro=x.ReactCurrentDispatcher,io=x.ReactCurrentBatchConfig,ao=0,oo=null,so=null,lo=null,uo=!1,co=!1,fo=0,ho=0;function po(){throw Error(a(321))}function mo(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!sr(e[n],t[n]))return!1;return!0}function go(e,t,n,r,i,o){if(ao=o,oo=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ro.current=null===e||null===e.memoizedState?Jo:es,e=n(r,i),co){o=0;do{if(co=!1,fo=0,25<=o)throw Error(a(301));o+=1,lo=so=null,t.updateQueue=null,ro.current=ts,e=n(r,i)}while(co)}if(ro.current=Zo,t=null!==so&&null!==so.next,ao=0,lo=so=oo=null,uo=!1,t)throw Error(a(300));return e}function yo(){var e=0!==fo;return fo=0,e}function vo(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===lo?oo.memoizedState=lo=e:lo=lo.next=e,lo}function bo(){if(null===so){var e=oo.alternate;e=null!==e?e.memoizedState:null}else e=so.next;var t=null===lo?oo.memoizedState:lo.next;if(null!==t)lo=t,so=e;else{if(null===e)throw Error(a(310));e={memoizedState:(so=e).memoizedState,baseState:so.baseState,baseQueue:so.baseQueue,queue:so.queue,next:null},null===lo?oo.memoizedState=lo=e:lo=lo.next=e}return lo}function xo(e,t){return"function"===typeof t?t(e):t}function wo(e){var t=bo(),n=t.queue;if(null===n)throw Error(a(311));n.lastRenderedReducer=e;var r=so,i=r.baseQueue,o=n.pending;if(null!==o){if(null!==i){var s=i.next;i.next=o.next,o.next=s}r.baseQueue=i=o,n.pending=null}if(null!==i){o=i.next,r=r.baseState;var l=s=null,u=null,c=o;do{var d=c.lane;if((ao&d)===d)null!==u&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var f={lane:d,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};null===u?(l=u=f,s=r):u=u.next=f,oo.lanes|=d,Ol|=d}c=c.next}while(null!==c&&c!==o);null===u?s=r:u.next=l,sr(r,t.memoizedState)||(bs=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=u,n.lastRenderedState=r}if(null!==(e=n.interleaved)){i=e;do{o=i.lane,oo.lanes|=o,Ol|=o,i=i.next}while(i!==e)}else null===i&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function So(e){var t=bo(),n=t.queue;if(null===n)throw Error(a(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,o=t.memoizedState;if(null!==i){n.pending=null;var s=i=i.next;do{o=e(o,s.action),s=s.next}while(s!==i);sr(o,t.memoizedState)||(bs=!0),t.memoizedState=o,null===t.baseQueue&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function ko(){}function Eo(e,t){var n=oo,r=bo(),i=t(),o=!sr(r.memoizedState,i);if(o&&(r.memoizedState=i,bs=!0),r=r.queue,Oo(Co.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||null!==lo&&1&lo.memoizedState.tag){if(n.flags|=2048,_o(9,To.bind(null,n,r,i,t),void 0,null),null===Nl)throw Error(a(349));0!==(30&ao)||jo(n,t,i)}return i}function jo(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=oo.updateQueue)?(t={lastEffect:null,stores:null},oo.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function To(e,t,n,r){t.value=n,t.getSnapshot=r,Po(t)&&No(e)}function Co(e,t,n){return n(function(){Po(t)&&No(e)})}function Po(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!sr(e,n)}catch(r){return!0}}function No(e){var t=Da(e,1);null!==t&&nu(t,e,1,-1)}function Fo(e){var t=vo();return"function"===typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:xo,lastRenderedState:e},t.queue=e,e=e.dispatch=Xo.bind(null,oo,e),[t.memoizedState,e]}function _o(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=oo.updateQueue)?(t={lastEffect:null,stores:null},oo.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function Ao(){return bo().memoizedState}function Ro(e,t,n,r){var i=vo();oo.flags|=e,i.memoizedState=_o(1|t,n,void 0,void 0===r?null:r)}function Do(e,t,n,r){var i=bo();r=void 0===r?null:r;var a=void 0;if(null!==so){var o=so.memoizedState;if(a=o.destroy,null!==r&&mo(r,o.deps))return void(i.memoizedState=_o(t,n,a,r))}oo.flags|=e,i.memoizedState=_o(1|t,n,a,r)}function Mo(e,t){return Ro(8390656,8,e,t)}function Oo(e,t){return Do(2048,8,e,t)}function Lo(e,t){return Do(4,2,e,t)}function zo(e,t){return Do(4,4,e,t)}function Vo(e,t){return"function"===typeof t?(e=e(),t(e),function(){t(null)}):null!==t&&void 0!==t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Io(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Do(4,4,Vo.bind(null,t,e),n)}function Uo(){}function Bo(e,t){var n=bo();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&mo(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Wo(e,t){var n=bo();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&mo(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Ho(e,t,n){return 0===(21&ao)?(e.baseState&&(e.baseState=!1,bs=!0),e.memoizedState=n):(sr(n,t)||(n=mt(),oo.lanes|=n,Ol|=n,e.baseState=!0),t)}function qo(e,t){var n=bt;bt=0!==n&&4>n?n:4,e(!0);var r=io.transition;io.transition={};try{e(!1),t()}finally{bt=n,io.transition=r}}function Ko(){return bo().memoizedState}function $o(e,t,n){var r=tu(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Yo(e))Qo(t,n);else if(null!==(n=Ra(e,t,n,r))){nu(n,e,r,eu()),Go(n,t,r)}}function Xo(e,t,n){var r=tu(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Yo(e))Qo(t,i);else{var a=e.alternate;if(0===e.lanes&&(null===a||0===a.lanes)&&null!==(a=t.lastRenderedReducer))try{var o=t.lastRenderedState,s=a(o,n);if(i.hasEagerState=!0,i.eagerState=s,sr(s,o)){var l=t.interleaved;return null===l?(i.next=i,Aa(t)):(i.next=l.next,l.next=i),void(t.interleaved=i)}}catch(u){}null!==(n=Ra(e,t,i,r))&&(nu(n,e,r,i=eu()),Go(n,t,r))}}function Yo(e){var t=e.alternate;return e===oo||null!==t&&t===oo}function Qo(e,t){co=uo=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Go(e,t,n){if(0!==(4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,vt(e,n)}}var Zo={readContext:Fa,useCallback:po,useContext:po,useEffect:po,useImperativeHandle:po,useInsertionEffect:po,useLayoutEffect:po,useMemo:po,useReducer:po,useRef:po,useState:po,useDebugValue:po,useDeferredValue:po,useTransition:po,useMutableSource:po,useSyncExternalStore:po,useId:po,unstable_isNewReconciler:!1},Jo={readContext:Fa,useCallback:function(e,t){return vo().memoizedState=[e,void 0===t?null:t],e},useContext:Fa,useEffect:Mo,useImperativeHandle:function(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Ro(4194308,4,Vo.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Ro(4194308,4,e,t)},useInsertionEffect:function(e,t){return Ro(4,2,e,t)},useMemo:function(e,t){var n=vo();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=vo();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=$o.bind(null,oo,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},vo().memoizedState=e},useState:Fo,useDebugValue:Uo,useDeferredValue:function(e){return vo().memoizedState=e},useTransition:function(){var e=Fo(!1),t=e[0];return e=qo.bind(null,e[1]),vo().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=oo,i=vo();if(ia){if(void 0===n)throw Error(a(407));n=n()}else{if(n=t(),null===Nl)throw Error(a(349));0!==(30&ao)||jo(r,t,n)}i.memoizedState=n;var o={value:n,getSnapshot:t};return i.queue=o,Mo(Co.bind(null,r,o,e),[e]),r.flags|=2048,_o(9,To.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=vo(),t=Nl.identifierPrefix;if(ia){var n=Gi;t=":"+t+"R"+(n=(Qi&~(1<<32-ot(Qi)-1)).toString(32)+n),0<(n=fo++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=ho++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},es={readContext:Fa,useCallback:Bo,useContext:Fa,useEffect:Oo,useImperativeHandle:Io,useInsertionEffect:Lo,useLayoutEffect:zo,useMemo:Wo,useReducer:wo,useRef:Ao,useState:function(){return wo(xo)},useDebugValue:Uo,useDeferredValue:function(e){return Ho(bo(),so.memoizedState,e)},useTransition:function(){return[wo(xo)[0],bo().memoizedState]},useMutableSource:ko,useSyncExternalStore:Eo,useId:Ko,unstable_isNewReconciler:!1},ts={readContext:Fa,useCallback:Bo,useContext:Fa,useEffect:Oo,useImperativeHandle:Io,useInsertionEffect:Lo,useLayoutEffect:zo,useMemo:Wo,useReducer:So,useRef:Ao,useState:function(){return So(xo)},useDebugValue:Uo,useDeferredValue:function(e){var t=bo();return null===so?t.memoizedState=e:Ho(t,so.memoizedState,e)},useTransition:function(){return[So(xo)[0],bo().memoizedState]},useMutableSource:ko,useSyncExternalStore:Eo,useId:Ko,unstable_isNewReconciler:!1};function ns(e,t){if(e&&e.defaultProps){for(var n in t=L({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function rs(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:L({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var is={isMounted:function(e){return!!(e=e._reactInternals)&&Be(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=eu(),i=tu(e),a=za(r,i);a.payload=t,void 0!==n&&null!==n&&(a.callback=n),null!==(t=Va(e,a,i))&&(nu(t,e,i,r),Ia(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=eu(),i=tu(e),a=za(r,i);a.tag=1,a.payload=t,void 0!==n&&null!==n&&(a.callback=n),null!==(t=Va(e,a,i))&&(nu(t,e,i,r),Ia(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=eu(),r=tu(e),i=za(n,r);i.tag=2,void 0!==t&&null!==t&&(i.callback=t),null!==(t=Va(e,i,r))&&(nu(t,e,r,n),Ia(t,e,r))}};function as(e,t,n,r,i,a,o){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,a,o):!t.prototype||!t.prototype.isPureReactComponent||(!lr(n,r)||!lr(i,a))}function os(e,t,n){var r=!1,i=Ci,a=t.contextType;return"object"===typeof a&&null!==a?a=Fa(a):(i=Ai(t)?Fi:Pi.current,a=(r=null!==(r=t.contextTypes)&&void 0!==r)?_i(e,i):Ci),t=new t(n,a),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=is,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=a),t}function ss(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&is.enqueueReplaceState(t,t.state,null)}function ls(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},Oa(e);var a=t.contextType;"object"===typeof a&&null!==a?i.context=Fa(a):(a=Ai(t)?Fi:Pi.current,i.context=_i(e,a)),i.state=e.memoizedState,"function"===typeof(a=t.getDerivedStateFromProps)&&(rs(e,t,a,n),i.state=e.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof i.getSnapshotBeforeUpdate||"function"!==typeof i.UNSAFE_componentWillMount&&"function"!==typeof i.componentWillMount||(t=i.state,"function"===typeof i.componentWillMount&&i.componentWillMount(),"function"===typeof i.UNSAFE_componentWillMount&&i.UNSAFE_componentWillMount(),t!==i.state&&is.enqueueReplaceState(i,i.state,null),Ba(e,n,i,r),i.state=e.memoizedState),"function"===typeof i.componentDidMount&&(e.flags|=4194308)}function us(e,t){try{var n="",r=t;do{n+=U(r),r=r.return}while(r);var i=n}catch(a){i="\nError generating stack: "+a.message+"\n"+a.stack}return{value:e,source:t,stack:i,digest:null}}function cs(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function ds(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var fs="function"===typeof WeakMap?WeakMap:Map;function hs(e,t,n){(n=za(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Hl||(Hl=!0,ql=r),ds(0,t)},n}function ps(e,t,n){(n=za(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"===typeof r){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){ds(0,t)}}var a=e.stateNode;return null!==a&&"function"===typeof a.componentDidCatch&&(n.callback=function(){ds(0,t),"function"!==typeof r&&(null===Kl?Kl=new Set([this]):Kl.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function ms(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new fs;var i=new Set;r.set(t,i)}else void 0===(i=r.get(t))&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=ju.bind(null,e,t,n),t.then(e,e))}function gs(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function ys(e,t,n,r,i){return 0===(1&e.mode)?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=za(-1,1)).tag=2,Va(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=i,e)}var vs=x.ReactCurrentOwner,bs=!1;function xs(e,t,n,r){t.child=null===e?wa(t,null,n,r):xa(t,e.child,n,r)}function ws(e,t,n,r,i){n=n.render;var a=t.ref;return Na(t,i),r=go(e,t,n,r,a,i),n=yo(),null===e||bs?(ia&&n&&ea(t),t.flags|=1,xs(e,t,r,i),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,Hs(e,t,i))}function Ss(e,t,n,r,i){if(null===e){var a=n.type;return"function"!==typeof a||Au(a)||void 0!==a.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=Du(n.type,null,r,t,t.mode,i)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=a,ks(e,t,a,r,i))}if(a=e.child,0===(e.lanes&i)){var o=a.memoizedProps;if((n=null!==(n=n.compare)?n:lr)(o,r)&&e.ref===t.ref)return Hs(e,t,i)}return t.flags|=1,(e=Ru(a,r)).ref=t.ref,e.return=t,t.child=e}function ks(e,t,n,r,i){if(null!==e){var a=e.memoizedProps;if(lr(a,r)&&e.ref===t.ref){if(bs=!1,t.pendingProps=r=a,0===(e.lanes&i))return t.lanes=e.lanes,Hs(e,t,i);0!==(131072&e.flags)&&(bs=!0)}}return Ts(e,t,n,r,i)}function Es(e,t,n){var r=t.pendingProps,i=r.children,a=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(0===(1&t.mode))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Ti(Rl,Al),Al|=n;else{if(0===(1073741824&n))return e=null!==a?a.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Ti(Rl,Al),Al|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==a?a.baseLanes:n,Ti(Rl,Al),Al|=r}else null!==a?(r=a.baseLanes|n,t.memoizedState=null):r=n,Ti(Rl,Al),Al|=r;return xs(e,t,i,n),t.child}function js(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Ts(e,t,n,r,i){var a=Ai(n)?Fi:Pi.current;return a=_i(t,a),Na(t,i),n=go(e,t,n,r,a,i),r=yo(),null===e||bs?(ia&&r&&ea(t),t.flags|=1,xs(e,t,n,i),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,Hs(e,t,i))}function Cs(e,t,n,r,i){if(Ai(n)){var a=!0;Oi(t)}else a=!1;if(Na(t,i),null===t.stateNode)Ws(e,t),os(t,n,r),ls(t,n,r,i),r=!0;else if(null===e){var o=t.stateNode,s=t.memoizedProps;o.props=s;var l=o.context,u=n.contextType;"object"===typeof u&&null!==u?u=Fa(u):u=_i(t,u=Ai(n)?Fi:Pi.current);var c=n.getDerivedStateFromProps,d="function"===typeof c||"function"===typeof o.getSnapshotBeforeUpdate;d||"function"!==typeof o.UNSAFE_componentWillReceiveProps&&"function"!==typeof o.componentWillReceiveProps||(s!==r||l!==u)&&ss(t,o,r,u),Ma=!1;var f=t.memoizedState;o.state=f,Ba(t,r,o,i),l=t.memoizedState,s!==r||f!==l||Ni.current||Ma?("function"===typeof c&&(rs(t,n,c,r),l=t.memoizedState),(s=Ma||as(t,n,s,r,f,l,u))?(d||"function"!==typeof o.UNSAFE_componentWillMount&&"function"!==typeof o.componentWillMount||("function"===typeof o.componentWillMount&&o.componentWillMount(),"function"===typeof o.UNSAFE_componentWillMount&&o.UNSAFE_componentWillMount()),"function"===typeof o.componentDidMount&&(t.flags|=4194308)):("function"===typeof o.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),o.props=r,o.state=l,o.context=u,r=s):("function"===typeof o.componentDidMount&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,La(e,t),s=t.memoizedProps,u=t.type===t.elementType?s:ns(t.type,s),o.props=u,d=t.pendingProps,f=o.context,"object"===typeof(l=n.contextType)&&null!==l?l=Fa(l):l=_i(t,l=Ai(n)?Fi:Pi.current);var h=n.getDerivedStateFromProps;(c="function"===typeof h||"function"===typeof o.getSnapshotBeforeUpdate)||"function"!==typeof o.UNSAFE_componentWillReceiveProps&&"function"!==typeof o.componentWillReceiveProps||(s!==d||f!==l)&&ss(t,o,r,l),Ma=!1,f=t.memoizedState,o.state=f,Ba(t,r,o,i);var p=t.memoizedState;s!==d||f!==p||Ni.current||Ma?("function"===typeof h&&(rs(t,n,h,r),p=t.memoizedState),(u=Ma||as(t,n,u,r,f,p,l)||!1)?(c||"function"!==typeof o.UNSAFE_componentWillUpdate&&"function"!==typeof o.componentWillUpdate||("function"===typeof o.componentWillUpdate&&o.componentWillUpdate(r,p,l),"function"===typeof o.UNSAFE_componentWillUpdate&&o.UNSAFE_componentWillUpdate(r,p,l)),"function"===typeof o.componentDidUpdate&&(t.flags|=4),"function"===typeof o.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof o.componentDidUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof o.getSnapshotBeforeUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=p),o.props=r,o.state=p,o.context=l,r=u):("function"!==typeof o.componentDidUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof o.getSnapshotBeforeUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return Ps(e,t,n,r,a,i)}function Ps(e,t,n,r,i,a){js(e,t);var o=0!==(128&t.flags);if(!r&&!o)return i&&Li(t,n,!1),Hs(e,t,a);r=t.stateNode,vs.current=t;var s=o&&"function"!==typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&o?(t.child=xa(t,e.child,null,a),t.child=xa(t,null,s,a)):xs(e,t,s,a),t.memoizedState=r.state,i&&Li(t,n,!0),t.child}function Ns(e){var t=e.stateNode;t.pendingContext?Di(0,t.pendingContext,t.pendingContext!==t.context):t.context&&Di(0,t.context,!1),Ya(e,t.containerInfo)}function Fs(e,t,n,r,i){return ha(),pa(i),t.flags|=256,xs(e,t,n,r),t.child}var _s,As,Rs,Ds,Ms={dehydrated:null,treeContext:null,retryLane:0};function Os(e){return{baseLanes:e,cachePool:null,transitions:null}}function Ls(e,t,n){var r,i=t.pendingProps,o=Ja.current,s=!1,l=0!==(128&t.flags);if((r=l)||(r=(null===e||null!==e.memoizedState)&&0!==(2&o)),r?(s=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(o|=1),Ti(Ja,1&o),null===e)return ua(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(0===(1&t.mode)?t.lanes=1:"$!"===e.data?t.lanes=8:t.lanes=1073741824,null):(l=i.children,e=i.fallback,s?(i=t.mode,s=t.child,l={mode:"hidden",children:l},0===(1&i)&&null!==s?(s.childLanes=0,s.pendingProps=l):s=Ou(l,i,0,null),e=Mu(e,i,n,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=Os(n),t.memoizedState=Ms,e):zs(t,l));if(null!==(o=e.memoizedState)&&null!==(r=o.dehydrated))return function(e,t,n,r,i,o,s){if(n)return 256&t.flags?(t.flags&=-257,Vs(e,t,s,r=cs(Error(a(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(o=r.fallback,i=t.mode,r=Ou({mode:"visible",children:r.children},i,0,null),(o=Mu(o,i,s,null)).flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,0!==(1&t.mode)&&xa(t,e.child,null,s),t.child.memoizedState=Os(s),t.memoizedState=Ms,o);if(0===(1&t.mode))return Vs(e,t,s,null);if("$!"===i.data){if(r=i.nextSibling&&i.nextSibling.dataset)var l=r.dgst;return r=l,Vs(e,t,s,r=cs(o=Error(a(419)),r,void 0))}if(l=0!==(s&e.childLanes),bs||l){if(null!==(r=Nl)){switch(s&-s){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}0!==(i=0!==(i&(r.suspendedLanes|s))?0:i)&&i!==o.retryLane&&(o.retryLane=i,Da(e,i),nu(r,e,i,-1))}return mu(),Vs(e,t,s,r=cs(Error(a(421))))}return"$?"===i.data?(t.flags|=128,t.child=e.child,t=Cu.bind(null,e),i._reactRetry=t,null):(e=o.treeContext,ra=ui(i.nextSibling),na=t,ia=!0,aa=null,null!==e&&($i[Xi++]=Qi,$i[Xi++]=Gi,$i[Xi++]=Yi,Qi=e.id,Gi=e.overflow,Yi=t),t=zs(t,r.children),t.flags|=4096,t)}(e,t,l,i,r,o,n);if(s){s=i.fallback,l=t.mode,r=(o=e.child).sibling;var u={mode:"hidden",children:i.children};return 0===(1&l)&&t.child!==o?((i=t.child).childLanes=0,i.pendingProps=u,t.deletions=null):(i=Ru(o,u)).subtreeFlags=14680064&o.subtreeFlags,null!==r?s=Ru(r,s):(s=Mu(s,l,n,null)).flags|=2,s.return=t,i.return=t,i.sibling=s,t.child=i,i=s,s=t.child,l=null===(l=e.child.memoizedState)?Os(n):{baseLanes:l.baseLanes|n,cachePool:null,transitions:l.transitions},s.memoizedState=l,s.childLanes=e.childLanes&~n,t.memoizedState=Ms,i}return e=(s=e.child).sibling,i=Ru(s,{mode:"visible",children:i.children}),0===(1&t.mode)&&(i.lanes=n),i.return=t,i.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=i,t.memoizedState=null,i}function zs(e,t){return(t=Ou({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function Vs(e,t,n,r){return null!==r&&pa(r),xa(t,e.child,null,n),(e=zs(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Is(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),Pa(e.return,t,n)}function Us(e,t,n,r,i){var a=e.memoizedState;null===a?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(a.isBackwards=t,a.rendering=null,a.renderingStartTime=0,a.last=r,a.tail=n,a.tailMode=i)}function Bs(e,t,n){var r=t.pendingProps,i=r.revealOrder,a=r.tail;if(xs(e,t,r.children,n),0!==(2&(r=Ja.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Is(e,n,t);else if(19===e.tag)Is(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Ti(Ja,r),0===(1&t.mode))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;null!==n;)null!==(e=n.alternate)&&null===eo(e)&&(i=n),n=n.sibling;null===(n=i)?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),Us(t,!1,i,n,a);break;case"backwards":for(n=null,i=t.child,t.child=null;null!==i;){if(null!==(e=i.alternate)&&null===eo(e)){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}Us(t,!0,n,null,a);break;case"together":Us(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Ws(e,t){0===(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Hs(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),Ol|=t.lanes,0===(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(a(153));if(null!==t.child){for(n=Ru(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Ru(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function qs(e,t){if(!ia)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Ks(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;null!==i;)n|=i.lanes|i.childLanes,r|=14680064&i.subtreeFlags,r|=14680064&i.flags,i.return=e,i=i.sibling;else for(i=e.child;null!==i;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function $s(e,t,n){var r=t.pendingProps;switch(ta(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ks(t),null;case 1:case 17:return Ai(t.type)&&Ri(),Ks(t),null;case 3:return r=t.stateNode,Qa(),ji(Ni),ji(Pi),no(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(da(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,null!==aa&&(ou(aa),aa=null))),As(e,t),Ks(t),null;case 5:Za(t);var i=Xa($a.current);if(n=t.type,null!==e&&null!=t.stateNode)Rs(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(null===t.stateNode)throw Error(a(166));return Ks(t),null}if(e=Xa(qa.current),da(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[fi]=t,r[hi]=o,e=0!==(1&t.mode),n){case"dialog":Vr("cancel",r),Vr("close",r);break;case"iframe":case"object":case"embed":Vr("load",r);break;case"video":case"audio":for(i=0;i<Mr.length;i++)Vr(Mr[i],r);break;case"source":Vr("error",r);break;case"img":case"image":case"link":Vr("error",r),Vr("load",r);break;case"details":Vr("toggle",r);break;case"input":Q(r,o),Vr("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},Vr("invalid",r);break;case"textarea":ie(r,o),Vr("invalid",r)}for(var l in ve(n,o),i=null,o)if(o.hasOwnProperty(l)){var u=o[l];"children"===l?"string"===typeof u?r.textContent!==u&&(!0!==o.suppressHydrationWarning&&Zr(r.textContent,u,e),i=["children",u]):"number"===typeof u&&r.textContent!==""+u&&(!0!==o.suppressHydrationWarning&&Zr(r.textContent,u,e),i=["children",""+u]):s.hasOwnProperty(l)&&null!=u&&"onScroll"===l&&Vr("scroll",r)}switch(n){case"input":K(r),J(r,o,!0);break;case"textarea":K(r),oe(r);break;case"select":case"option":break;default:"function"===typeof o.onClick&&(r.onclick=Jr)}r=i,t.updateQueue=r,null!==r&&(t.flags|=4)}else{l=9===i.nodeType?i:i.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=se(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=l.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"===typeof r.is?e=l.createElement(n,{is:r.is}):(e=l.createElement(n),"select"===n&&(l=e,r.multiple?l.multiple=!0:r.size&&(l.size=r.size))):e=l.createElementNS(e,n),e[fi]=t,e[hi]=r,_s(e,t,!1,!1),t.stateNode=e;e:{switch(l=be(n,r),n){case"dialog":Vr("cancel",e),Vr("close",e),i=r;break;case"iframe":case"object":case"embed":Vr("load",e),i=r;break;case"video":case"audio":for(i=0;i<Mr.length;i++)Vr(Mr[i],e);i=r;break;case"source":Vr("error",e),i=r;break;case"img":case"image":case"link":Vr("error",e),Vr("load",e),i=r;break;case"details":Vr("toggle",e),i=r;break;case"input":Q(e,r),i=Y(e,r),Vr("invalid",e);break;case"option":default:i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=L({},r,{value:void 0}),Vr("invalid",e);break;case"textarea":ie(e,r),i=re(e,r),Vr("invalid",e)}for(o in ve(n,i),u=i)if(u.hasOwnProperty(o)){var c=u[o];"style"===o?ge(e,c):"dangerouslySetInnerHTML"===o?null!=(c=c?c.__html:void 0)&&de(e,c):"children"===o?"string"===typeof c?("textarea"!==n||""!==c)&&fe(e,c):"number"===typeof c&&fe(e,""+c):"suppressContentEditableWarning"!==o&&"suppressHydrationWarning"!==o&&"autoFocus"!==o&&(s.hasOwnProperty(o)?null!=c&&"onScroll"===o&&Vr("scroll",e):null!=c&&b(e,o,c,l))}switch(n){case"input":K(e),J(e,r,!1);break;case"textarea":K(e),oe(e);break;case"option":null!=r.value&&e.setAttribute("value",""+H(r.value));break;case"select":e.multiple=!!r.multiple,null!=(o=r.value)?ne(e,!!r.multiple,o,!1):null!=r.defaultValue&&ne(e,!!r.multiple,r.defaultValue,!0);break;default:"function"===typeof i.onClick&&(e.onclick=Jr)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return Ks(t),null;case 6:if(e&&null!=t.stateNode)Ds(e,t,e.memoizedProps,r);else{if("string"!==typeof r&&null===t.stateNode)throw Error(a(166));if(n=Xa($a.current),Xa(qa.current),da(t)){if(r=t.stateNode,n=t.memoizedProps,r[fi]=t,(o=r.nodeValue!==n)&&null!==(e=na))switch(e.tag){case 3:Zr(r.nodeValue,n,0!==(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Zr(r.nodeValue,n,0!==(1&e.mode))}o&&(t.flags|=4)}else(r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[fi]=t,t.stateNode=r}return Ks(t),null;case 13:if(ji(Ja),r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(ia&&null!==ra&&0!==(1&t.mode)&&0===(128&t.flags))fa(),ha(),t.flags|=98560,o=!1;else if(o=da(t),null!==r&&null!==r.dehydrated){if(null===e){if(!o)throw Error(a(318));if(!(o=null!==(o=t.memoizedState)?o.dehydrated:null))throw Error(a(317));o[fi]=t}else ha(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;Ks(t),o=!1}else null!==aa&&(ou(aa),aa=null),o=!0;if(!o)return 65536&t.flags?t:null}return 0!==(128&t.flags)?(t.lanes=n,t):((r=null!==r)!==(null!==e&&null!==e.memoizedState)&&r&&(t.child.flags|=8192,0!==(1&t.mode)&&(null===e||0!==(1&Ja.current)?0===Dl&&(Dl=3):mu())),null!==t.updateQueue&&(t.flags|=4),Ks(t),null);case 4:return Qa(),As(e,t),null===e&&Br(t.stateNode.containerInfo),Ks(t),null;case 10:return Ca(t.type._context),Ks(t),null;case 19:if(ji(Ja),null===(o=t.memoizedState))return Ks(t),null;if(r=0!==(128&t.flags),null===(l=o.rendering))if(r)qs(o,!1);else{if(0!==Dl||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(l=eo(e))){for(t.flags|=128,qs(o,!1),null!==(r=l.updateQueue)&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;null!==n;)e=r,(o=n).flags&=14680066,null===(l=o.alternate)?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=l.childLanes,o.lanes=l.lanes,o.child=l.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=l.memoizedProps,o.memoizedState=l.memoizedState,o.updateQueue=l.updateQueue,o.type=l.type,e=l.dependencies,o.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Ti(Ja,1&Ja.current|2),t.child}e=e.sibling}null!==o.tail&&Ge()>Bl&&(t.flags|=128,r=!0,qs(o,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=eo(l))){if(t.flags|=128,r=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),qs(o,!0),null===o.tail&&"hidden"===o.tailMode&&!l.alternate&&!ia)return Ks(t),null}else 2*Ge()-o.renderingStartTime>Bl&&1073741824!==n&&(t.flags|=128,r=!0,qs(o,!1),t.lanes=4194304);o.isBackwards?(l.sibling=t.child,t.child=l):(null!==(n=o.last)?n.sibling=l:t.child=l,o.last=l)}return null!==o.tail?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=Ge(),t.sibling=null,n=Ja.current,Ti(Ja,r?1&n|2:1&n),t):(Ks(t),null);case 22:case 23:return du(),r=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==r&&(t.flags|=8192),r&&0!==(1&t.mode)?0!==(1073741824&Al)&&(Ks(t),6&t.subtreeFlags&&(t.flags|=8192)):Ks(t),null;case 24:case 25:return null}throw Error(a(156,t.tag))}function Xs(e,t){switch(ta(t),t.tag){case 1:return Ai(t.type)&&Ri(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return Qa(),ji(Ni),ji(Pi),no(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 5:return Za(t),null;case 13:if(ji(Ja),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(a(340));ha()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return ji(Ja),null;case 4:return Qa(),null;case 10:return Ca(t.type._context),null;case 22:case 23:return du(),null;default:return null}}_s=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},As=function(){},Rs=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,Xa(qa.current);var a,o=null;switch(n){case"input":i=Y(e,i),r=Y(e,r),o=[];break;case"select":i=L({},i,{value:void 0}),r=L({},r,{value:void 0}),o=[];break;case"textarea":i=re(e,i),r=re(e,r),o=[];break;default:"function"!==typeof i.onClick&&"function"===typeof r.onClick&&(e.onclick=Jr)}for(c in ve(n,r),n=null,i)if(!r.hasOwnProperty(c)&&i.hasOwnProperty(c)&&null!=i[c])if("style"===c){var l=i[c];for(a in l)l.hasOwnProperty(a)&&(n||(n={}),n[a]="")}else"dangerouslySetInnerHTML"!==c&&"children"!==c&&"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&"autoFocus"!==c&&(s.hasOwnProperty(c)?o||(o=[]):(o=o||[]).push(c,null));for(c in r){var u=r[c];if(l=null!=i?i[c]:void 0,r.hasOwnProperty(c)&&u!==l&&(null!=u||null!=l))if("style"===c)if(l){for(a in l)!l.hasOwnProperty(a)||u&&u.hasOwnProperty(a)||(n||(n={}),n[a]="");for(a in u)u.hasOwnProperty(a)&&l[a]!==u[a]&&(n||(n={}),n[a]=u[a])}else n||(o||(o=[]),o.push(c,n)),n=u;else"dangerouslySetInnerHTML"===c?(u=u?u.__html:void 0,l=l?l.__html:void 0,null!=u&&l!==u&&(o=o||[]).push(c,u)):"children"===c?"string"!==typeof u&&"number"!==typeof u||(o=o||[]).push(c,""+u):"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&(s.hasOwnProperty(c)?(null!=u&&"onScroll"===c&&Vr("scroll",e),o||l===u||(o=[])):(o=o||[]).push(c,u))}n&&(o=o||[]).push("style",n);var c=o;(t.updateQueue=c)&&(t.flags|=4)}},Ds=function(e,t,n,r){n!==r&&(t.flags|=4)};var Ys=!1,Qs=!1,Gs="function"===typeof WeakSet?WeakSet:Set,Zs=null;function Js(e,t){var n=e.ref;if(null!==n)if("function"===typeof n)try{n(null)}catch(r){Eu(e,t,r)}else n.current=null}function el(e,t,n){try{n()}catch(r){Eu(e,t,r)}}var tl=!1;function nl(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var i=r=r.next;do{if((i.tag&e)===e){var a=i.destroy;i.destroy=void 0,void 0!==a&&el(t,n,a)}i=i.next}while(i!==r)}}function rl(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function il(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"===typeof t?t(e):t.current=e}}function al(e){var t=e.alternate;null!==t&&(e.alternate=null,al(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[fi],delete t[hi],delete t[mi],delete t[gi],delete t[yi])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function ol(e){return 5===e.tag||3===e.tag||4===e.tag}function sl(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||ol(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function ll(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Jr));else if(4!==r&&null!==(e=e.child))for(ll(e,t,n),e=e.sibling;null!==e;)ll(e,t,n),e=e.sibling}function ul(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(ul(e,t,n),e=e.sibling;null!==e;)ul(e,t,n),e=e.sibling}var cl=null,dl=!1;function fl(e,t,n){for(n=n.child;null!==n;)hl(e,t,n),n=n.sibling}function hl(e,t,n){if(at&&"function"===typeof at.onCommitFiberUnmount)try{at.onCommitFiberUnmount(it,n)}catch(s){}switch(n.tag){case 5:Qs||Js(n,t);case 6:var r=cl,i=dl;cl=null,fl(e,t,n),dl=i,null!==(cl=r)&&(dl?(e=cl,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):cl.removeChild(n.stateNode));break;case 18:null!==cl&&(dl?(e=cl,n=n.stateNode,8===e.nodeType?li(e.parentNode,n):1===e.nodeType&&li(e,n),Bt(e)):li(cl,n.stateNode));break;case 4:r=cl,i=dl,cl=n.stateNode.containerInfo,dl=!0,fl(e,t,n),cl=r,dl=i;break;case 0:case 11:case 14:case 15:if(!Qs&&(null!==(r=n.updateQueue)&&null!==(r=r.lastEffect))){i=r=r.next;do{var a=i,o=a.destroy;a=a.tag,void 0!==o&&(0!==(2&a)||0!==(4&a))&&el(n,t,o),i=i.next}while(i!==r)}fl(e,t,n);break;case 1:if(!Qs&&(Js(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(s){Eu(n,t,s)}fl(e,t,n);break;case 21:fl(e,t,n);break;case 22:1&n.mode?(Qs=(r=Qs)||null!==n.memoizedState,fl(e,t,n),Qs=r):fl(e,t,n);break;default:fl(e,t,n)}}function pl(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Gs),t.forEach(function(t){var r=Pu.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))})}}function ml(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var i=n[r];try{var o=e,s=t,l=s;e:for(;null!==l;){switch(l.tag){case 5:cl=l.stateNode,dl=!1;break e;case 3:case 4:cl=l.stateNode.containerInfo,dl=!0;break e}l=l.return}if(null===cl)throw Error(a(160));hl(o,s,i),cl=null,dl=!1;var u=i.alternate;null!==u&&(u.return=null),i.return=null}catch(c){Eu(i,t,c)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)gl(t,e),t=t.sibling}function gl(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(ml(t,e),yl(e),4&r){try{nl(3,e,e.return),rl(3,e)}catch(g){Eu(e,e.return,g)}try{nl(5,e,e.return)}catch(g){Eu(e,e.return,g)}}break;case 1:ml(t,e),yl(e),512&r&&null!==n&&Js(n,n.return);break;case 5:if(ml(t,e),yl(e),512&r&&null!==n&&Js(n,n.return),32&e.flags){var i=e.stateNode;try{fe(i,"")}catch(g){Eu(e,e.return,g)}}if(4&r&&null!=(i=e.stateNode)){var o=e.memoizedProps,s=null!==n?n.memoizedProps:o,l=e.type,u=e.updateQueue;if(e.updateQueue=null,null!==u)try{"input"===l&&"radio"===o.type&&null!=o.name&&G(i,o),be(l,s);var c=be(l,o);for(s=0;s<u.length;s+=2){var d=u[s],f=u[s+1];"style"===d?ge(i,f):"dangerouslySetInnerHTML"===d?de(i,f):"children"===d?fe(i,f):b(i,d,f,c)}switch(l){case"input":Z(i,o);break;case"textarea":ae(i,o);break;case"select":var h=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!o.multiple;var p=o.value;null!=p?ne(i,!!o.multiple,p,!1):h!==!!o.multiple&&(null!=o.defaultValue?ne(i,!!o.multiple,o.defaultValue,!0):ne(i,!!o.multiple,o.multiple?[]:"",!1))}i[hi]=o}catch(g){Eu(e,e.return,g)}}break;case 6:if(ml(t,e),yl(e),4&r){if(null===e.stateNode)throw Error(a(162));i=e.stateNode,o=e.memoizedProps;try{i.nodeValue=o}catch(g){Eu(e,e.return,g)}}break;case 3:if(ml(t,e),yl(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Bt(t.containerInfo)}catch(g){Eu(e,e.return,g)}break;case 4:default:ml(t,e),yl(e);break;case 13:ml(t,e),yl(e),8192&(i=e.child).flags&&(o=null!==i.memoizedState,i.stateNode.isHidden=o,!o||null!==i.alternate&&null!==i.alternate.memoizedState||(Ul=Ge())),4&r&&pl(e);break;case 22:if(d=null!==n&&null!==n.memoizedState,1&e.mode?(Qs=(c=Qs)||d,ml(t,e),Qs=c):ml(t,e),yl(e),8192&r){if(c=null!==e.memoizedState,(e.stateNode.isHidden=c)&&!d&&0!==(1&e.mode))for(Zs=e,d=e.child;null!==d;){for(f=Zs=d;null!==Zs;){switch(p=(h=Zs).child,h.tag){case 0:case 11:case 14:case 15:nl(4,h,h.return);break;case 1:Js(h,h.return);var m=h.stateNode;if("function"===typeof m.componentWillUnmount){r=h,n=h.return;try{t=r,m.props=t.memoizedProps,m.state=t.memoizedState,m.componentWillUnmount()}catch(g){Eu(r,n,g)}}break;case 5:Js(h,h.return);break;case 22:if(null!==h.memoizedState){wl(f);continue}}null!==p?(p.return=h,Zs=p):wl(f)}d=d.sibling}e:for(d=null,f=e;;){if(5===f.tag){if(null===d){d=f;try{i=f.stateNode,c?"function"===typeof(o=i.style).setProperty?o.setProperty("display","none","important"):o.display="none":(l=f.stateNode,s=void 0!==(u=f.memoizedProps.style)&&null!==u&&u.hasOwnProperty("display")?u.display:null,l.style.display=me("display",s))}catch(g){Eu(e,e.return,g)}}}else if(6===f.tag){if(null===d)try{f.stateNode.nodeValue=c?"":f.memoizedProps}catch(g){Eu(e,e.return,g)}}else if((22!==f.tag&&23!==f.tag||null===f.memoizedState||f===e)&&null!==f.child){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;null===f.sibling;){if(null===f.return||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:ml(t,e),yl(e),4&r&&pl(e);case 21:}}function yl(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(ol(n)){var r=n;break e}n=n.return}throw Error(a(160))}switch(r.tag){case 5:var i=r.stateNode;32&r.flags&&(fe(i,""),r.flags&=-33),ul(e,sl(e),i);break;case 3:case 4:var o=r.stateNode.containerInfo;ll(e,sl(e),o);break;default:throw Error(a(161))}}catch(s){Eu(e,e.return,s)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function vl(e,t,n){Zs=e,bl(e,t,n)}function bl(e,t,n){for(var r=0!==(1&e.mode);null!==Zs;){var i=Zs,a=i.child;if(22===i.tag&&r){var o=null!==i.memoizedState||Ys;if(!o){var s=i.alternate,l=null!==s&&null!==s.memoizedState||Qs;s=Ys;var u=Qs;if(Ys=o,(Qs=l)&&!u)for(Zs=i;null!==Zs;)l=(o=Zs).child,22===o.tag&&null!==o.memoizedState?Sl(i):null!==l?(l.return=o,Zs=l):Sl(i);for(;null!==a;)Zs=a,bl(a,t,n),a=a.sibling;Zs=i,Ys=s,Qs=u}xl(e)}else 0!==(8772&i.subtreeFlags)&&null!==a?(a.return=i,Zs=a):xl(e)}}function xl(e){for(;null!==Zs;){var t=Zs;if(0!==(8772&t.flags)){var n=t.alternate;try{if(0!==(8772&t.flags))switch(t.tag){case 0:case 11:case 15:Qs||rl(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!Qs)if(null===n)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:ns(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;null!==o&&Wa(t,o,r);break;case 3:var s=t.updateQueue;if(null!==s){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}Wa(t,s,n)}break;case 5:var l=t.stateNode;if(null===n&&4&t.flags){n=l;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var c=t.alternate;if(null!==c){var d=c.memoizedState;if(null!==d){var f=d.dehydrated;null!==f&&Bt(f)}}}break;default:throw Error(a(163))}Qs||512&t.flags&&il(t)}catch(h){Eu(t,t.return,h)}}if(t===e){Zs=null;break}if(null!==(n=t.sibling)){n.return=t.return,Zs=n;break}Zs=t.return}}function wl(e){for(;null!==Zs;){var t=Zs;if(t===e){Zs=null;break}var n=t.sibling;if(null!==n){n.return=t.return,Zs=n;break}Zs=t.return}}function Sl(e){for(;null!==Zs;){var t=Zs;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{rl(4,t)}catch(l){Eu(t,n,l)}break;case 1:var r=t.stateNode;if("function"===typeof r.componentDidMount){var i=t.return;try{r.componentDidMount()}catch(l){Eu(t,i,l)}}var a=t.return;try{il(t)}catch(l){Eu(t,a,l)}break;case 5:var o=t.return;try{il(t)}catch(l){Eu(t,o,l)}}}catch(l){Eu(t,t.return,l)}if(t===e){Zs=null;break}var s=t.sibling;if(null!==s){s.return=t.return,Zs=s;break}Zs=t.return}}var kl,El=Math.ceil,jl=x.ReactCurrentDispatcher,Tl=x.ReactCurrentOwner,Cl=x.ReactCurrentBatchConfig,Pl=0,Nl=null,Fl=null,_l=0,Al=0,Rl=Ei(0),Dl=0,Ml=null,Ol=0,Ll=0,zl=0,Vl=null,Il=null,Ul=0,Bl=1/0,Wl=null,Hl=!1,ql=null,Kl=null,$l=!1,Xl=null,Yl=0,Ql=0,Gl=null,Zl=-1,Jl=0;function eu(){return 0!==(6&Pl)?Ge():-1!==Zl?Zl:Zl=Ge()}function tu(e){return 0===(1&e.mode)?1:0!==(2&Pl)&&0!==_l?_l&-_l:null!==ma.transition?(0===Jl&&(Jl=mt()),Jl):0!==(e=bt)?e:e=void 0===(e=window.event)?16:Qt(e.type)}function nu(e,t,n,r){if(50<Ql)throw Ql=0,Gl=null,Error(a(185));yt(e,n,r),0!==(2&Pl)&&e===Nl||(e===Nl&&(0===(2&Pl)&&(Ll|=n),4===Dl&&su(e,_l)),ru(e,r),1===n&&0===Pl&&0===(1&t.mode)&&(Bl=Ge()+500,Vi&&Bi()))}function ru(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,a=e.pendingLanes;0<a;){var o=31-ot(a),s=1<<o,l=i[o];-1===l?0!==(s&n)&&0===(s&r)||(i[o]=ht(s,t)):l<=t&&(e.expiredLanes|=s),a&=~s}}(e,t);var r=ft(e,e===Nl?_l:0);if(0===r)null!==n&&Xe(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&Xe(n),1===t)0===e.tag?function(e){Vi=!0,Ui(e)}(lu.bind(null,e)):Ui(lu.bind(null,e)),oi(function(){0===(6&Pl)&&Bi()}),n=null;else{switch(xt(r)){case 1:n=Je;break;case 4:n=et;break;case 16:default:n=tt;break;case 536870912:n=rt}n=Nu(n,iu.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function iu(e,t){if(Zl=-1,Jl=0,0!==(6&Pl))throw Error(a(327));var n=e.callbackNode;if(Su()&&e.callbackNode!==n)return null;var r=ft(e,e===Nl?_l:0);if(0===r)return null;if(0!==(30&r)||0!==(r&e.expiredLanes)||t)t=gu(e,r);else{t=r;var i=Pl;Pl|=2;var o=pu();for(Nl===e&&_l===t||(Wl=null,Bl=Ge()+500,fu(e,t));;)try{vu();break}catch(l){hu(e,l)}Ta(),jl.current=o,Pl=i,null!==Fl?t=0:(Nl=null,_l=0,t=Dl)}if(0!==t){if(2===t&&(0!==(i=pt(e))&&(r=i,t=au(e,i))),1===t)throw n=Ml,fu(e,0),su(e,r),ru(e,Ge()),n;if(6===t)su(e,r);else{if(i=e.current.alternate,0===(30&r)&&!function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var i=n[r],a=i.getSnapshot;i=i.value;try{if(!sr(a(),i))return!1}catch(s){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(i)&&(2===(t=gu(e,r))&&(0!==(o=pt(e))&&(r=o,t=au(e,o))),1===t))throw n=Ml,fu(e,0),su(e,r),ru(e,Ge()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(a(345));case 2:case 5:wu(e,Il,Wl);break;case 3:if(su(e,r),(130023424&r)===r&&10<(t=Ul+500-Ge())){if(0!==ft(e,0))break;if(((i=e.suspendedLanes)&r)!==r){eu(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=ri(wu.bind(null,e,Il,Wl),t);break}wu(e,Il,Wl);break;case 4:if(su(e,r),(4194240&r)===r)break;for(t=e.eventTimes,i=-1;0<r;){var s=31-ot(r);o=1<<s,(s=t[s])>i&&(i=s),r&=~o}if(r=i,10<(r=(120>(r=Ge()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*El(r/1960))-r)){e.timeoutHandle=ri(wu.bind(null,e,Il,Wl),r);break}wu(e,Il,Wl);break;default:throw Error(a(329))}}}return ru(e,Ge()),e.callbackNode===n?iu.bind(null,e):null}function au(e,t){var n=Vl;return e.current.memoizedState.isDehydrated&&(fu(e,t).flags|=256),2!==(e=gu(e,t))&&(t=Il,Il=n,null!==t&&ou(t)),e}function ou(e){null===Il?Il=e:Il.push.apply(Il,e)}function su(e,t){for(t&=~zl,t&=~Ll,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-ot(t),r=1<<n;e[n]=-1,t&=~r}}function lu(e){if(0!==(6&Pl))throw Error(a(327));Su();var t=ft(e,0);if(0===(1&t))return ru(e,Ge()),null;var n=gu(e,t);if(0!==e.tag&&2===n){var r=pt(e);0!==r&&(t=r,n=au(e,r))}if(1===n)throw n=Ml,fu(e,0),su(e,t),ru(e,Ge()),n;if(6===n)throw Error(a(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,wu(e,Il,Wl),ru(e,Ge()),null}function uu(e,t){var n=Pl;Pl|=1;try{return e(t)}finally{0===(Pl=n)&&(Bl=Ge()+500,Vi&&Bi())}}function cu(e){null!==Xl&&0===Xl.tag&&0===(6&Pl)&&Su();var t=Pl;Pl|=1;var n=Cl.transition,r=bt;try{if(Cl.transition=null,bt=1,e)return e()}finally{bt=r,Cl.transition=n,0===(6&(Pl=t))&&Bi()}}function du(){Al=Rl.current,ji(Rl)}function fu(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,ii(n)),null!==Fl)for(n=Fl.return;null!==n;){var r=n;switch(ta(r),r.tag){case 1:null!==(r=r.type.childContextTypes)&&void 0!==r&&Ri();break;case 3:Qa(),ji(Ni),ji(Pi),no();break;case 5:Za(r);break;case 4:Qa();break;case 13:case 19:ji(Ja);break;case 10:Ca(r.type._context);break;case 22:case 23:du()}n=n.return}if(Nl=e,Fl=e=Ru(e.current,null),_l=Al=t,Dl=0,Ml=null,zl=Ll=Ol=0,Il=Vl=null,null!==_a){for(t=0;t<_a.length;t++)if(null!==(r=(n=_a[t]).interleaved)){n.interleaved=null;var i=r.next,a=n.pending;if(null!==a){var o=a.next;a.next=i,r.next=o}n.pending=r}_a=null}return e}function hu(e,t){for(;;){var n=Fl;try{if(Ta(),ro.current=Zo,uo){for(var r=oo.memoizedState;null!==r;){var i=r.queue;null!==i&&(i.pending=null),r=r.next}uo=!1}if(ao=0,lo=so=oo=null,co=!1,fo=0,Tl.current=null,null===n||null===n.return){Dl=1,Ml=t,Fl=null;break}e:{var o=e,s=n.return,l=n,u=t;if(t=_l,l.flags|=32768,null!==u&&"object"===typeof u&&"function"===typeof u.then){var c=u,d=l,f=d.tag;if(0===(1&d.mode)&&(0===f||11===f||15===f)){var h=d.alternate;h?(d.updateQueue=h.updateQueue,d.memoizedState=h.memoizedState,d.lanes=h.lanes):(d.updateQueue=null,d.memoizedState=null)}var p=gs(s);if(null!==p){p.flags&=-257,ys(p,s,l,0,t),1&p.mode&&ms(o,c,t),u=c;var m=(t=p).updateQueue;if(null===m){var g=new Set;g.add(u),t.updateQueue=g}else m.add(u);break e}if(0===(1&t)){ms(o,c,t),mu();break e}u=Error(a(426))}else if(ia&&1&l.mode){var y=gs(s);if(null!==y){0===(65536&y.flags)&&(y.flags|=256),ys(y,s,l,0,t),pa(us(u,l));break e}}o=u=us(u,l),4!==Dl&&(Dl=2),null===Vl?Vl=[o]:Vl.push(o),o=s;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t,Ua(o,hs(0,u,t));break e;case 1:l=u;var v=o.type,b=o.stateNode;if(0===(128&o.flags)&&("function"===typeof v.getDerivedStateFromError||null!==b&&"function"===typeof b.componentDidCatch&&(null===Kl||!Kl.has(b)))){o.flags|=65536,t&=-t,o.lanes|=t,Ua(o,ps(o,l,t));break e}}o=o.return}while(null!==o)}xu(n)}catch(x){t=x,Fl===n&&null!==n&&(Fl=n=n.return);continue}break}}function pu(){var e=jl.current;return jl.current=Zo,null===e?Zo:e}function mu(){0!==Dl&&3!==Dl&&2!==Dl||(Dl=4),null===Nl||0===(268435455&Ol)&&0===(268435455&Ll)||su(Nl,_l)}function gu(e,t){var n=Pl;Pl|=2;var r=pu();for(Nl===e&&_l===t||(Wl=null,fu(e,t));;)try{yu();break}catch(i){hu(e,i)}if(Ta(),Pl=n,jl.current=r,null!==Fl)throw Error(a(261));return Nl=null,_l=0,Dl}function yu(){for(;null!==Fl;)bu(Fl)}function vu(){for(;null!==Fl&&!Ye();)bu(Fl)}function bu(e){var t=kl(e.alternate,e,Al);e.memoizedProps=e.pendingProps,null===t?xu(e):Fl=t,Tl.current=null}function xu(e){var t=e;do{var n=t.alternate;if(e=t.return,0===(32768&t.flags)){if(null!==(n=$s(n,t,Al)))return void(Fl=n)}else{if(null!==(n=Xs(n,t)))return n.flags&=32767,void(Fl=n);if(null===e)return Dl=6,void(Fl=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}if(null!==(t=t.sibling))return void(Fl=t);Fl=t=e}while(null!==t);0===Dl&&(Dl=5)}function wu(e,t,n){var r=bt,i=Cl.transition;try{Cl.transition=null,bt=1,function(e,t,n,r){do{Su()}while(null!==Xl);if(0!==(6&Pl))throw Error(a(327));n=e.finishedWork;var i=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(a(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-ot(n),a=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~a}}(e,o),e===Nl&&(Fl=Nl=null,_l=0),0===(2064&n.subtreeFlags)&&0===(2064&n.flags)||$l||($l=!0,Nu(tt,function(){return Su(),null})),o=0!==(15990&n.flags),0!==(15990&n.subtreeFlags)||o){o=Cl.transition,Cl.transition=null;var s=bt;bt=1;var l=Pl;Pl|=4,Tl.current=null,function(e,t){if(ei=Ht,hr(e=fr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var i=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch(w){n=null;break e}var s=0,l=-1,u=-1,c=0,d=0,f=e,h=null;t:for(;;){for(var p;f!==n||0!==i&&3!==f.nodeType||(l=s+i),f!==o||0!==r&&3!==f.nodeType||(u=s+r),3===f.nodeType&&(s+=f.nodeValue.length),null!==(p=f.firstChild);)h=f,f=p;for(;;){if(f===e)break t;if(h===n&&++c===i&&(l=s),h===o&&++d===r&&(u=s),null!==(p=f.nextSibling))break;h=(f=h).parentNode}f=p}n=-1===l||-1===u?null:{start:l,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(ti={focusedElem:e,selectionRange:n},Ht=!1,Zs=t;null!==Zs;)if(e=(t=Zs).child,0!==(1028&t.subtreeFlags)&&null!==e)e.return=t,Zs=e;else for(;null!==Zs;){t=Zs;try{var m=t.alternate;if(0!==(1024&t.flags))switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==m){var g=m.memoizedProps,y=m.memoizedState,v=t.stateNode,b=v.getSnapshotBeforeUpdate(t.elementType===t.type?g:ns(t.type,g),y);v.__reactInternalSnapshotBeforeUpdate=b}break;case 3:var x=t.stateNode.containerInfo;1===x.nodeType?x.textContent="":9===x.nodeType&&x.documentElement&&x.removeChild(x.documentElement);break;default:throw Error(a(163))}}catch(w){Eu(t,t.return,w)}if(null!==(e=t.sibling)){e.return=t.return,Zs=e;break}Zs=t.return}m=tl,tl=!1}(e,n),gl(n,e),pr(ti),Ht=!!ei,ti=ei=null,e.current=n,vl(n,e,i),Qe(),Pl=l,bt=s,Cl.transition=o}else e.current=n;if($l&&($l=!1,Xl=e,Yl=i),o=e.pendingLanes,0===o&&(Kl=null),function(e){if(at&&"function"===typeof at.onCommitFiberRoot)try{at.onCommitFiberRoot(it,e,void 0,128===(128&e.current.flags))}catch(t){}}(n.stateNode),ru(e,Ge()),null!==t)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(Hl)throw Hl=!1,e=ql,ql=null,e;0!==(1&Yl)&&0!==e.tag&&Su(),o=e.pendingLanes,0!==(1&o)?e===Gl?Ql++:(Ql=0,Gl=e):Ql=0,Bi()}(e,t,n,r)}finally{Cl.transition=i,bt=r}return null}function Su(){if(null!==Xl){var e=xt(Yl),t=Cl.transition,n=bt;try{if(Cl.transition=null,bt=16>e?16:e,null===Xl)var r=!1;else{if(e=Xl,Xl=null,Yl=0,0!==(6&Pl))throw Error(a(331));var i=Pl;for(Pl|=4,Zs=e.current;null!==Zs;){var o=Zs,s=o.child;if(0!==(16&Zs.flags)){var l=o.deletions;if(null!==l){for(var u=0;u<l.length;u++){var c=l[u];for(Zs=c;null!==Zs;){var d=Zs;switch(d.tag){case 0:case 11:case 15:nl(8,d,o)}var f=d.child;if(null!==f)f.return=d,Zs=f;else for(;null!==Zs;){var h=(d=Zs).sibling,p=d.return;if(al(d),d===c){Zs=null;break}if(null!==h){h.return=p,Zs=h;break}Zs=p}}}var m=o.alternate;if(null!==m){var g=m.child;if(null!==g){m.child=null;do{var y=g.sibling;g.sibling=null,g=y}while(null!==g)}}Zs=o}}if(0!==(2064&o.subtreeFlags)&&null!==s)s.return=o,Zs=s;else e:for(;null!==Zs;){if(0!==(2048&(o=Zs).flags))switch(o.tag){case 0:case 11:case 15:nl(9,o,o.return)}var v=o.sibling;if(null!==v){v.return=o.return,Zs=v;break e}Zs=o.return}}var b=e.current;for(Zs=b;null!==Zs;){var x=(s=Zs).child;if(0!==(2064&s.subtreeFlags)&&null!==x)x.return=s,Zs=x;else e:for(s=b;null!==Zs;){if(0!==(2048&(l=Zs).flags))try{switch(l.tag){case 0:case 11:case 15:rl(9,l)}}catch(S){Eu(l,l.return,S)}if(l===s){Zs=null;break e}var w=l.sibling;if(null!==w){w.return=l.return,Zs=w;break e}Zs=l.return}}if(Pl=i,Bi(),at&&"function"===typeof at.onPostCommitFiberRoot)try{at.onPostCommitFiberRoot(it,e)}catch(S){}r=!0}return r}finally{bt=n,Cl.transition=t}}return!1}function ku(e,t,n){e=Va(e,t=hs(0,t=us(n,t),1),1),t=eu(),null!==e&&(yt(e,1,t),ru(e,t))}function Eu(e,t,n){if(3===e.tag)ku(e,e,n);else for(;null!==t;){if(3===t.tag){ku(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===Kl||!Kl.has(r))){t=Va(t,e=ps(t,e=us(n,e),1),1),e=eu(),null!==t&&(yt(t,1,e),ru(t,e));break}}t=t.return}}function ju(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=eu(),e.pingedLanes|=e.suspendedLanes&n,Nl===e&&(_l&n)===n&&(4===Dl||3===Dl&&(130023424&_l)===_l&&500>Ge()-Ul?fu(e,0):zl|=n),ru(e,t)}function Tu(e,t){0===t&&(0===(1&e.mode)?t=1:(t=ct,0===(130023424&(ct<<=1))&&(ct=4194304)));var n=eu();null!==(e=Da(e,t))&&(yt(e,t,n),ru(e,n))}function Cu(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),Tu(e,n)}function Pu(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;null!==i&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(a(314))}null!==r&&r.delete(t),Tu(e,n)}function Nu(e,t){return $e(e,t)}function Fu(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function _u(e,t,n,r){return new Fu(e,t,n,r)}function Au(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Ru(e,t){var n=e.alternate;return null===n?((n=_u(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Du(e,t,n,r,i,o){var s=2;if(r=e,"function"===typeof e)Au(e)&&(s=1);else if("string"===typeof e)s=5;else e:switch(e){case k:return Mu(n.children,i,o,t);case E:s=8,i|=8;break;case j:return(e=_u(12,n,t,2|i)).elementType=j,e.lanes=o,e;case N:return(e=_u(13,n,t,i)).elementType=N,e.lanes=o,e;case F:return(e=_u(19,n,t,i)).elementType=F,e.lanes=o,e;case R:return Ou(n,i,o,t);default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case T:s=10;break e;case C:s=9;break e;case P:s=11;break e;case _:s=14;break e;case A:s=16,r=null;break e}throw Error(a(130,null==e?e:typeof e,""))}return(t=_u(s,n,t,i)).elementType=e,t.type=r,t.lanes=o,t}function Mu(e,t,n,r){return(e=_u(7,e,r,t)).lanes=n,e}function Ou(e,t,n,r){return(e=_u(22,e,r,t)).elementType=R,e.lanes=n,e.stateNode={isHidden:!1},e}function Lu(e,t,n){return(e=_u(6,e,null,t)).lanes=n,e}function zu(e,t,n){return(t=_u(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Vu(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=gt(0),this.expirationTimes=gt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=gt(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function Iu(e,t,n,r,i,a,o,s,l){return e=new Vu(e,t,n,s,l),1===t?(t=1,!0===a&&(t|=8)):t=0,a=_u(3,null,null,t),e.current=a,a.stateNode=e,a.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Oa(a),e}function Uu(e){if(!e)return Ci;e:{if(Be(e=e._reactInternals)!==e||1!==e.tag)throw Error(a(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ai(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(a(171))}if(1===e.tag){var n=e.type;if(Ai(n))return Mi(e,n,t)}return t}function Bu(e,t,n,r,i,a,o,s,l){return(e=Iu(n,r,!0,e,0,a,0,s,l)).context=Uu(null),n=e.current,(a=za(r=eu(),i=tu(n))).callback=void 0!==t&&null!==t?t:null,Va(n,a,i),e.current.lanes=i,yt(e,i,r),ru(e,r),e}function Wu(e,t,n,r){var i=t.current,a=eu(),o=tu(i);return n=Uu(n),null===t.context?t.context=n:t.pendingContext=n,(t=za(a,o)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=Va(i,t,o))&&(nu(e,i,o,a),Ia(e,i,o)),o}function Hu(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function qu(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function Ku(e,t){qu(e,t),(e=e.alternate)&&qu(e,t)}kl=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||Ni.current)bs=!0;else{if(0===(e.lanes&n)&&0===(128&t.flags))return bs=!1,function(e,t,n){switch(t.tag){case 3:Ns(t),ha();break;case 5:Ga(t);break;case 1:Ai(t.type)&&Oi(t);break;case 4:Ya(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;Ti(Sa,r._currentValue),r._currentValue=i;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(Ti(Ja,1&Ja.current),t.flags|=128,null):0!==(n&t.child.childLanes)?Ls(e,t,n):(Ti(Ja,1&Ja.current),null!==(e=Hs(e,t,n))?e.sibling:null);Ti(Ja,1&Ja.current);break;case 19:if(r=0!==(n&t.childLanes),0!==(128&e.flags)){if(r)return Bs(e,t,n);t.flags|=128}if(null!==(i=t.memoizedState)&&(i.rendering=null,i.tail=null,i.lastEffect=null),Ti(Ja,Ja.current),r)break;return null;case 22:case 23:return t.lanes=0,Es(e,t,n)}return Hs(e,t,n)}(e,t,n);bs=0!==(131072&e.flags)}else bs=!1,ia&&0!==(1048576&t.flags)&&Ji(t,Ki,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Ws(e,t),e=t.pendingProps;var i=_i(t,Pi.current);Na(t,n),i=go(null,t,r,e,i,n);var o=yo();return t.flags|=1,"object"===typeof i&&null!==i&&"function"===typeof i.render&&void 0===i.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ai(r)?(o=!0,Oi(t)):o=!1,t.memoizedState=null!==i.state&&void 0!==i.state?i.state:null,Oa(t),i.updater=is,t.stateNode=i,i._reactInternals=t,ls(t,r,e,n),t=Ps(null,t,r,!0,o,n)):(t.tag=0,ia&&o&&ea(t),xs(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Ws(e,t),e=t.pendingProps,r=(i=r._init)(r._payload),t.type=r,i=t.tag=function(e){if("function"===typeof e)return Au(e)?1:0;if(void 0!==e&&null!==e){if((e=e.$$typeof)===P)return 11;if(e===_)return 14}return 2}(r),e=ns(r,e),i){case 0:t=Ts(null,t,r,e,n);break e;case 1:t=Cs(null,t,r,e,n);break e;case 11:t=ws(null,t,r,e,n);break e;case 14:t=Ss(null,t,r,ns(r.type,e),n);break e}throw Error(a(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,Ts(e,t,r,i=t.elementType===r?i:ns(r,i),n);case 1:return r=t.type,i=t.pendingProps,Cs(e,t,r,i=t.elementType===r?i:ns(r,i),n);case 3:e:{if(Ns(t),null===e)throw Error(a(387));r=t.pendingProps,i=(o=t.memoizedState).element,La(e,t),Ba(t,r,null,n);var s=t.memoizedState;if(r=s.element,o.isDehydrated){if(o={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=o,t.memoizedState=o,256&t.flags){t=Fs(e,t,r,n,i=us(Error(a(423)),t));break e}if(r!==i){t=Fs(e,t,r,n,i=us(Error(a(424)),t));break e}for(ra=ui(t.stateNode.containerInfo.firstChild),na=t,ia=!0,aa=null,n=wa(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(ha(),r===i){t=Hs(e,t,n);break e}xs(e,t,r,n)}t=t.child}return t;case 5:return Ga(t),null===e&&ua(t),r=t.type,i=t.pendingProps,o=null!==e?e.memoizedProps:null,s=i.children,ni(r,i)?s=null:null!==o&&ni(r,o)&&(t.flags|=32),js(e,t),xs(e,t,s,n),t.child;case 6:return null===e&&ua(t),null;case 13:return Ls(e,t,n);case 4:return Ya(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=xa(t,null,r,n):xs(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,ws(e,t,r,i=t.elementType===r?i:ns(r,i),n);case 7:return xs(e,t,t.pendingProps,n),t.child;case 8:case 12:return xs(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,o=t.memoizedProps,s=i.value,Ti(Sa,r._currentValue),r._currentValue=s,null!==o)if(sr(o.value,s)){if(o.children===i.children&&!Ni.current){t=Hs(e,t,n);break e}}else for(null!==(o=t.child)&&(o.return=t);null!==o;){var l=o.dependencies;if(null!==l){s=o.child;for(var u=l.firstContext;null!==u;){if(u.context===r){if(1===o.tag){(u=za(-1,n&-n)).tag=2;var c=o.updateQueue;if(null!==c){var d=(c=c.shared).pending;null===d?u.next=u:(u.next=d.next,d.next=u),c.pending=u}}o.lanes|=n,null!==(u=o.alternate)&&(u.lanes|=n),Pa(o.return,n,t),l.lanes|=n;break}u=u.next}}else if(10===o.tag)s=o.type===t.type?null:o.child;else if(18===o.tag){if(null===(s=o.return))throw Error(a(341));s.lanes|=n,null!==(l=s.alternate)&&(l.lanes|=n),Pa(s,n,t),s=o.sibling}else s=o.child;if(null!==s)s.return=o;else for(s=o;null!==s;){if(s===t){s=null;break}if(null!==(o=s.sibling)){o.return=s.return,s=o;break}s=s.return}o=s}xs(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,Na(t,n),r=r(i=Fa(i)),t.flags|=1,xs(e,t,r,n),t.child;case 14:return i=ns(r=t.type,t.pendingProps),Ss(e,t,r,i=ns(r.type,i),n);case 15:return ks(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:ns(r,i),Ws(e,t),t.tag=1,Ai(r)?(e=!0,Oi(t)):e=!1,Na(t,n),os(t,r,i),ls(t,r,i,n),Ps(null,t,r,!0,e,n);case 19:return Bs(e,t,n);case 22:return Es(e,t,n)}throw Error(a(156,t.tag))};var $u="function"===typeof reportError?reportError:function(e){console.error(e)};function Xu(e){this._internalRoot=e}function Yu(e){this._internalRoot=e}function Qu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Gu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Zu(){}function Ju(e,t,n,r,i){var a=n._reactRootContainer;if(a){var o=a;if("function"===typeof i){var s=i;i=function(){var e=Hu(o);s.call(e)}}Wu(t,o,e,i)}else o=function(e,t,n,r,i){if(i){if("function"===typeof r){var a=r;r=function(){var e=Hu(o);a.call(e)}}var o=Bu(t,r,e,0,null,!1,0,"",Zu);return e._reactRootContainer=o,e[pi]=o.current,Br(8===e.nodeType?e.parentNode:e),cu(),o}for(;i=e.lastChild;)e.removeChild(i);if("function"===typeof r){var s=r;r=function(){var e=Hu(l);s.call(e)}}var l=Iu(e,0,!1,null,0,!1,0,"",Zu);return e._reactRootContainer=l,e[pi]=l.current,Br(8===e.nodeType?e.parentNode:e),cu(function(){Wu(t,l,n,r)}),l}(n,t,e,i,r);return Hu(o)}Yu.prototype.render=Xu.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(a(409));Wu(e,t,null,null)},Yu.prototype.unmount=Xu.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;cu(function(){Wu(null,e,null,null)}),t[pi]=null}},Yu.prototype.unstable_scheduleHydration=function(e){if(e){var t=Et();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Rt.length&&0!==t&&t<Rt[n].priority;n++);Rt.splice(n,0,e),0===n&&Lt(e)}},wt=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=dt(t.pendingLanes);0!==n&&(vt(t,1|n),ru(t,Ge()),0===(6&Pl)&&(Bl=Ge()+500,Bi()))}break;case 13:cu(function(){var t=Da(e,1);if(null!==t){var n=eu();nu(t,e,1,n)}}),Ku(e,1)}},St=function(e){if(13===e.tag){var t=Da(e,134217728);if(null!==t)nu(t,e,134217728,eu());Ku(e,134217728)}},kt=function(e){if(13===e.tag){var t=tu(e),n=Da(e,t);if(null!==n)nu(n,e,t,eu());Ku(e,t)}},Et=function(){return bt},jt=function(e,t){var n=bt;try{return bt=e,t()}finally{bt=n}},Se=function(e,t,n){switch(t){case"input":if(Z(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=wi(r);if(!i)throw Error(a(90));$(r),Z(r,i)}}}break;case"textarea":ae(e,n);break;case"select":null!=(t=n.value)&&ne(e,!!n.multiple,t,!1)}},Pe=uu,Ne=cu;var ec={usingClientEntryPoint:!1,Events:[bi,xi,wi,Te,Ce,uu]},tc={findFiberByHostInstance:vi,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},nc={bundleType:tc.bundleType,version:tc.version,rendererPackageName:tc.rendererPackageName,rendererConfig:tc.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:x.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=qe(e))?null:e.stateNode},findFiberByHostInstance:tc.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var rc=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!rc.isDisabled&&rc.supportsFiber)try{it=rc.inject(nc),at=rc}catch(ce){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ec,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Qu(t))throw Error(a(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:S,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.createRoot=function(e,t){if(!Qu(e))throw Error(a(299));var n=!1,r="",i=$u;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onRecoverableError&&(i=t.onRecoverableError)),t=Iu(e,1,!1,null,0,n,0,r,i),e[pi]=t.current,Br(8===e.nodeType?e.parentNode:e),new Xu(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(a(188));throw e=Object.keys(e).join(","),Error(a(268,e))}return e=null===(e=qe(t))?null:e.stateNode},t.flushSync=function(e){return cu(e)},t.hydrate=function(e,t,n){if(!Gu(t))throw Error(a(200));return Ju(null,e,t,!0,n)},t.hydrateRoot=function(e,t,n){if(!Qu(e))throw Error(a(405));var r=null!=n&&n.hydratedSources||null,i=!1,o="",s=$u;if(null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(i=!0),void 0!==n.identifierPrefix&&(o=n.identifierPrefix),void 0!==n.onRecoverableError&&(s=n.onRecoverableError)),t=Bu(t,null,e,1,null!=n?n:null,i,0,o,s),e[pi]=t.current,Br(e),r)for(e=0;e<r.length;e++)i=(i=(n=r[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new Yu(t)},t.render=function(e,t,n){if(!Gu(t))throw Error(a(200));return Ju(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!Gu(e))throw Error(a(40));return!!e._reactRootContainer&&(cu(function(){Ju(null,null,e,!1,function(){e._reactRootContainer=null,e[pi]=null})}),!0)},t.unstable_batchedUpdates=uu,t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Gu(n))throw Error(a(200));if(null==e||void 0===e._reactInternals)throw Error(a(38));return Ju(e,t,n,!1,r)},t.version="18.3.1-next-f1338f8080-20240426"},853:(e,t,n)=>{e.exports=n(234)},950:(e,t,n)=>{!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(730)}},t={};function n(r){var i=t[r];if(void 0!==i)return i.exports;var a=t[r]={exports:{}};return e[r](a,a.exports,n),a.exports}n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};n.r(r),n.d(r,{hasBrowserEnv:()=>au,hasStandardBrowserEnv:()=>su,hasStandardBrowserWebWorkerEnv:()=>lu,navigator:()=>ou,origin:()=>uu});var i=n(43),a=n(391);function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function s(e){var t=function(e,t){if("object"!=o(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=o(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==o(t)?t:t+""}function l(e,t,n){return(t=s(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach(function(t){l(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function d(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}const f=e=>{const t=(e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase()))(e);return t.charAt(0).toUpperCase()+t.slice(1)},h=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>Boolean(e)&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()};var p={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const m=["color","size","strokeWidth","absoluteStrokeWidth","className","children","iconNode"],g=(0,i.forwardRef)((e,t)=>{let{color:n="currentColor",size:r=24,strokeWidth:a=2,absoluteStrokeWidth:o,className:s="",children:l,iconNode:u}=e,f=d(e,m);return(0,i.createElement)("svg",c(c(c({ref:t},p),{},{width:r,height:r,stroke:n,strokeWidth:o?24*Number(a)/Number(r):a,className:h("lucide",s)},!l&&!(e=>{for(const t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(f)&&{"aria-hidden":"true"}),f),[...u.map(e=>{let[t,n]=e;return(0,i.createElement)(t,n)}),...Array.isArray(l)?l:[l]])}),y=["className"],v=(e,t)=>{const n=(0,i.forwardRef)((n,r)=>{let{className:a}=n,o=d(n,y);return(0,i.createElement)(g,c({ref:r,iconNode:t,className:h("lucide-".concat((s=f(e),s.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase())),"lucide-".concat(e),a)},o));var s});return n.displayName=f(e),n},b=v("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),x=v("ellipsis-vertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]]),w=v("pen",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}]]),S=v("trash-2",[["path",{d:"M10 11v6",key:"nco0om"}],["path",{d:"M14 11v6",key:"outv1u"}],["path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6",key:"miytrc"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",key:"e791ji"}]]),k=v("settings",[["path",{d:"M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915",key:"1i5ecw"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),E=v("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]]);var j=n(579);const T=e=>{var t;let{conversations:n,currentConversation:r,onSelectConversation:a,onNewConversation:o,onLogout:s,onOpenPreferences:l,user:u}=e;const[c,d]=(0,i.useState)(null),[f,h]=(0,i.useState)(""),[p,m]=(0,i.useState)(null),g=e=>{console.log("Renaming chat",e,"to",f),d(null),h("")};return(0,j.jsxs)("div",{className:"w-80 h-screen bg-gradient-to-b from-slate-900 via-slate-800 to-slate-900 text-white flex flex-col shadow-2xl",children:[(0,j.jsxs)("div",{className:"p-6 border-b border-white/10 bg-white/5 backdrop-blur-sm",children:[(0,j.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,j.jsx)("div",{className:"text-3xl animate-pulse-slow",children:"\ud83e\udd16"}),(0,j.jsx)("h2",{className:"text-xl font-bold bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent",children:"Sozhaa Tech AI"})]}),(0,j.jsxs)("button",{onClick:o,className:"w-full flex items-center justify-center gap-2 px-4 py-3 bg-gradient-to-r from-primary-500 to-secondary-500 hover:from-primary-600 hover:to-secondary-600 text-white rounded-xl font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg",children:[(0,j.jsx)(b,{size:20}),"New Chat"]})]}),(0,j.jsx)("div",{className:"flex-1 overflow-y-auto custom-scrollbar px-4 py-2",children:n.map(e=>(0,j.jsxs)("div",{className:"group relative p-3 mb-2 rounded-xl cursor-pointer transition-all duration-300 hover:bg-white/10 ".concat(r===e.id?"bg-gradient-to-r from-primary-500/30 to-secondary-500/30 border border-primary-400/50":"bg-white/5 hover:bg-white/10"),children:[(0,j.jsxs)("div",{className:"flex-1",onClick:()=>a(e.id),children:[c===e.id?(0,j.jsx)("input",{type:"text",value:f,onChange:e=>h(e.target.value),onKeyDown:t=>((e,t)=>{"Enter"===e.key?g(t):"Escape"===e.key&&(d(null),h(""))})(t,e.id),onBlur:()=>g(e.id),className:"w-full bg-transparent border-b border-white/30 text-white text-sm font-medium focus:outline-none focus:border-primary-400",autoFocus:!0}):(0,j.jsx)("div",{className:"text-sm font-medium text-white truncate",children:e.title}),(0,j.jsx)("div",{className:"text-xs text-white/60 mt-1",children:new Date(e.updated_at).toLocaleDateString()})]}),(0,j.jsxs)("div",{className:"absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity",children:[(0,j.jsx)("button",{onClick:t=>{t.stopPropagation(),m(p===e.id?null:e.id)},className:"p-1 hover:bg-white/20 rounded-md transition-colors",children:(0,j.jsx)(x,{size:16,className:"text-white/70"})}),p===e.id&&(0,j.jsxs)("div",{className:"absolute right-0 top-8 bg-slate-800 border border-white/20 rounded-lg shadow-xl z-10 min-w-[120px]",children:[(0,j.jsxs)("button",{onClick:()=>{return t=e.id,n=e.title,d(t),h(n),void m(null);var t,n},className:"w-full flex items-center gap-2 px-3 py-2 text-sm text-white hover:bg-white/10 transition-colors",children:[(0,j.jsx)(w,{size:14}),"Rename"]}),(0,j.jsxs)("button",{onClick:()=>{return t=e.id,console.log("Deleting chat",t),void m(null);var t},className:"w-full flex items-center gap-2 px-3 py-2 text-sm text-red-400 hover:bg-red-500/20 transition-colors",children:[(0,j.jsx)(S,{size:14}),"Delete"]})]})]})]},e.id))}),(0,j.jsxs)("div",{className:"p-4 border-t border-white/10 bg-white/5 backdrop-blur-sm",children:[(0,j.jsx)("div",{className:"flex items-center justify-between mb-3",children:(0,j.jsxs)("div",{className:"flex items-center gap-3",children:[(0,j.jsx)("div",{className:"w-10 h-10 rounded-full bg-gradient-to-r from-primary-500 to-secondary-500 flex items-center justify-center text-lg font-bold",children:(null===u||void 0===u||null===(t=u.username)||void 0===t?void 0:t.charAt(0).toUpperCase())||"\ud83d\udc64"}),(0,j.jsx)("div",{className:"text-sm font-medium text-white",children:null===u||void 0===u?void 0:u.username})]})}),(0,j.jsxs)("div",{className:"space-y-2",children:[(0,j.jsxs)("button",{onClick:l,className:"w-full flex items-center justify-center gap-2 px-4 py-2 bg-primary-500/20 hover:bg-primary-500/30 text-primary-400 hover:text-primary-300 rounded-lg transition-all duration-300 text-sm font-medium",children:[(0,j.jsx)(k,{size:16}),"Preferences"]}),(0,j.jsxs)("button",{onClick:s,className:"w-full flex items-center justify-center gap-2 px-4 py-2 bg-red-500/20 hover:bg-red-500/30 text-red-400 hover:text-red-300 rounded-lg transition-all duration-300 text-sm font-medium",children:[(0,j.jsx)(E,{size:16}),"Logout"]})]})]})]})},C=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],P=(()=>new Set(C))(),N=e=>180*e/Math.PI,F=e=>{const t=N(Math.atan2(e[1],e[0]));return A(t)},_={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:F,rotateZ:F,skewX:e=>N(Math.atan(e[1])),skewY:e=>N(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},A=e=>((e%=360)<0&&(e+=360),e),R=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),D=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),M={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:R,scaleY:D,scale:e=>(R(e)+D(e))/2,rotateX:e=>A(N(Math.atan2(e[6],e[5]))),rotateY:e=>A(N(Math.atan2(-e[2],e[0]))),rotateZ:F,rotate:F,skewX:e=>N(Math.atan(e[4])),skewY:e=>N(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function O(e){return e.includes("scale")?1:0}function L(e,t){if(!e||"none"===e)return O(t);const n=e.match(/^matrix3d\(([\t-\r ,-\.0-9e\xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]+)\)$/);let r,i;if(n)r=M,i=n;else{const t=e.match(/^matrix\(([\t-\r ,-\.0-9e\xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]+)\)$/);r=_,i=t}if(!i)return O(t);const a=r[t],o=i[1].split(",").map(z);return"function"===typeof a?a(o):o[a]}function z(e){return parseFloat(e.trim())}const V=e=>t=>"string"===typeof t&&t.startsWith(e),I=V("--"),U=V("var(--"),B=e=>!!U(e)&&W.test(e.split("/*")[0].trim()),W=/var\(--(?:[\x2D0-9A-Z_a-z\u017F\u212A]+[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*|[\x2D0-9A-Z_a-z\u017F\u212A]+[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*,(?:[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*(?:(?![\t-\r \(\)\xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uD800-\uDFFF\uFEFF])[^]|[\uD800-\uDBFF][\uDC00-\uDFFF])|[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*\((?:(?:(?![\(\)\uD800-\uDFFF])[^]|[\uD800-\uDBFF][\uDC00-\uDFFF])|\((?:(?![\(\)\uD800-\uDFFF])[^]|[\uD800-\uDBFF][\uDC00-\uDFFF])*\))*\))+[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*)\)$/i;function H(e){let{top:t,left:n,right:r,bottom:i}=e;return{x:{min:n,max:r},y:{min:t,max:i}}}const q=(e,t,n)=>e+(t-e)*n;function K(e){return void 0===e||1===e}function $(e){let{scale:t,scaleX:n,scaleY:r}=e;return!K(t)||!K(n)||!K(r)}function X(e){return $(e)||Y(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function Y(e){return Q(e.x)||Q(e.y)}function Q(e){return e&&"0%"!==e}function G(e,t,n){return n+t*(e-n)}function Z(e,t,n,r,i){return void 0!==i&&(e=G(e,i,r)),G(e,n,r)+t}function J(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3?arguments[3]:void 0,i=arguments.length>4?arguments[4]:void 0;e.min=Z(e.min,t,n,r,i),e.max=Z(e.max,t,n,r,i)}function ee(e,t){let{x:n,y:r}=t;J(e.x,n.translate,n.scale,n.originPoint),J(e.y,r.translate,r.scale,r.originPoint)}const te=.999999999999,ne=1.0000000000001;function re(e,t){e.min=e.min+t,e.max=e.max+t}function ie(e,t,n,r){let i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:.5;J(e,t,n,q(e.min,e.max,i),r)}function ae(e,t){ie(e.x,t.x,t.scaleX,t.scale,t.originX),ie(e.y,t.y,t.scaleY,t.scale,t.originY)}function oe(e,t){return H(function(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}(e.getBoundingClientRect(),t))}const se=new Set(["width","height","top","left","right","bottom",...C]),le=(e,t,n)=>n>t?t:n<e?e:n,ue={test:e=>"number"===typeof e,parse:parseFloat,transform:e=>e},ce=c(c({},ue),{},{transform:e=>le(0,1,e)}),de=c(c({},ue),{},{default:1}),fe=e=>({test:t=>"string"===typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>"".concat(t).concat(e)}),he=fe("deg"),pe=fe("%"),me=fe("px"),ge=fe("vh"),ye=fe("vw"),ve=(()=>c(c({},pe),{},{parse:e=>pe.parse(e)/100,transform:e=>pe.transform(100*e)}))(),be=e=>t=>t.test(e),xe=[ue,me,pe,he,ye,ge,{test:e=>"auto"===e,parse:e=>e}],we=e=>xe.find(be(e));const Se=e=>/^-?(?:[0-9]+(?:\.[0-9]+)?|\.[0-9]+)$/.test(e),ke=/^var\(--(?:([\x2D0-9A-Z_a-z]+)|([\x2D0-9A-Z_a-z]+), ?([ #%\(\),-\.0-9A-Za-z]+))\)/;function Ee(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;'Max CSS variable fallback depth detected in property "'.concat(e,'". This may indicate a circular fallback dependency.');const[r,i]=function(e){const t=ke.exec(e);if(!t)return[,];const[,n,r,i]=t;return["--".concat(null!==n&&void 0!==n?n:r),i]}(e);if(!r)return;const a=window.getComputedStyle(t).getPropertyValue(r);if(a){const e=a.trim();return Se(e)?parseFloat(e):e}return B(i)?Ee(i,t,n+1):i}const je=e=>e===ue||e===me,Te=new Set(["x","y","z"]),Ce=C.filter(e=>!Te.has(e));const Pe={width:(e,t)=>{let{x:n}=e,{paddingLeft:r="0",paddingRight:i="0"}=t;return n.max-n.min-parseFloat(r)-parseFloat(i)},height:(e,t)=>{let{y:n}=e,{paddingTop:r="0",paddingBottom:i="0"}=t;return n.max-n.min-parseFloat(r)-parseFloat(i)},top:(e,t)=>{let{top:n}=t;return parseFloat(n)},left:(e,t)=>{let{left:n}=t;return parseFloat(n)},bottom:(e,t)=>{let{y:n}=e,{top:r}=t;return parseFloat(r)+(n.max-n.min)},right:(e,t)=>{let{x:n}=e,{left:r}=t;return parseFloat(r)+(n.max-n.min)},x:(e,t)=>{let{transform:n}=t;return L(n,"x")},y:(e,t)=>{let{transform:n}=t;return L(n,"y")}};Pe.translateX=Pe.x,Pe.translateY=Pe.y;const Ne=e=>e,Fe={},_e=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],Ae={value:null,addProjectionMetrics:null};function Re(e,t){let n=!1,r=!0;const i={delta:0,timestamp:0,isProcessing:!1},a=()=>n=!0,o=_e.reduce((e,n)=>(e[n]=function(e,t){let n=new Set,r=new Set,i=!1,a=!1;const o=new WeakSet;let s={delta:0,timestamp:0,isProcessing:!1},l=0;function u(t){o.has(t)&&(c.schedule(t),e()),l++,t(s)}const c={schedule:function(e){const t=arguments.length>2&&void 0!==arguments[2]&&arguments[2]&&i?n:r;return arguments.length>1&&void 0!==arguments[1]&&arguments[1]&&o.add(e),t.has(e)||t.add(e),e},cancel:e=>{r.delete(e),o.delete(e)},process:e=>{s=e,i?a=!0:(i=!0,[n,r]=[r,n],n.forEach(u),t&&Ae.value&&Ae.value.frameloop[t].push(l),l=0,n.clear(),i=!1,a&&(a=!1,c.process(e)))}};return c}(a,t?n:void 0),e),{}),{setup:s,read:l,resolveKeyframes:u,preUpdate:c,update:d,preRender:f,render:h,postRender:p}=o,m=()=>{const a=Fe.useManualTiming?i.timestamp:performance.now();n=!1,Fe.useManualTiming||(i.delta=r?1e3/60:Math.max(Math.min(a-i.timestamp,40),1)),i.timestamp=a,i.isProcessing=!0,s.process(i),l.process(i),u.process(i),c.process(i),d.process(i),f.process(i),h.process(i),p.process(i),i.isProcessing=!1,n&&t&&(r=!1,e(m))},g=_e.reduce((t,a)=>{const s=o[a];return t[a]=function(t){let a=arguments.length>1&&void 0!==arguments[1]&&arguments[1],o=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return n||(n=!0,r=!0,i.isProcessing||e(m)),s.schedule(t,a,o)},t},{});return{schedule:g,cancel:e=>{for(let t=0;t<_e.length;t++)o[_e[t]].cancel(e)},state:i,steps:o}}const{schedule:De,cancel:Me,state:Oe,steps:Le}=Re("undefined"!==typeof requestAnimationFrame?requestAnimationFrame:Ne,!0),ze=new Set;let Ve=!1,Ie=!1,Ue=!1;function Be(){if(Ie){const e=Array.from(ze).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),n=new Map;t.forEach(e=>{const t=function(e){const t=[];return Ce.forEach(n=>{const r=e.getValue(n);void 0!==r&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t}(e);t.length&&(n.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();const t=n.get(e);t&&t.forEach(t=>{var n;let[r,i]=t;null===(n=e.getValue(r))||void 0===n||n.set(i)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}Ie=!1,Ve=!1,ze.forEach(e=>e.complete(Ue)),ze.clear()}function We(){ze.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(Ie=!0)})}class He{constructor(e,t,n,r,i){let a=arguments.length>5&&void 0!==arguments[5]&&arguments[5];this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=n,this.motionValue=r,this.element=i,this.isAsync=a}scheduleResolve(){this.state="scheduled",this.isAsync?(ze.add(this),Ve||(Ve=!0,De.read(We),De.resolveKeyframes(Be))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:e,name:t,element:n,motionValue:r}=this;if(null===e[0]){const i=null===r||void 0===r?void 0:r.get(),a=e[e.length-1];if(void 0!==i)e[0]=i;else if(n&&t){const r=n.readValue(t,a);void 0!==r&&null!==r&&(e[0]=r)}void 0===e[0]&&(e[0]=a),r&&void 0===i&&r.set(e[0])}!function(e){for(let n=1;n<e.length;n++){var t;null!==(t=e[n])&&void 0!==t||(e[n]=e[n-1])}}(e)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),ze.delete(this)}cancel(){"scheduled"===this.state&&(ze.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}const qe=e=>/^0(?:[\0-\x08\x0E-\x1F!-\x2D\/-\x9F\xA1-\u167F\u1681-\u1FFF\u200B-\u2027\u202A-\u202E\u2030-\u205E\u2060-\u2FFF\u3001-\uD7FF\uE000-\uFEFE\uFF00-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])+$/.test(e);function Ke(e){return"number"===typeof e?0===e:null===e||("none"===e||"0"===e||qe(e))}const $e=e=>Math.round(1e5*e)/1e5,Xe=/-?(?:[0-9]+(?:\.[0-9]+)?|\.[0-9]+)/g;const Ye=/^(?:#[0-9a-f]{3,8}|(?:rgb|h[s\u017F]l)a?\((?:-?[\.0-9]+%?[\t-\r ,\xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]+){2}-?[\.0-9]+%?[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*(?:[,\/][\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*)?(?:\b[0-9]+(?:\.[0-9]+)?|\.[0-9]+)?%?\))$/i,Qe=(e,t)=>n=>Boolean("string"===typeof n&&Ye.test(n)&&n.startsWith(e)||t&&!function(e){return null==e}(n)&&Object.prototype.hasOwnProperty.call(n,t)),Ge=(e,t,n)=>r=>{if("string"!==typeof r)return r;const[i,a,o,s]=r.match(Xe);return{[e]:parseFloat(i),[t]:parseFloat(a),[n]:parseFloat(o),alpha:void 0!==s?parseFloat(s):1}},Ze=c(c({},ue),{},{transform:e=>Math.round((e=>le(0,255,e))(e))}),Je={test:Qe("rgb","red"),parse:Ge("red","green","blue"),transform:e=>{let{red:t,green:n,blue:r,alpha:i=1}=e;return"rgba("+Ze.transform(t)+", "+Ze.transform(n)+", "+Ze.transform(r)+", "+$e(ce.transform(i))+")"}};const et={test:Qe("#"),parse:function(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}},transform:Je.transform},tt={test:Qe("hsl","hue"),parse:Ge("hue","saturation","lightness"),transform:e=>{let{hue:t,saturation:n,lightness:r,alpha:i=1}=e;return"hsla("+Math.round(t)+", "+pe.transform($e(n))+", "+pe.transform($e(r))+", "+$e(ce.transform(i))+")"}},nt={test:e=>Je.test(e)||et.test(e)||tt.test(e),parse:e=>Je.test(e)?Je.parse(e):tt.test(e)?tt.parse(e):et.parse(e),transform:e=>"string"===typeof e?e:e.hasOwnProperty("red")?Je.transform(e):tt.transform(e),getAnimatableNone:e=>{const t=nt.parse(e);return t.alpha=0,nt.transform(t)}},rt=/(?:#[0-9a-f]{3,8}|(?:rgb|h[s\u017F]l)a?\((?:-?[\.0-9]+%?[\t-\r ,\xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]+){2}-?[\.0-9]+%?[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*(?:[,\/][\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*)?(?:\b[0-9]+(?:\.[0-9]+)?|\.[0-9]+)?%?\))/gi;const it="number",at="color",ot=/var[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*\([\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*--(?:[\x2D0-9A-Z_a-z\u017F\u212A]+[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*|[\x2D0-9A-Z_a-z\u017F\u212A]+[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*,(?:[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*(?:(?![\t-\r \(\)\xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uD800-\uDFFF\uFEFF])[^]|[\uD800-\uDBFF][\uDC00-\uDFFF])|[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*\((?:(?:(?![\(\)\uD800-\uDFFF])[^]|[\uD800-\uDBFF][\uDC00-\uDFFF])|\((?:(?![\(\)\uD800-\uDFFF])[^]|[\uD800-\uDBFF][\uDC00-\uDFFF])*\))*\))+[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*)\)|#[0-9a-f]{3,8}|(?:rgb|h[s\u017F]l)a?\((?:-?[\.0-9]+%?[\t-\r ,\xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]+){2}-?[\.0-9]+%?[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*(?:[,\/][\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*)?(?:\b[0-9]+(?:\.[0-9]+)?|\.[0-9]+)?%?\)|-?(?:[0-9]+(?:\.[0-9]+)?|\.[0-9]+)/gi;function st(e){const t=e.toString(),n=[],r={color:[],number:[],var:[]},i=[];let a=0;const o=t.replace(ot,e=>(nt.test(e)?(r.color.push(a),i.push(at),n.push(nt.parse(e))):e.startsWith("var(")?(r.var.push(a),i.push("var"),n.push(e)):(r.number.push(a),i.push(it),n.push(parseFloat(e))),++a,"${}")).split("${}");return{values:n,split:o,indexes:r,types:i}}function lt(e){return st(e).values}function ut(e){const{split:t,types:n}=st(e),r=t.length;return e=>{let i="";for(let a=0;a<r;a++)if(i+=t[a],void 0!==e[a]){const t=n[a];i+=t===it?$e(e[a]):t===at?nt.transform(e[a]):e[a]}return i}}const ct=e=>"number"===typeof e?0:nt.test(e)?nt.getAnimatableNone(e):e;const dt={test:function(e){var t,n;return isNaN(e)&&"string"===typeof e&&((null===(t=e.match(Xe))||void 0===t?void 0:t.length)||0)+((null===(n=e.match(rt))||void 0===n?void 0:n.length)||0)>0},parse:lt,createTransformer:ut,getAnimatableNone:function(e){const t=lt(e);return ut(e)(t.map(ct))}},ft=new Set(["brightness","contrast","saturate","opacity"]);function ht(e){const[t,n]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;const[r]=n.match(Xe)||[];if(!r)return e;const i=n.replace(r,"");let a=ft.has(t)?1:0;return r!==n&&(a*=100),t+"("+a+i+")"}const pt=/\b([\x2Da-z]*)\((?:[\0-\t\x0B\f\x0E-\u2027\u202A-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])*?\)/g,mt=c(c({},dt),{},{getAnimatableNone:e=>{const t=e.match(pt);return t?t.map(ht).join(" "):e}}),gt=c(c({},ue),{},{transform:Math.round}),yt=c(c({borderWidth:me,borderTopWidth:me,borderRightWidth:me,borderBottomWidth:me,borderLeftWidth:me,borderRadius:me,radius:me,borderTopLeftRadius:me,borderTopRightRadius:me,borderBottomRightRadius:me,borderBottomLeftRadius:me,width:me,maxWidth:me,height:me,maxHeight:me,top:me,right:me,bottom:me,left:me,padding:me,paddingTop:me,paddingRight:me,paddingBottom:me,paddingLeft:me,margin:me,marginTop:me,marginRight:me,marginBottom:me,marginLeft:me,backgroundPositionX:me,backgroundPositionY:me},{rotate:he,rotateX:he,rotateY:he,rotateZ:he,scale:de,scaleX:de,scaleY:de,scaleZ:de,skew:he,skewX:he,skewY:he,distance:me,translateX:me,translateY:me,translateZ:me,x:me,y:me,z:me,perspective:me,transformPerspective:me,opacity:ce,originX:ve,originY:ve,originZ:me}),{},{zIndex:gt,fillOpacity:ce,strokeOpacity:ce,numOctaves:gt}),vt=c(c({},yt),{},{color:nt,backgroundColor:nt,outlineColor:nt,fill:nt,stroke:nt,borderColor:nt,borderTopColor:nt,borderRightColor:nt,borderBottomColor:nt,borderLeftColor:nt,filter:mt,WebkitFilter:mt}),bt=e=>vt[e];function xt(e,t){let n=bt(e);return n!==mt&&(n=dt),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const wt=new Set(["auto","none","0"]);class St extends He{constructor(e,t,n,r,i){super(e,t,n,r,i,!0)}readKeyframes(){const{unresolvedKeyframes:e,element:t,name:n}=this;if(!t||!t.current)return;super.readKeyframes();for(let s=0;s<e.length;s++){let n=e[s];if("string"===typeof n&&(n=n.trim(),B(n))){const r=Ee(n,t.current);void 0!==r&&(e[s]=r),s===e.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!se.has(n)||2!==e.length)return;const[r,i]=e,a=we(r),o=we(i);if(a!==o)if(je(a)&&je(o))for(let s=0;s<e.length;s++){const t=e[s];"string"===typeof t&&(e[s]=parseFloat(t))}else Pe[n]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:e,name:t}=this,n=[];for(let r=0;r<e.length;r++)(null===e[r]||Ke(e[r]))&&n.push(r);n.length&&function(e,t,n){let r,i=0;for(;i<e.length&&!r;){const t=e[i];"string"===typeof t&&!wt.has(t)&&st(t).values.length&&(r=e[i]),i++}if(r&&n)for(const a of t)e[a]=xt(n,r)}(e,n,t)}measureInitialState(){const{element:e,unresolvedKeyframes:t,name:n}=this;if(!e||!e.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=Pe[n](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;const r=t[t.length-1];void 0!==r&&e.getValue(n,r).jump(r,!1)}measureEndState(){var e;const{element:t,name:n,unresolvedKeyframes:r}=this;if(!t||!t.current)return;const i=t.getValue(n);i&&i.jump(this.measuredOrigin,!1);const a=r.length-1,o=r[a];r[a]=Pe[n](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),null!==(e=this.removedTransforms)&&void 0!==e&&e.length&&this.removedTransforms.forEach(e=>{let[n,r]=e;t.getValue(n).set(r)}),this.resolveNoneKeyframes()}}const kt=e=>Boolean(e&&e.getVelocity);let Et;function jt(){Et=void 0}const Tt={now:()=>(void 0===Et&&Tt.set(Oe.isProcessing||Fe.useManualTiming?Oe.timestamp:performance.now()),Et),set:e=>{Et=e,queueMicrotask(jt)}};function Ct(e,t){-1===e.indexOf(t)&&e.push(t)}function Pt(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class Nt{constructor(){this.subscriptions=[]}add(e){return Ct(this.subscriptions,e),()=>Pt(this.subscriptions,e)}notify(e,t,n){const r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](e,t,n);else for(let i=0;i<r;i++){const r=this.subscriptions[i];r&&r(e,t,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function Ft(e,t){return t?e*(1e3/t):0}const _t={current:void 0};class At{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.canTrackVelocity=null,this.events={},this.updateAndNotify=e=>{const t=Tt.now();var n;if((this.updatedAt!==t&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev)&&(null===(n=this.events.change)||void 0===n||n.notify(this.current),this.dependents))for(const r of this.dependents)r.dirty()},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){var t;this.current=e,this.updatedAt=Tt.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=(t=this.current,!isNaN(parseFloat(t))))}setPrevFrameValue(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.current;this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new Nt);const n=this.events[e].add(t);return"change"===e?()=>{n(),De.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e){this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e)}setWithVelocity(e,t,n){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-n}jump(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){var e;null===(e=this.events.change)||void 0===e||e.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return _t.current&&_t.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){const e=Tt.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;const t=Math.min(this.updatedAt-this.prevUpdatedAt,30);return Ft(parseFloat(this.current)-parseFloat(this.prevFrameValue),t)}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){var e,t;null===(e=this.dependents)||void 0===e||e.clear(),null===(t=this.events.destroy)||void 0===t||t.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Rt(e,t){return new At(e,t)}const Dt=[...xe,nt,dt],{schedule:Mt,cancel:Ot}=Re(queueMicrotask,!1),Lt={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},zt={};for(const Bc in Lt)zt[Bc]={isEnabled:e=>Lt[Bc].some(t=>!!e[t])};const Vt=()=>({x:{min:0,max:0},y:{min:0,max:0}}),It="undefined"!==typeof window,Ut={current:null},Bt={current:!1};const Wt=new WeakMap;function Ht(e){return null!==e&&"object"===typeof e&&"function"===typeof e.start}function qt(e){return"string"===typeof e||Array.isArray(e)}const Kt=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],$t=["initial",...Kt];function Xt(e){return Ht(e.animate)||$t.some(t=>qt(e[t]))}function Yt(e){return Boolean(Xt(e)||e.variants)}function Qt(e){const t=[{},{}];return null===e||void 0===e||e.values.forEach((e,n)=>{t[0][n]=e.get(),t[1][n]=e.getVelocity()}),t}function Gt(e,t,n,r){if("function"===typeof t){const[i,a]=Qt(r);t=t(void 0!==n?n:e.custom,i,a)}if("string"===typeof t&&(t=e.variants&&e.variants[t]),"function"===typeof t){const[i,a]=Qt(r);t=t(void 0!==n?n:e.custom,i,a)}return t}const Zt=["willChange"],Jt=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class en{scrapeMotionValuesFromProps(e,t,n){return{}}constructor(e){let{parent:t,props:n,presenceContext:r,reducedMotionConfig:i,blockInitialAnimation:a,visualState:o}=e,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=He,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const e=Tt.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,De.render(this.render,!1,!0))};const{latestValues:l,renderState:u}=o;this.latestValues=l,this.baseTarget=c({},l),this.initialValues=n.initial?c({},l):{},this.renderState=u,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=s,this.blockInitialAnimation=Boolean(a),this.isControllingVariants=Xt(n),this.isVariantNode=Yt(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(t&&t.current);const f=this.scrapeMotionValuesFromProps(n,{},this),{willChange:h}=f,p=d(f,Zt);for(const c in p){const e=p[c];void 0!==l[c]&&kt(e)&&e.set(l[c])}}mount(e){var t;this.current=e,Wt.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),Bt.current||function(){if(Bt.current=!0,It)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>Ut.current=e.matches;e.addEventListener("change",t),t()}else Ut.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||Ut.current),null===(t=this.parent)||void 0===t||t.addChild(this),this.update(this.props,this.presenceContext)}unmount(){var e;this.projection&&this.projection.unmount(),Me(this.notifyUpdate),Me(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),null===(e=this.parent)||void 0===e||e.removeChild(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}addChild(e){var t;this.children.add(e),null!==(t=this.enteringChildren)&&void 0!==t||(this.enteringChildren=new Set),this.enteringChildren.add(e)}removeChild(e){this.children.delete(e),this.enteringChildren&&this.enteringChildren.delete(e)}bindToMotionValue(e,t){this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();const n=P.has(e);n&&this.onBindTransform&&this.onBindTransform();const r=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&De.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0),this.scheduleRender()});let i;window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{r(),i&&i(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in zt){const t=zt[e];if(!t)continue;const{isEnabled:n,Feature:r}=t;if(!this.features[e]&&r&&n(this.props)&&(this.features[e]=new r(this)),this.features[e]){const t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let n=0;n<Jt.length;n++){const t=Jt[n];this.propEventSubscriptions[t]&&(this.propEventSubscriptions[t](),delete this.propEventSubscriptions[t]);const r=e["on"+t];r&&(this.propEventSubscriptions[t]=this.on(t,r))}this.prevMotionValues=function(e,t,n){for(const r in t){const i=t[r],a=n[r];if(kt(i))e.addValue(r,i);else if(kt(a))e.addValue(r,Rt(i,{owner:e}));else if(a!==i)if(e.hasValue(r)){const t=e.getValue(r);!0===t.liveStyle?t.jump(i):t.hasAnimated||t.set(i)}else{const t=e.getStaticValue(r);e.addValue(r,Rt(void 0!==t?t:i,{owner:e}))}}for(const r in n)void 0===t[r]&&e.removeValue(r);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){const t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){const n=this.values.get(e);t!==n&&(n&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);const t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let n=this.values.get(e);return void 0===n&&void 0!==t&&(n=Rt(null===t?void 0:t,{owner:this}),this.addValue(e,n)),n}readValue(e,t){var n;let r=void 0===this.latestValues[e]&&this.current?null!==(n=this.getBaseTargetFromProps(this.props,e))&&void 0!==n?n:this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];var i;return void 0!==r&&null!==r&&("string"===typeof r&&(Se(r)||qe(r))?r=parseFloat(r):(i=r,!Dt.find(be(i))&&dt.test(t)&&(r=xt(e,t))),this.setBaseTarget(e,kt(r)?r.get():r)),kt(r)?r.get():r}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){const{initial:t}=this.props;let n;if("string"===typeof t||"object"===typeof t){var r;const i=Gt(this.props,t,null===(r=this.presenceContext)||void 0===r?void 0:r.custom);i&&(n=i[e])}if(t&&void 0!==n)return n;const i=this.getBaseTargetFromProps(this.props,e);return void 0===i||kt(i)?void 0!==this.initialValues[e]&&void 0===n?void 0:this.baseTarget[e]:i}on(e,t){return this.events[e]||(this.events[e]=new Nt),this.events[e].add(t)}notify(e){if(this.events[e]){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];this.events[e].notify(...n)}}scheduleRenderMicrotask(){Mt.render(this.render)}}class tn extends en{constructor(){super(...arguments),this.KeyframeResolver=St}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,t){let{vars:n,style:r}=t;delete n[e],delete r[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:e}=this.props;kt(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent="".concat(e))}))}}const nn=(e,t)=>t&&"number"===typeof e?t.transform(e):e,rn={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},an=C.length;function on(e,t,n){const{style:r,vars:i,transformOrigin:a}=e;let o=!1,s=!1;for(const l in t){const e=t[l];if(P.has(l))o=!0;else if(I(l))i[l]=e;else{const t=nn(e,yt[l]);l.startsWith("origin")?(s=!0,a[l]=t):r[l]=t}}if(t.transform||(o||n?r.transform=function(e,t,n){let r="",i=!0;for(let a=0;a<an;a++){const o=C[a],s=e[o];if(void 0===s)continue;let l=!0;if(l="number"===typeof s?s===(o.startsWith("scale")?1:0):0===parseFloat(s),!l||n){const e=nn(s,yt[o]);l||(i=!1,r+="".concat(rn[o]||o,"(").concat(e,") ")),n&&(t[o]=e)}}return r=r.trim(),n?r=n(t,i?"":r):i&&(r="none"),r}(t,e.transform,n):r.transform&&(r.transform="none")),s){const{originX:e="50%",originY:t="50%",originZ:n=0}=a;r.transformOrigin="".concat(e," ").concat(t," ").concat(n)}}function sn(e,t,n,r){let{style:i,vars:a}=t;const o=e.style;let s;for(s in i)o[s]=i[s];for(s in null===r||void 0===r||r.applyProjectionStyles(o,n),a)o.setProperty(s,a[s])}const ln={};function un(e,t){let{layout:n,layoutId:r}=t;return P.has(e)||e.startsWith("origin")||(n||void 0!==r)&&(!!ln[e]||"opacity"===e)}function cn(e,t,n){const{style:r}=e,i={};for(const o in r){var a;(kt(r[o])||t.style&&kt(t.style[o])||un(o,e)||void 0!==(null===n||void 0===n||null===(a=n.getValue(o))||void 0===a?void 0:a.liveStyle))&&(i[o]=r[o])}return i}class dn extends tn{constructor(){super(...arguments),this.type="html",this.renderInstance=sn}readValueFromInstance(e,t){var n,r;if(P.has(t))return null!==(n=this.projection)&&void 0!==n&&n.isProjecting?O(t):((e,t)=>{const{transform:n="none"}=getComputedStyle(e);return L(n,t)})(e,t);{const n=(r=e,window.getComputedStyle(r)),i=(I(t)?n.getPropertyValue(t):n[t])||0;return"string"===typeof i?i.trim():i}}measureInstanceViewportBox(e,t){let{transformPagePoint:n}=t;return oe(e,n)}build(e,t,n){on(e,t,n.transformTemplate)}scrapeMotionValuesFromProps(e,t,n){return cn(e,t,n)}}const fn=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),hn={offset:"stroke-dashoffset",array:"stroke-dasharray"},pn={offset:"strokeDashoffset",array:"strokeDasharray"};const mn=["attrX","attrY","attrScale","pathLength","pathSpacing","pathOffset"];function gn(e,t,n,r,i){let{attrX:a,attrY:o,attrScale:s,pathLength:l,pathSpacing:u=1,pathOffset:c=0}=t;if(on(e,d(t,mn),r),n)return void(e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox));e.attrs=e.style,e.style={};const{attrs:f,style:h}=e;var p,m;(f.transform&&(h.transform=f.transform,delete f.transform),h.transform||f.transformOrigin)&&(h.transformOrigin=null!==(p=f.transformOrigin)&&void 0!==p?p:"50% 50%",delete f.transformOrigin);h.transform&&(h.transformBox=null!==(m=null===i||void 0===i?void 0:i.transformBox)&&void 0!==m?m:"fill-box",delete f.transformBox);void 0!==a&&(f.x=a),void 0!==o&&(f.y=o),void 0!==s&&(f.scale=s),void 0!==l&&function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i=!(arguments.length>4&&void 0!==arguments[4])||arguments[4];e.pathLength=1;const a=i?hn:pn;e[a.offset]=me.transform(-r);const o=me.transform(t),s=me.transform(n);e[a.array]="".concat(o," ").concat(s)}(f,l,u,c,!1)}const yn=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]),vn=e=>"string"===typeof e&&"svg"===e.toLowerCase();function bn(e,t,n){const r=cn(e,t,n);for(const i in e)if(kt(e[i])||kt(t[i])){r[-1!==C.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=e[i]}return r}class xn extends tn{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=Vt}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(P.has(t)){const e=bt(t);return e&&e.default||0}return t=yn.has(t)?t:fn(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,n){return bn(e,t,n)}build(e,t,n){gn(e,t,this.isSVGTag,n.transformTemplate,n.style)}renderInstance(e,t,n,r){!function(e,t,n,r){sn(e,t,void 0,r);for(const i in t.attrs)e.setAttribute(yn.has(i)?i:fn(i),t.attrs[i])}(e,t,0,r)}mount(e){this.isSVGTag=vn(e.tagName),super.mount(e)}}const wn=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Sn(e){return"string"===typeof e&&!e.includes("-")&&!!(wn.indexOf(e)>-1||/[A-Z]/.test(e))}const kn=(e,t)=>Sn(e)?new xn(t):new dn(t,{allowProjection:e!==i.Fragment}),En=(0,i.createContext)({}),jn=(0,i.createContext)({strict:!1}),Tn=(0,i.createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),Cn=(0,i.createContext)({});function Pn(e){const{initial:t,animate:n}=function(e,t){if(Xt(e)){const{initial:t,animate:n}=e;return{initial:!1===t||qt(t)?t:void 0,animate:qt(n)?n:void 0}}return!1!==e.inherit?t:{}}(e,(0,i.useContext)(Cn));return(0,i.useMemo)(()=>({initial:t,animate:n}),[Nn(t),Nn(n)])}function Nn(e){return Array.isArray(e)?e.join(" "):e}const Fn=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function _n(e,t,n){for(const r in t)kt(t[r])||un(r,n)||(e[r]=t[r])}function An(e,t){const n={};return _n(n,e.style||{},e),Object.assign(n,function(e,t){let{transformTemplate:n}=e;return(0,i.useMemo)(()=>{const e={style:{},transform:{},transformOrigin:{},vars:{}};return on(e,t,n),Object.assign({},e.vars,e.style)},[t])}(e,t)),n}function Rn(e,t){const n={},r=An(e,t);return e.drag&&!1!==e.dragListener&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===e.drag?"none":"pan-".concat("x"===e.drag?"y":"x")),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n}const Dn=()=>c(c({},{style:{},transform:{},transformOrigin:{},vars:{}}),{},{attrs:{}});function Mn(e,t,n,r){const a=(0,i.useMemo)(()=>{const n=Dn();return gn(n,t,vn(r),e.transformTemplate,e.style),c(c({},n.attrs),{},{style:c({},n.style)})},[t]);if(e.style){const t={};_n(t,e.style,e),a.style=c(c({},t),a.style)}return a}const On=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Ln(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||On.has(e)}let zn=e=>!Ln(e);try{"function"===typeof(Vn=require("@emotion/is-prop-valid").default)&&(zn=e=>e.startsWith("on")?!Ln(e):Vn(e))}catch(Uc){}var Vn;function In(e,t,n,r,a){let{latestValues:o}=r,s=arguments.length>5&&void 0!==arguments[5]&&arguments[5];const l=(Sn(e)?Mn:Rn)(t,o,a,e),u=function(e,t,n){const r={};for(const i in e)"values"===i&&"object"===typeof e.values||(zn(i)||!0===n&&Ln(i)||!t&&!Ln(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}(t,"string"===typeof e,s),d=e!==i.Fragment?c(c(c({},u),l),{},{ref:n}):{},{children:f}=t,h=(0,i.useMemo)(()=>kt(f)?f.get():f,[f]);return(0,i.createElement)(e,c(c({},d),{},{children:h}))}const Un=(0,i.createContext)(null);function Bn(e){const t=(0,i.useRef)(null);return null===t.current&&(t.current=e()),t.current}function Wn(e){return kt(e)?e.get():e}const Hn=["transitionEnd","transition"];function qn(e,t,n,r){const i={},a=r(e,{});for(const d in a)i[d]=Wn(a[d]);let{initial:o,animate:s}=e;const l=Xt(e),u=Yt(e);t&&u&&!l&&!1!==e.inherit&&(void 0===o&&(o=t.initial),void 0===s&&(s=t.animate));let c=!!n&&!1===n.initial;c=c||!1===o;const f=c?s:o;if(f&&"boolean"!==typeof f&&!Ht(f)){const t=Array.isArray(f)?f:[f];for(let n=0;n<t.length;n++){const r=Gt(e,t[n]);if(r){const{transitionEnd:e,transition:t}=r,n=d(r,Hn);for(const r in n){let e=n[r];if(Array.isArray(e)){e=e[c?e.length-1:0]}null!==e&&(i[r]=e)}for(const r in e)i[r]=e[r]}}}return i}const Kn=e=>(t,n)=>{const r=(0,i.useContext)(Cn),a=(0,i.useContext)(Un),o=()=>function(e,t,n,r){let{scrapeMotionValuesFromProps:i,createRenderState:a}=e;return{latestValues:qn(t,n,r,i),renderState:a()}}(e,t,r,a);return n?o():Bn(o)},$n=Kn({scrapeMotionValuesFromProps:cn,createRenderState:Fn}),Xn=Kn({scrapeMotionValuesFromProps:bn,createRenderState:Dn});const Yn=Symbol.for("motionComponentSymbol");function Qn(e){return e&&"object"===typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}function Gn(e,t,n){return(0,i.useCallback)(r=>{r&&e.onMount&&e.onMount(r),t&&(r?t.mount(r):t.unmount()),n&&("function"===typeof n?n(r):Qn(n)&&(n.current=r))},[t])}const Zn="data-"+fn("framerAppearId"),Jn=(0,i.createContext)({}),er=It?i.useLayoutEffect:i.useEffect;function tr(e,t,n,r,a){var o,s,l,u;const{visualElement:c}=(0,i.useContext)(Cn),d=(0,i.useContext)(jn),f=(0,i.useContext)(Un),h=(0,i.useContext)(Tn).reducedMotion,p=(0,i.useRef)(null);r=r||d.renderer,!p.current&&r&&(p.current=r(e,{visualState:t,parent:c,props:n,presenceContext:f,blockInitialAnimation:!!f&&!1===f.initial,reducedMotionConfig:h}));const m=p.current,g=(0,i.useContext)(Jn);!m||m.projection||!a||"html"!==m.type&&"svg"!==m.type||function(e,t,n,r){const{layoutId:i,layout:a,drag:o,dragConstraints:s,layoutScroll:l,layoutRoot:u,layoutCrossfade:c}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:nr(e.parent)),e.projection.setOptions({layoutId:i,layout:a,alwaysMeasureLayout:Boolean(o)||s&&Qn(s),visualElement:e,animationType:"string"===typeof a?a:"both",initialPromotionConfig:r,crossfade:c,layoutScroll:l,layoutRoot:u})}(p.current,n,a,g);const y=(0,i.useRef)(!1);(0,i.useInsertionEffect)(()=>{m&&y.current&&m.update(n,f)});const v=n[Zn],b=(0,i.useRef)(Boolean(v)&&!(null!==(o=(s=window).MotionHandoffIsComplete)&&void 0!==o&&o.call(s,v))&&(null===(l=(u=window).MotionHasOptimisedAnimation)||void 0===l?void 0:l.call(u,v)));return er(()=>{m&&(y.current=!0,window.MotionIsMounted=!0,m.updateFeatures(),m.scheduleRenderMicrotask(),b.current&&m.animationState&&m.animationState.animateChanges())}),(0,i.useEffect)(()=>{m&&(!b.current&&m.animationState&&m.animationState.animateChanges(),b.current&&(queueMicrotask(()=>{var e,t;null===(e=(t=window).MotionHandoffMarkAsComplete)||void 0===e||e.call(t,v)}),b.current=!1),m.enteringChildren=void 0)}),m}function nr(e){if(e)return!1!==e.options.allowProjection?e.projection:nr(e.parent)}function rr(e){var t,n;let{forwardMotionProps:r=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0;a&&function(e){for(const t in e)zt[t]=c(c({},zt[t]),e[t])}(a);const s=Sn(e)?Xn:$n;function l(t,n){let a;const l=c(c(c({},(0,i.useContext)(Tn)),t),{},{layoutId:ir(t)}),{isStatic:u}=l,d=Pn(t),f=s(t,u);if(!u&&It){!function(){(0,i.useContext)(jn).strict;0}();const t=function(e){const{drag:t,layout:n}=zt;if(!t&&!n)return{};const r=c(c({},t),n);return{MeasureLayout:null!==t&&void 0!==t&&t.isEnabled(e)||null!==n&&void 0!==n&&n.isEnabled(e)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(l);a=t.MeasureLayout,d.visualElement=tr(e,f,l,o,t.ProjectionNode)}return(0,j.jsxs)(Cn.Provider,{value:d,children:[a&&d.visualElement?(0,j.jsx)(a,c({visualElement:d.visualElement},l)):null,In(e,t,Gn(f,d.visualElement,n),f,u,r)]})}l.displayName="motion.".concat("string"===typeof e?e:"create(".concat(null!==(t=null!==(n=e.displayName)&&void 0!==n?n:e.name)&&void 0!==t?t:"",")"));const u=(0,i.forwardRef)(l);return u[Yn]=e,u}function ir(e){let{layoutId:t}=e;const n=(0,i.useContext)(En).id;return n&&void 0!==t?n+"-"+t:t}function ar(e,t){if("undefined"===typeof Proxy)return rr;const n=new Map,r=(n,r)=>rr(n,r,e,t);return new Proxy((e,t)=>r(e,t),{get:(i,a)=>"create"===a?r:(n.has(a)||n.set(a,rr(a,void 0,e,t)),n.get(a))})}function or(e,t,n){const r=e.getProps();return Gt(r,t,void 0!==n?n:r.custom,e)}function sr(e,t){var n,r;return null!==(n=null!==(r=null===e||void 0===e?void 0:e[t])&&void 0!==r?r:null===e||void 0===e?void 0:e.default)&&void 0!==n?n:e}const lr=e=>Array.isArray(e),ur=["transitionEnd","transition"];function cr(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,Rt(n))}function dr(e){return lr(e)?e[e.length-1]||0:e}function fr(e,t){const n=e.getValue("willChange");if(r=n,Boolean(kt(r)&&r.add))return n.add(t);if(!n&&Fe.WillChange){const n=new Fe.WillChange("auto");e.addValue("willChange",n),n.add(t)}var r}function hr(e){return e.props[Zn]}function pr(e){e.duration=0,e.type="keyframes"}const mr=(e,t)=>n=>t(e(n)),gr=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.reduce(mr)},yr=e=>1e3*e,vr=e=>e/1e3,br={layout:0,mainThread:0,waapi:0};function xr(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+6*(t-e)*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function wr(e,t){return n=>n>0?t:e}const Sr=(e,t,n)=>{const r=e*e,i=n*(t*t-r)+r;return i<0?0:Math.sqrt(i)},kr=[et,Je,tt];function Er(e){const t=(n=e,kr.find(e=>e.test(n)));var n;if(Boolean(t),"'".concat(e,"' is not an animatable color. Use the equivalent color code instead."),!Boolean(t))return!1;let r=t.parse(e);return t===tt&&(r=function(e){let{hue:t,saturation:n,lightness:r,alpha:i}=e;t/=360,n/=100,r/=100;let a=0,o=0,s=0;if(n){const e=r<.5?r*(1+n):r+n-r*n,i=2*r-e;a=xr(i,e,t+1/3),o=xr(i,e,t),s=xr(i,e,t-1/3)}else a=o=s=r;return{red:Math.round(255*a),green:Math.round(255*o),blue:Math.round(255*s),alpha:i}}(r)),r}const jr=(e,t)=>{const n=Er(e),r=Er(t);if(!n||!r)return wr(e,t);const i=c({},n);return e=>(i.red=Sr(n.red,r.red,e),i.green=Sr(n.green,r.green,e),i.blue=Sr(n.blue,r.blue,e),i.alpha=q(n.alpha,r.alpha,e),Je.transform(i))},Tr=new Set(["none","hidden"]);function Cr(e,t){return n=>q(e,t,n)}function Pr(e){return"number"===typeof e?Cr:"string"===typeof e?B(e)?wr:nt.test(e)?jr:_r:Array.isArray(e)?Nr:"object"===typeof e?nt.test(e)?jr:Fr:wr}function Nr(e,t){const n=[...e],r=n.length,i=e.map((e,n)=>Pr(e)(e,t[n]));return e=>{for(let t=0;t<r;t++)n[t]=i[t](e);return n}}function Fr(e,t){const n=c(c({},e),t),r={};for(const i in n)void 0!==e[i]&&void 0!==t[i]&&(r[i]=Pr(e[i])(e[i],t[i]));return e=>{for(const t in r)n[t]=r[t](e);return n}}const _r=(e,t)=>{const n=dt.createTransformer(t),r=st(e),i=st(t);return r.indexes.var.length===i.indexes.var.length&&r.indexes.color.length===i.indexes.color.length&&r.indexes.number.length>=i.indexes.number.length?Tr.has(e)&&!i.values.length||Tr.has(t)&&!r.values.length?function(e,t){return Tr.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}(e,t):gr(Nr(function(e,t){const n=[],r={color:0,var:0,number:0};for(let a=0;a<t.values.length;a++){var i;const o=t.types[a],s=e.indexes[o][r[o]],l=null!==(i=e.values[s])&&void 0!==i?i:0;n[a]=l,r[o]++}return n}(r,i),i.values),n):("Complex values '".concat(e,"' and '").concat(t,"' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition."),wr(e,t))};function Ar(e,t,n){if("number"===typeof e&&"number"===typeof t&&"number"===typeof n)return q(e,t,n);return Pr(e)(e,t)}const Rr=e=>{const t=t=>{let{timestamp:n}=t;return e(n)};return{start:function(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return De.update(t,e)},stop:()=>Me(t),now:()=>Oe.isProcessing?Oe.timestamp:Tt.now()}},Dr=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10,r="";const i=Math.max(Math.round(t/n),2);for(let a=0;a<i;a++)r+=Math.round(1e4*e(a/(i-1)))/1e4+", ";return"linear(".concat(r.substring(0,r.length-2),")")},Mr=2e4;function Or(e){let t=0;let n=e.next(t);for(;!n.done&&t<Mr;)t+=50,n=e.next(t);return t>=Mr?1/0:t}function Lr(e,t,n){const r=Math.max(t-5,0);return Ft(n-e(r),t-r)}const zr={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},Vr=.001;function Ir(e){let t,n,{duration:r=zr.duration,bounce:i=zr.bounce,velocity:a=zr.velocity,mass:o=zr.mass}=e;yr(zr.maxDuration);let s=1-i;s=le(zr.minDamping,zr.maxDamping,s),r=le(zr.minDuration,zr.maxDuration,vr(r)),s<1?(t=e=>{const t=e*s,n=t*r,i=t-a,o=Br(e,s),l=Math.exp(-n);return Vr-i/o*l},n=e=>{const n=e*s*r,i=n*a+a,o=Math.pow(s,2)*Math.pow(e,2)*r,l=Math.exp(-n),u=Br(Math.pow(e,2),s);return(-t(e)+Vr>0?-1:1)*((i-o)*l)/u}):(t=e=>Math.exp(-e*r)*((e-a)*r+1)-.001,n=e=>Math.exp(-e*r)*(r*r*(a-e)));const l=function(e,t,n){let r=n;for(let i=1;i<Ur;i++)r-=e(r)/t(r);return r}(t,n,5/r);if(r=yr(r),isNaN(l))return{stiffness:zr.stiffness,damping:zr.damping,duration:r};{const e=Math.pow(l,2)*o;return{stiffness:e,damping:2*s*Math.sqrt(o*e),duration:r}}}const Ur=12;function Br(e,t){return e*Math.sqrt(1-t*t)}const Wr=["duration","bounce"],Hr=["stiffness","damping","mass"];function qr(e,t){return t.some(t=>void 0!==e[t])}function Kr(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:zr.visualDuration,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:zr.bounce;const n="object"!==typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e;let{restSpeed:r,restDelta:i}=n;const a=n.keyframes[0],o=n.keyframes[n.keyframes.length-1],s={done:!1,value:a},{stiffness:l,damping:u,mass:d,duration:f,velocity:h,isResolvedFromDuration:p}=function(e){let t=c({velocity:zr.velocity,stiffness:zr.stiffness,damping:zr.damping,mass:zr.mass,isResolvedFromDuration:!1},e);if(!qr(e,Hr)&&qr(e,Wr))if(e.visualDuration){const n=e.visualDuration,r=2*Math.PI/(1.2*n),i=r*r,a=2*le(.05,1,1-(e.bounce||0))*Math.sqrt(i);t=c(c({},t),{},{mass:zr.mass,stiffness:i,damping:a})}else{const n=Ir(e);t=c(c(c({},t),n),{},{mass:zr.mass}),t.isResolvedFromDuration=!0}return t}(c(c({},n),{},{velocity:-vr(n.velocity||0)})),m=h||0,g=u/(2*Math.sqrt(l*d)),y=o-a,v=vr(Math.sqrt(l/d)),b=Math.abs(y)<5;let x;if(r||(r=b?zr.restSpeed.granular:zr.restSpeed.default),i||(i=b?zr.restDelta.granular:zr.restDelta.default),g<1){const e=Br(v,g);x=t=>{const n=Math.exp(-g*v*t);return o-n*((m+g*v*y)/e*Math.sin(e*t)+y*Math.cos(e*t))}}else if(1===g)x=e=>o-Math.exp(-v*e)*(y+(m+v*y)*e);else{const e=v*Math.sqrt(g*g-1);x=t=>{const n=Math.exp(-g*v*t),r=Math.min(e*t,300);return o-n*((m+g*v*y)*Math.sinh(r)+e*y*Math.cosh(r))/e}}const w={calculatedDuration:p&&f||null,next:e=>{const t=x(e);if(p)s.done=e>=f;else{let n=0===e?m:0;g<1&&(n=0===e?yr(m):Lr(x,e,t));const a=Math.abs(n)<=r,l=Math.abs(o-t)<=i;s.done=a&&l}return s.value=s.done?o:t,s},toString:()=>{const e=Math.min(Or(w),Mr),t=Dr(t=>w.next(e*t).value,e,30);return e+"ms "+t},toTransition:()=>{}};return w}function $r(e){let{keyframes:t,velocity:n=0,power:r=.8,timeConstant:i=325,bounceDamping:a=10,bounceStiffness:o=500,modifyTarget:s,min:l,max:u,restDelta:c=.5,restSpeed:d}=e;const f=t[0],h={done:!1,value:f},p=e=>void 0===l?u:void 0===u||Math.abs(l-e)<Math.abs(u-e)?l:u;let m=r*n;const g=f+m,y=void 0===s?g:s(g);y!==g&&(m=y-f);const v=e=>-m*Math.exp(-e/i),b=e=>y+v(e),x=e=>{const t=v(e),n=b(e);h.done=Math.abs(t)<=c,h.value=h.done?y:n};let w,S;const k=e=>{var t;(t=h.value,void 0!==l&&t<l||void 0!==u&&t>u)&&(w=e,S=Kr({keyframes:[h.value,p(h.value)],velocity:Lr(b,e,h.value),damping:a,stiffness:o,restDelta:c,restSpeed:d}))};return k(0),{calculatedDuration:null,next:e=>{let t=!1;return S||void 0!==w||(t=!0,x(e),k(e)),void 0!==w&&e>=w?S.next(e-w):(!t&&x(e),h)}}}Kr.applyToOptions=e=>{const t=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100;const n=(arguments.length>2?arguments[2]:void 0)(c(c({},e),{},{keyframes:[0,t]})),r=Math.min(Or(n),Mr);return{type:"keyframes",ease:e=>n.next(r*e).value/t,duration:vr(r)}}(e,100,Kr);return e.ease=t.ease,e.duration=yr(t.duration),e.type="keyframes",e};const Xr=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e;function Yr(e,t,n,r){if(e===t&&n===r)return Ne;const i=t=>function(e,t,n,r,i){let a,o,s=0;do{o=t+(n-t)/2,a=Xr(o,r,i)-e,a>0?n=o:t=o}while(Math.abs(a)>1e-7&&++s<12);return o}(t,0,1,e,n);return e=>0===e||1===e?e:Xr(i(e),t,r)}const Qr=Yr(.42,0,1,1),Gr=Yr(0,0,.58,1),Zr=Yr(.42,0,.58,1),Jr=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,ei=e=>t=>1-e(1-t),ti=Yr(.33,1.53,.69,.99),ni=ei(ti),ri=Jr(ni),ii=e=>(e*=2)<1?.5*ni(e):.5*(2-Math.pow(2,-10*(e-1))),ai=e=>1-Math.sin(Math.acos(e)),oi=ei(ai),si=Jr(ai),li=e=>Array.isArray(e)&&"number"===typeof e[0],ui={linear:Ne,easeIn:Qr,easeInOut:Zr,easeOut:Gr,circIn:ai,circInOut:si,circOut:oi,backIn:ni,backInOut:ri,backOut:ti,anticipate:ii},ci=e=>{if(li(e)){e.length;const[t,n,r,i]=e;return Yr(t,n,r,i)}return"string"===typeof e?("Invalid easing type '".concat(e,"'"),ui[e]):e},di=(e,t,n)=>{const r=t-e;return 0===r?1:(n-e)/r};function fi(e,t){let{clamp:n=!0,ease:r,mixer:i}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const a=e.length;if(t.length,1===a)return()=>t[0];if(2===a&&t[0]===t[1])return()=>t[1];const o=e[0]===e[1];e[0]>e[a-1]&&(e=[...e].reverse(),t=[...t].reverse());const s=function(e,t,n){const r=[],i=n||Fe.mix||Ar,a=e.length-1;for(let o=0;o<a;o++){let n=i(e[o],e[o+1]);if(t){const e=Array.isArray(t)?t[o]||Ne:t;n=gr(e,n)}r.push(n)}return r}(t,r,i),l=s.length,u=n=>{if(o&&n<e[0])return t[0];let r=0;if(l>1)for(;r<e.length-2&&!(n<e[r+1]);r++);const i=di(e[r],e[r+1],n);return s[r](i)};return n?t=>u(le(e[0],e[a-1],t)):u}function hi(e){const t=[0];return function(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const i=di(0,t,r);e.push(q(n,1,i))}}(t,e.length-1),t}function pi(e){let{duration:t=300,keyframes:n,times:r,ease:i="easeInOut"}=e;const a=(e=>Array.isArray(e)&&"number"!==typeof e[0])(i)?i.map(ci):ci(i),o={done:!1,value:n[0]},s=function(e,t){return e.map(e=>e*t)}(r&&r.length===n.length?r:hi(n),t),l=fi(s,n,{ease:Array.isArray(a)?a:(u=n,c=a,u.map(()=>c||Zr).splice(0,u.length-1))});var u,c;return{calculatedDuration:t,next:e=>(o.value=l(e),o.done=e>=t,o)}}const mi=e=>null!==e;function gi(e,t,n){let{repeat:r,repeatType:i="loop"}=t,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1;const o=e.filter(mi),s=a<0||r&&"loop"!==i&&r%2===1?0:o.length-1;return s&&void 0!==n?n:o[s]}const yi={decay:$r,inertia:$r,tween:pi,keyframes:pi,spring:Kr};function vi(e){"string"===typeof e.type&&(e.type=yi[e.type])}class bi{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,t){return this.finished.then(e,t)}}const xi=e=>e/100;class wi extends bi{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{var e,t;const{motionValue:n}=this.options;n&&n.updatedAt!==Tt.now()&&this.tick(Tt.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),null===(e=(t=this.options).onStop)||void 0===e||e.call(t))},br.mainThread++,this.options=e,this.initAnimation(),this.play(),!1===e.autoplay&&this.pause()}initAnimation(){const{options:e}=this;vi(e);const{type:t=pi,repeat:n=0,repeatDelay:r=0,repeatType:i,velocity:a=0}=e;let{keyframes:o}=e;const s=t||pi;s!==pi&&"number"!==typeof o[0]&&(this.mixKeyframes=gr(xi,Ar(o[0],o[1])),o=[0,100]);const l=s(c(c({},e),{},{keyframes:o}));"mirror"===i&&(this.mirroredGenerator=s(c(c({},e),{},{keyframes:[...o].reverse(),velocity:-a}))),null===l.calculatedDuration&&(l.calculatedDuration=Or(l));const{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+r,this.totalDuration=this.resolvedDuration*(n+1)-r,this.generator=l}updateTime(e){const t=Math.round(e-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=t}tick(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const{generator:n,totalDuration:r,mixKeyframes:i,mirroredGenerator:a,resolvedDuration:o,calculatedDuration:s}=this;if(null===this.startTime)return n.next(0);const{delay:l=0,keyframes:u,repeat:c,repeatType:d,repeatDelay:f,type:h,onUpdate:p,finalKeyframe:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-r/this.speed,this.startTime)),t?this.currentTime=e:this.updateTime(e);const g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>r;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=r);let v=this.currentTime,b=n;if(c){const e=Math.min(this.currentTime,r)/o;let t=Math.floor(e),n=e%1;!n&&e>=1&&(n=1),1===n&&t--,t=Math.min(t,c+1);Boolean(t%2)&&("reverse"===d?(n=1-n,f&&(n-=f/o)):"mirror"===d&&(b=a)),v=le(0,1,n)*o}const x=y?{done:!1,value:u[0]}:b.next(v);i&&(x.value=i(x.value));let{done:w}=x;y||null===s||(w=this.playbackSpeed>=0?this.currentTime>=r:this.currentTime<=0);const S=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return S&&h!==$r&&(x.value=gi(u,this.options,m,this.speed)),p&&p(x.value),S&&this.finish(),x}then(e,t){return this.finished.then(e,t)}get duration(){return vr(this.calculatedDuration)}get iterationDuration(){const{delay:e=0}=this.options||{};return this.duration+vr(e)}get time(){return vr(this.currentTime)}set time(e){var t;e=yr(e),this.currentTime=e,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),null===(t=this.driver)||void 0===t||t.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(Tt.now());const t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=vr(this.currentTime))}play(){var e,t;if(this.isStopped)return;const{driver:n=Rr,startTime:r}=this.options;this.driver||(this.driver=n(e=>this.tick(e))),null===(e=(t=this.options).onPlay)||void 0===e||e.call(t);const i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=null!==r&&void 0!==r?r:i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(Tt.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){var e,t;this.notifyFinished(),this.teardown(),this.state="finished",null===(e=(t=this.options).onComplete)||void 0===e||e.call(t)}cancel(){var e,t;this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),null===(e=(t=this.options).onCancel)||void 0===e||e.call(t)}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,br.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){var t;return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),null===(t=this.driver)||void 0===t||t.stop(),e.observe(this)}}function Si(e){let t;return()=>(void 0===t&&(t=e()),t)}const ki=Si(()=>void 0!==window.ScrollTimeline),Ei={};function ji(e,t){const n=Si(e);return()=>{var e;return null!==(e=Ei[t])&&void 0!==e?e:n()}}const Ti=ji(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing"),Ci=e=>{let[t,n,r,i]=e;return"cubic-bezier(".concat(t,", ").concat(n,", ").concat(r,", ").concat(i,")")},Pi={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Ci([0,.65,.55,1]),circOut:Ci([.55,0,1,.45]),backIn:Ci([.31,.01,.66,-.59]),backOut:Ci([.33,1.53,.69,.99])};function Ni(e,t){return e?"function"===typeof e?Ti()?Dr(e,t):"ease-out":li(e)?Ci(e):Array.isArray(e)?e.map(e=>Ni(e,t)||Pi.easeOut):Pi[e]:void 0}function Fi(e,t,n){let{delay:r=0,duration:i=300,repeat:a=0,repeatType:o="loop",ease:s="easeOut",times:l}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},u=arguments.length>4&&void 0!==arguments[4]?arguments[4]:void 0;const c={[t]:n};l&&(c.offset=l);const d=Ni(s,i);Array.isArray(d)&&(c.easing=d),Ae.value&&br.waapi++;const f={delay:r,duration:i,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:a+1,direction:"reverse"===o?"alternate":"normal"};u&&(f.pseudoElement=u);const h=e.animate(c,f);return Ae.value&&h.finished.finally(()=>{br.waapi--}),h}function _i(e){return"function"===typeof e&&"applyToOptions"in e}const Ai=["type"];class Ri extends bi{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;const{element:t,name:n,keyframes:r,pseudoElement:i,allowFlatten:a=!1,finalKeyframe:o,onComplete:s}=e;this.isPseudoElement=Boolean(i),this.allowFlatten=a,this.options=e,e.type;const l=function(e){let{type:t}=e,n=d(e,Ai);return _i(t)&&Ti()?t.applyToOptions(n):(null!==(r=n.duration)&&void 0!==r||(n.duration=300),null!==(i=n.ease)&&void 0!==i||(n.ease="easeOut"),n);var r,i}(e);this.animation=Fi(t,n,r,l,i),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!i){const e=gi(r,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(e):function(e,t,n){(e=>e.startsWith("--"))(t)?e.style.setProperty(t,n):e.style[t]=n}(t,n,e),this.animation.cancel()}null===s||void 0===s||s(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){var e,t;null===(e=(t=this.animation).finish)||void 0===e||e.call(t)}cancel(){try{this.animation.cancel()}catch(e){}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:e}=this;"idle"!==e&&"finished"!==e&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){var e,t;this.isPseudoElement||(null===(e=(t=this.animation).commitStyles)||void 0===e||e.call(t))}get duration(){var e,t;const n=(null===(e=this.animation.effect)||void 0===e||null===(t=e.getComputedTiming)||void 0===t?void 0:t.call(e).duration)||0;return vr(Number(n))}get iterationDuration(){const{delay:e=0}=this.options||{};return this.duration+vr(e)}get time(){return vr(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=yr(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline(e){let{timeline:t,observe:n}=e;var r;this.allowFlatten&&(null===(r=this.animation.effect)||void 0===r||r.updateTiming({easing:"linear"}));return this.animation.onfinish=null,t&&ki()?(this.animation.timeline=t,Ne):n(this)}}const Di={anticipate:ii,backInOut:ri,circInOut:si};function Mi(e){"string"===typeof e.ease&&e.ease in Di&&(e.ease=Di[e.ease])}const Oi=["motionValue","onUpdate","onComplete","element"];class Li extends Ri{constructor(e){Mi(e),vi(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){var t;const n=this.options,{motionValue:r,onUpdate:i,onComplete:a,element:o}=n,s=d(n,Oi);if(!r)return;if(void 0!==e)return void r.set(e);const l=new wi(c(c({},s),{},{autoplay:!1})),u=yr(null!==(t=this.finishedTime)&&void 0!==t?t:this.time);r.setWithVelocity(l.sample(u-10).value,l.sample(u).value,10),l.stop()}}const zi=(e,t)=>"zIndex"!==t&&(!("number"!==typeof e&&!Array.isArray(e))||!("string"!==typeof e||!dt.test(e)&&"0"!==e||e.startsWith("url(")));const Vi=new Set(["opacity","clipPath","filter","transform"]),Ii=Si(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));const Ui=["autoplay","delay","type","repeat","repeatDelay","repeatType","keyframes","name","motionValue","element"];class Bi extends bi{constructor(e){var t;let{autoplay:n=!0,delay:r=0,type:i="keyframes",repeat:a=0,repeatDelay:o=0,repeatType:s="loop",keyframes:l,name:u,motionValue:f,element:h}=e,p=d(e,Ui);super(),this.stop=()=>{var e,t;this._animation&&(this._animation.stop(),null===(t=this.stopTimeline)||void 0===t||t.call(this));null===(e=this.keyframeResolver)||void 0===e||e.cancel()},this.createdAt=Tt.now();const m=c({autoplay:n,delay:r,type:i,repeat:a,repeatDelay:o,repeatType:s,name:u,motionValue:f,element:h},p),g=(null===h||void 0===h?void 0:h.KeyframeResolver)||He;this.keyframeResolver=new g(l,(e,t,n)=>this.onKeyframesResolved(e,t,m,!n),u,f,h),null===(t=this.keyframeResolver)||void 0===t||t.scheduleResolve()}onKeyframesResolved(e,t,n,r){this.keyframeResolver=void 0;const{name:i,type:a,velocity:o,delay:s,isHandoff:l,onUpdate:u}=n;this.resolvedAt=Tt.now(),function(e,t,n,r){const i=e[0];if(null===i)return!1;if("display"===t||"visibility"===t)return!0;const a=e[e.length-1],o=zi(i,t),s=zi(a,t);return"You are trying to animate ".concat(t,' from "').concat(i,'" to "').concat(a,'". "').concat(o?a:i,'" is not an animatable value.'),!(!o||!s)&&(function(e){const t=e[0];if(1===e.length)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}(e)||("spring"===n||_i(n))&&r)}(e,i,a,o)||(!Fe.instantAnimations&&s||null===u||void 0===u||u(gi(e,n,t)),e[0]=e[e.length-1],pr(n),n.repeat=0);const d=c(c({startTime:r?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:t},n),{},{keyframes:e}),f=!l&&function(e){var t;const{motionValue:n,name:r,repeatDelay:i,repeatType:a,damping:o,type:s}=e;if(!((null===n||void 0===n||null===(t=n.owner)||void 0===t?void 0:t.current)instanceof HTMLElement))return!1;const{onUpdate:l,transformTemplate:u}=n.owner.getProps();return Ii()&&r&&Vi.has(r)&&("transform"!==r||!u)&&!l&&!i&&"mirror"!==a&&0!==o&&"inertia"!==s}(d)?new Li(c(c({},d),{},{element:d.motionValue.owner.current})):new wi(d);f.finished.then(()=>this.notifyFinished()).catch(Ne),this.pendingTimeline&&(this.stopTimeline=f.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=f}get finished(){return this._animation?this.animation.finished:this._finished}then(e,t){return this.finished.finally(e).then(()=>{})}get animation(){var e;this._animation||(null===(e=this.keyframeResolver)||void 0===e||e.resume(),Ue=!0,We(),Be(),Ue=!1);return this._animation}get duration(){return this.animation.duration}get iterationDuration(){return this.animation.iterationDuration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){var e;this._animation&&this.animation.cancel(),null===(e=this.keyframeResolver)||void 0===e||e.cancel()}}const Wi=e=>null!==e;const Hi={type:"spring",stiffness:500,damping:25,restSpeed:10},qi={type:"keyframes",duration:.8},Ki={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},$i=(e,t)=>{let{keyframes:n}=t;return n.length>2?qi:P.has(e)?e.startsWith("scale")?{type:"spring",stiffness:550,damping:0===n[1]?2*Math.sqrt(550):30,restSpeed:10}:Hi:Ki},Xi=["when","delay","delayChildren","staggerChildren","staggerDirection","repeat","repeatType","repeatDelay","from","elapsed"];const Yi=function(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=arguments.length>4?arguments[4]:void 0,a=arguments.length>5?arguments[5]:void 0;return o=>{const s=sr(r,e)||{},l=s.delay||r.delay||0;let{elapsed:u=0}=r;u-=yr(l);const f=c(c({keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity()},s),{},{delay:-u,onUpdate:e=>{t.set(e),s.onUpdate&&s.onUpdate(e)},onComplete:()=>{o(),s.onComplete&&s.onComplete()},name:e,motionValue:t,element:a?void 0:i});(function(e){let{when:t,delay:n,delayChildren:r,staggerChildren:i,staggerDirection:a,repeat:o,repeatType:s,repeatDelay:l,from:u,elapsed:c}=e,f=d(e,Xi);return!!Object.keys(f).length})(s)||Object.assign(f,$i(e,f)),f.duration&&(f.duration=yr(f.duration)),f.repeatDelay&&(f.repeatDelay=yr(f.repeatDelay)),void 0!==f.from&&(f.keyframes[0]=f.from);let h=!1;if((!1===f.type||0===f.duration&&!f.repeatDelay)&&(pr(f),0===f.delay&&(h=!0)),(Fe.instantAnimations||Fe.skipAnimations)&&(h=!0,pr(f),f.delay=0),f.allowFlatten=!s.type&&!s.ease,h&&!a&&void 0!==t.get()){const e=function(e,t,n){let{repeat:r,repeatType:i="loop"}=t;const a=e.filter(Wi),o=r&&"loop"!==i&&r%2===1?0:a.length-1;return o&&void 0!==n?n:a[o]}(f.keyframes,s);if(void 0!==e)return void De.update(()=>{f.onUpdate(e),f.onComplete()})}return s.isSync?new wi(f):new Bi(f)}},Qi=["transition","transitionEnd"];function Gi(e,t){let{protectedKeys:n,needsAnimating:r}=e;const i=n.hasOwnProperty(t)&&!0!==r[t];return r[t]=!1,i}function Zi(e,t){let{delay:n=0,transitionOverride:r,type:i}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{transition:a=e.getDefaultTransition(),transitionEnd:o}=t,s=d(t,Qi);r&&(a=r);const l=[],u=i&&e.animationState&&e.animationState.getState()[i];for(const d in s){var f;const t=e.getValue(d,null!==(f=e.latestValues[d])&&void 0!==f?f:null),r=s[d];if(void 0===r||u&&Gi(u,d))continue;const i=c({delay:n},sr(a||{},d)),o=t.get();if(void 0!==o&&!t.isAnimating&&!Array.isArray(r)&&r===o&&!i.velocity)continue;let h=!1;if(window.MotionHandoffAnimation){const t=hr(e);if(t){const e=window.MotionHandoffAnimation(t,d,De);null!==e&&(i.startTime=e,h=!0)}}fr(e,d),t.start(Yi(d,t,r,e.shouldReduceMotion&&se.has(d)?{type:!1}:i,e,h));const p=t.animation;p&&l.push(p)}return o&&Promise.all(l).then(()=>{De.update(()=>{o&&function(e,t){let n=or(e,t)||{},{transitionEnd:r={},transition:i={}}=n,a=d(n,ur);a=c(c({},a),r);for(const o in a)cr(e,o,dr(a[o]))}(e,o)})}),l}function Ji(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1;const a=Array.from(e).sort((e,t)=>e.sortNodePosition(t)).indexOf(t),o=e.size,s=(o-1)*r;return"function"===typeof n?n(a,o):1===i?a*r:s-a*r}function ea(e,t){var n;let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const i=or(e,t,"exit"===r.type?null===(n=e.presenceContext)||void 0===n?void 0:n.custom:void 0);let{transition:a=e.getDefaultTransition()||{}}=i||{};r.transitionOverride&&(a=r.transitionOverride);const o=i?()=>Promise.all(Zi(e,i,r)):()=>Promise.resolve(),s=e.variantChildren&&e.variantChildren.size?function(){let n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;const{delayChildren:i=0,staggerChildren:o,staggerDirection:s}=a;return function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:1,o=arguments.length>6?arguments[6]:void 0;const s=[];for(const l of e.variantChildren)l.notify("AnimationStart",t),s.push(ea(l,t,c(c({},o),{},{delay:n+("function"===typeof r?0:r)+Ji(e.variantChildren,l,r,i,a)})).then(()=>l.notify("AnimationComplete",t)));return Promise.all(s)}(e,t,n,i,o,s,r)}:()=>Promise.resolve(),{when:l}=a;if(l){const[e,t]="beforeChildren"===l?[o,s]:[s,o];return e().then(()=>t())}return Promise.all([o(),s(r.delay)])}function ta(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}const na=$t.length;function ra(e){if(!e)return;if(!e.isControllingVariants){const t=e.parent&&ra(e.parent)||{};return void 0!==e.props.initial&&(t.initial=e.props.initial),t}const t={};for(let n=0;n<na;n++){const r=$t[n],i=e.props[r];(qt(i)||!1===i)&&(t[r]=i)}return t}const ia=["transition","transitionEnd"],aa=[...Kt].reverse(),oa=Kt.length;function sa(e){return t=>Promise.all(t.map(t=>{let{animation:n,options:r}=t;return function(e,t){let n,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(e.notify("AnimationStart",t),Array.isArray(t)){const i=t.map(t=>ea(e,t,r));n=Promise.all(i)}else if("string"===typeof t)n=ea(e,t,r);else{const i="function"===typeof t?or(e,t,r.custom):t;n=Promise.all(Zi(e,i,r))}return n.then(()=>{e.notify("AnimationComplete",t)})}(e,n,r)}))}function la(e){let t=sa(e),n=da(),r=!0;const i=t=>(n,r)=>{var i;const a=or(e,r,"exit"===t?null===(i=e.presenceContext)||void 0===i?void 0:i.custom:void 0);if(a){const{transition:e,transitionEnd:t}=a,r=d(a,ia);n=c(c(c({},n),r),t)}return n};function a(a){const{props:o}=e,s=ra(e.parent)||{},l=[],u=new Set;let d={},f=1/0;for(let t=0;t<oa;t++){const h=aa[t],p=n[h],m=void 0!==o[h]?o[h]:s[h],g=qt(m),y=h===a?p.isActive:null;!1===y&&(f=t);let v=m===s[h]&&m!==o[h]&&g;if(v&&r&&e.manuallyAnimateOnMount&&(v=!1),p.protectedKeys=c({},d),!p.isActive&&null===y||!m&&!p.prevProp||Ht(m)||"boolean"===typeof m)continue;const b=ua(p.prevProp,m);let x=b||h===a&&p.isActive&&!v&&g||t>f&&g,w=!1;const S=Array.isArray(m)?m:[m];let k=S.reduce(i(h),{});!1===y&&(k={});const{prevResolvedValues:E={}}=p,j=c(c({},E),k),T=t=>{x=!0,u.has(t)&&(w=!0,u.delete(t)),p.needsAnimating[t]=!0;const n=e.getValue(t);n&&(n.liveStyle=!1)};for(const e in j){const t=k[e],n=E[e];if(d.hasOwnProperty(e))continue;let r=!1;r=lr(t)&&lr(n)?!ta(t,n):t!==n,r?void 0!==t&&null!==t?T(e):u.add(e):void 0!==t&&u.has(e)?T(e):p.protectedKeys[e]=!0}p.prevProp=m,p.prevResolvedValues=k,p.isActive&&(d=c(c({},d),k)),r&&e.blockInitialAnimation&&(x=!1);const C=v&&b;x&&(!C||w)&&l.push(...S.map(t=>{const n={type:h};if("string"===typeof t&&r&&!C&&e.manuallyAnimateOnMount&&e.parent){const{parent:r}=e,i=or(r,t);if(r.enteringChildren&&i){const{delayChildren:t}=i.transition||{};n.delay=Ji(r.enteringChildren,e,t)}}return{animation:t,options:n}}))}if(u.size){const t={};if("boolean"!==typeof o.initial){const n=or(e,Array.isArray(o.initial)?o.initial[0]:o.initial);n&&n.transition&&(t.transition=n.transition)}u.forEach(n=>{const r=e.getBaseTarget(n),i=e.getValue(n);i&&(i.liveStyle=!0),t[n]=null!==r&&void 0!==r?r:null}),l.push({animation:t})}let h=Boolean(l.length);return!r||!1!==o.initial&&o.initial!==o.animate||e.manuallyAnimateOnMount||(h=!1),r=!1,h?t(l):Promise.resolve()}return{animateChanges:a,setActive:function(t,r){var i;if(n[t].isActive===r)return Promise.resolve();null===(i=e.variantChildren)||void 0===i||i.forEach(e=>{var n;return null===(n=e.animationState)||void 0===n?void 0:n.setActive(t,r)}),n[t].isActive=r;const o=a(t);for(const e in n)n[e].protectedKeys={};return o},setAnimateFunction:function(n){t=n(e)},getState:()=>n,reset:()=>{n=da(),r=!0}}}function ua(e,t){return"string"===typeof t?t!==e:!!Array.isArray(t)&&!ta(t,e)}function ca(){return{isActive:arguments.length>0&&void 0!==arguments[0]&&arguments[0],protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function da(){return{animate:ca(!0),whileInView:ca(),whileHover:ca(),whileTap:ca(),whileDrag:ca(),whileFocus:ca(),exit:ca()}}class fa{constructor(e){this.isMounted=!1,this.node=e}update(){}}let ha=0;const pa={animation:{Feature:class extends fa{constructor(e){super(e),e.animationState||(e.animationState=la(e))}updateAnimationControlsSubscription(){const{animate:e}=this.node.getProps();Ht(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){var e;this.node.animationState.reset(),null===(e=this.unmountControls)||void 0===e||e.call(this)}}},exit:{Feature:class extends fa{constructor(){super(...arguments),this.id=ha++}update(){if(!this.node.presenceContext)return;const{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===n)return;const r=this.node.animationState.setActive("exit",!e);t&&!e&&r.then(()=>{t(this.id)})}mount(){const{register:e,onExitComplete:t}=this.node.presenceContext||{};t&&t(this.id),e&&(this.unmount=e(this.id))}unmount(){}}}},ma={x:!1,y:!1};function ga(){return ma.x||ma.y}function ya(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{passive:!0};return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}const va=e=>"mouse"===e.pointerType?"number"!==typeof e.button||e.button<=0:!1!==e.isPrimary;function ba(e){return{point:{x:e.pageX,y:e.pageY}}}function xa(e,t,n,r){return ya(e,t,(e=>t=>va(t)&&e(t,ba(t)))(n),r)}function wa(e){return e.max-e.min}function Sa(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.5;e.origin=r,e.originPoint=q(t.min,t.max,e.origin),e.scale=wa(n)/wa(t),e.translate=q(n.min,n.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function ka(e,t,n,r){Sa(e.x,t.x,n.x,r?r.originX:void 0),Sa(e.y,t.y,n.y,r?r.originY:void 0)}function Ea(e,t,n){e.min=n.min+t.min,e.max=e.min+wa(t)}function ja(e,t,n){e.min=t.min-n.min,e.max=e.min+wa(t)}function Ta(e,t,n){ja(e.x,t.x,n.x),ja(e.y,t.y,n.y)}function Ca(e){return[e("x"),e("y")]}const Pa=e=>{let{current:t}=e;return t?t.ownerDocument.defaultView:null},Na=(e,t)=>Math.abs(e-t);class Fa{constructor(e,t){let{transformPagePoint:n,contextWindow:r=window,dragSnapToOrigin:i=!1,distanceThreshold:a=3}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!this.lastMoveEvent||!this.lastMoveEventInfo)return;const e=Ra(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,n=function(e,t){const n=Na(e.x,t.x),r=Na(e.y,t.y);return Math.sqrt(n**2+r**2)}(e.offset,{x:0,y:0})>=this.distanceThreshold;if(!t&&!n)return;const{point:r}=e,{timestamp:i}=Oe;this.history.push(c(c({},r),{},{timestamp:i}));const{onStart:a,onMove:o}=this.handlers;t||(a&&a(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=_a(t,this.transformPagePoint),De.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();const{onEnd:n,onSessionEnd:r,resumeAnimation:i}=this.handlers;if(this.dragSnapToOrigin&&i&&i(),!this.lastMoveEvent||!this.lastMoveEventInfo)return;const a=Ra("pointercancel"===e.type?this.lastMoveEventInfo:_a(t,this.transformPagePoint),this.history);this.startEvent&&n&&n(e,a),r&&r(e,a)},!va(e))return;this.dragSnapToOrigin=i,this.handlers=t,this.transformPagePoint=n,this.distanceThreshold=a,this.contextWindow=r||window;const o=_a(ba(e),this.transformPagePoint),{point:s}=o,{timestamp:l}=Oe;this.history=[c(c({},s),{},{timestamp:l})];const{onSessionStart:u}=t;u&&u(e,Ra(o,this.history)),this.removeListeners=gr(xa(this.contextWindow,"pointermove",this.handlePointerMove),xa(this.contextWindow,"pointerup",this.handlePointerUp),xa(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),Me(this.updatePoint)}}function _a(e,t){return t?{point:t(e.point)}:e}function Aa(e,t){return{x:e.x-t.x,y:e.y-t.y}}function Ra(e,t){let{point:n}=e;return{point:n,delta:Aa(n,Ma(t)),offset:Aa(n,Da(t)),velocity:Oa(t,.1)}}function Da(e){return e[0]}function Ma(e){return e[e.length-1]}function Oa(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const i=Ma(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>yr(t)));)n--;if(!r)return{x:0,y:0};const a=vr(i.timestamp-r.timestamp);if(0===a)return{x:0,y:0};const o={x:(i.x-r.x)/a,y:(i.y-r.y)/a};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}function La(e,t,n){return{min:void 0!==t?e.min+t:void 0,max:void 0!==n?e.max+n-(e.max-e.min):void 0}}function za(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}const Va=.35;function Ia(e,t,n){return{min:Ua(e,t),max:Ua(e,n)}}function Ua(e,t){return"number"===typeof e?e:e[t]||0}const Ba=new WeakMap;class Wa{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic={x:{min:0,max:0},y:{min:0,max:0}},this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=e}start(e){let{snapToCursor:t=!1,distanceThreshold:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{presenceContext:r}=this.visualElement;if(r&&!1===r.isPresent)return;const{dragSnapToOrigin:i}=this.getProps();this.panSession=new Fa(e,{onSessionStart:e=>{const{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(ba(e).point)},onStart:(e,t)=>{const{drag:n,dragPropagation:r,onDragStart:i}=this.getProps();if(n&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===(a=n)||"y"===a?ma[a]?null:(ma[a]=!0,()=>{ma[a]=!1}):ma.x||ma.y?null:(ma.x=ma.y=!0,()=>{ma.x=ma.y=!1}),!this.openDragLock))return;var a;this.latestPointerEvent=e,this.latestPanInfo=t,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Ca(e=>{let t=this.getAxisMotionValue(e).get()||0;if(pe.test(t)){const{projection:n}=this.visualElement;if(n&&n.layout){const r=n.layout.layoutBox[e];if(r){t=wa(r)*(parseFloat(t)/100)}}}this.originPoint[e]=t}),i&&De.postRender(()=>i(e,t)),fr(this.visualElement,"transform");const{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(e,t)=>{this.latestPointerEvent=e,this.latestPanInfo=t;const{dragPropagation:n,dragDirectionLock:r,onDirectionLock:i,onDrag:a}=this.getProps();if(!n&&!this.openDragLock)return;const{offset:o}=t;if(r&&null===this.currentDirection)return this.currentDirection=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,n=null;Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x");return n}(o),void(null!==this.currentDirection&&i&&i(this.currentDirection));this.updateAxis("x",t.point,o),this.updateAxis("y",t.point,o),this.visualElement.render(),a&&a(e,t)},onSessionEnd:(e,t)=>{this.latestPointerEvent=e,this.latestPanInfo=t,this.stop(e,t),this.latestPointerEvent=null,this.latestPanInfo=null},resumeAnimation:()=>Ca(e=>{var t;return"paused"===this.getAnimationState(e)&&(null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,distanceThreshold:n,contextWindow:Pa(this.visualElement)})}stop(e,t){const n=e||this.latestPointerEvent,r=t||this.latestPanInfo,i=this.isDragging;if(this.cancel(),!i||!r||!n)return;const{velocity:a}=r;this.startAnimation(a);const{onDragEnd:o}=this.getProps();o&&De.postRender(()=>o(n,r))}cancel(){this.isDragging=!1;const{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,n){const{drag:r}=this.getProps();if(!n||!Ha(e,r,this.currentDirection))return;const i=this.getAxisMotionValue(e);let a=this.originPoint[e]+n[e];this.constraints&&this.constraints[e]&&(a=function(e,t,n){let{min:r,max:i}=t;return void 0!==r&&e<r?e=n?q(r,e,n.min):Math.max(e,r):void 0!==i&&e>i&&(e=n?q(i,e,n.max):Math.min(e,i)),e}(a,this.constraints[e],this.elastic[e])),i.set(a)}resolveConstraints(){var e;const{dragConstraints:t,dragElastic:n}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(e=this.visualElement.projection)||void 0===e?void 0:e.layout,i=this.constraints;t&&Qn(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):this.constraints=!(!t||!r)&&function(e,t){let{top:n,left:r,bottom:i,right:a}=t;return{x:La(e.x,r,a),y:La(e.y,n,i)}}(r.layoutBox,t),this.elastic=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Va;return!1===e?e=0:!0===e&&(e=Va),{x:Ia(e,"left","right"),y:Ia(e,"top","bottom")}}(n),i!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&Ca(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){const n={};return void 0!==t.min&&(n.min=t.min-e.min),void 0!==t.max&&(n.max=t.max-e.min),n}(r.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){const{dragConstraints:e,onMeasureDragConstraints:t}=this.getProps();if(!e||!Qn(e))return!1;const n=e.current,{projection:r}=this.visualElement;if(!r||!r.layout)return!1;const i=function(e,t,n){const r=oe(e,n),{scroll:i}=t;return i&&(re(r.x,i.offset.x),re(r.y,i.offset.y)),r}(n,r.root,this.visualElement.getTransformPagePoint());let a=function(e,t){return{x:za(e.x,t.x),y:za(e.y,t.y)}}(r.layout.layoutBox,i);if(t){const e=t(function(e){let{x:t,y:n}=e;return{top:n.min,right:t.max,bottom:n.max,left:t.min}}(a));this.hasMutatedConstraints=!!e,e&&(a=H(e))}return a}startAnimation(e){const{drag:t,dragMomentum:n,dragElastic:r,dragTransition:i,dragSnapToOrigin:a,onDragTransitionEnd:o}=this.getProps(),s=this.constraints||{},l=Ca(o=>{if(!Ha(o,t,this.currentDirection))return;let l=s&&s[o]||{};a&&(l={min:0,max:0});const u=r?200:1e6,d=r?40:1e7,f=c(c({type:"inertia",velocity:n?e[o]:0,bounceStiffness:u,bounceDamping:d,timeConstant:750,restDelta:1,restSpeed:10},i),l);return this.startAxisValueAnimation(o,f)});return Promise.all(l).then(o)}startAxisValueAnimation(e,t){const n=this.getAxisMotionValue(e);return fr(this.visualElement,e),n.start(Yi(e,n,0,t,this.visualElement,!1))}stopAnimation(){Ca(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){Ca(e=>{var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.pause()})}getAnimationState(e){var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.state}getAxisMotionValue(e){const t="_drag".concat(e.toUpperCase()),n=this.visualElement.getProps(),r=n[t];return r||this.visualElement.getValue(e,(n.initial?n.initial[e]:void 0)||0)}snapToCursor(e){Ca(t=>{const{drag:n}=this.getProps();if(!Ha(t,n,this.currentDirection))return;const{projection:r}=this.visualElement,i=this.getAxisMotionValue(t);if(r&&r.layout){const{min:n,max:a}=r.layout.layoutBox[t];i.set(e[t]-q(n,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:e,dragConstraints:t}=this.getProps(),{projection:n}=this.visualElement;if(!Qn(t)||!n||!this.constraints)return;this.stopAnimation();const r={x:0,y:0};Ca(e=>{const t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){const n=t.get();r[e]=function(e,t){let n=.5;const r=wa(e),i=wa(t);return i>r?n=di(t.min,t.max-r,e.min):r>i&&(n=di(e.min,e.max-i,t.min)),le(0,1,n)}({min:n,max:n},this.constraints[e])}});const{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),Ca(t=>{if(!Ha(t,e,null))return;const n=this.getAxisMotionValue(t),{min:i,max:a}=this.constraints[t];n.set(q(i,a,r[t]))})}addListeners(){if(!this.visualElement.current)return;Ba.set(this.visualElement,this);const e=xa(this.visualElement.current,"pointerdown",e=>{const{drag:t,dragListener:n=!0}=this.getProps();t&&n&&this.start(e)}),t=()=>{const{dragConstraints:e}=this.getProps();Qn(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,r=n.addEventListener("measure",t);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),De.read(t);const i=ya(window,"resize",()=>this.scalePositionWithinConstraints()),a=n.addEventListener("didUpdate",e=>{let{delta:t,hasLayoutChanged:n}=e;this.isDragging&&n&&(Ca(e=>{const n=this.getAxisMotionValue(e);n&&(this.originPoint[e]+=t[e].translate,n.set(n.get()+t[e].translate))}),this.visualElement.render())});return()=>{i(),e(),r(),a&&a()}}getProps(){const e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:n=!1,dragPropagation:r=!1,dragConstraints:i=!1,dragElastic:a=Va,dragMomentum:o=!0}=e;return c(c({},e),{},{drag:t,dragDirectionLock:n,dragPropagation:r,dragConstraints:i,dragElastic:a,dragMomentum:o})}}function Ha(e,t,n){return(!0===t||t===e)&&(null===n||n===e)}const qa=e=>(t,n)=>{e&&De.postRender(()=>e(t,n))};function Ka(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];const t=(0,i.useContext)(Un);if(null===t)return[!0,null];const{isPresent:n,onExitComplete:r,register:a}=t,o=(0,i.useId)();(0,i.useEffect)(()=>{if(e)return a(o)},[e]);const s=(0,i.useCallback)(()=>e&&r&&r(o),[o,r,e]);return!n&&r?[!1,s]:[!0]}const $a={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Xa(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const Ya={correct:(e,t)=>{if(!t.target)return e;if("string"===typeof e){if(!me.test(e))return e;e=parseFloat(e)}const n=Xa(e,t.target.x),r=Xa(e,t.target.y);return"".concat(n,"% ").concat(r,"%")}},Qa={correct:(e,t)=>{let{treeScale:n,projectionDelta:r}=t;const i=e,a=dt.parse(e);if(a.length>5)return i;const o=dt.createTransformer(e),s="number"!==typeof a[0]?1:0,l=r.x.scale*n.x,u=r.y.scale*n.y;a[0+s]/=l,a[1+s]/=u;const c=q(l,u,.5);return"number"===typeof a[2+s]&&(a[2+s]/=c),"number"===typeof a[3+s]&&(a[3+s]/=c),o(a)}};let Ga=!1;class Za extends i.Component{componentDidMount(){const{visualElement:e,layoutGroup:t,switchLayoutGroup:n,layoutId:r}=this.props,{projection:i}=e;!function(e){for(const t in e)ln[t]=e[t],I(t)&&(ln[t].isCSSVariable=!0)}(eo),i&&(t.group&&t.group.add(i),n&&n.register&&r&&n.register(i),Ga&&i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions(c(c({},i.options),{},{onExitComplete:()=>this.safeToRemove()}))),$a.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){const{layoutDependency:t,visualElement:n,drag:r,isPresent:i}=this.props,{projection:a}=n;return a?(a.isPresent=i,Ga=!0,r||e.layoutDependency!==t||void 0===t||e.isPresent!==i?a.willUpdate():this.safeToRemove(),e.isPresent!==i&&(i?a.promote():a.relegate()||De.postRender(()=>{const e=a.getStack();e&&e.members.length||this.safeToRemove()})),null):null}componentDidUpdate(){const{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),Mt.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:e,layoutGroup:t,switchLayoutGroup:n}=this.props,{projection:r}=e;Ga=!0,r&&(r.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(r),n&&n.deregister&&n.deregister(r))}safeToRemove(){const{safeToRemove:e}=this.props;e&&e()}render(){return null}}function Ja(e){const[t,n]=Ka(),r=(0,i.useContext)(En);return(0,j.jsx)(Za,c(c({},e),{},{layoutGroup:r,switchLayoutGroup:(0,i.useContext)(Jn),isPresent:t,safeToRemove:n}))}const eo={borderRadius:c(c({},Ya),{},{applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]}),borderTopLeftRadius:Ya,borderTopRightRadius:Ya,borderBottomLeftRadius:Ya,borderBottomRightRadius:Ya,boxShadow:Qa};function to(e){return"object"===typeof e&&null!==e}function no(e){return to(e)&&"ownerSVGElement"in e}const ro=(e,t)=>e.depth-t.depth;class io{constructor(){this.children=[],this.isDirty=!1}add(e){Ct(this.children,e),this.isDirty=!0}remove(e){Pt(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(ro),this.isDirty=!1,this.children.forEach(e)}}function ao(e,t){const n=Tt.now(),r=i=>{let{timestamp:a}=i;const o=a-n;o>=t&&(Me(r),e(o-t))};return De.setup(r,!0),()=>Me(r)}const oo=["TopLeft","TopRight","BottomLeft","BottomRight"],so=oo.length,lo=e=>"string"===typeof e?parseFloat(e):e,uo=e=>"number"===typeof e||me.test(e);function co(e,t){return void 0!==e[t]?e[t]:e.borderRadius}const fo=po(0,.5,oi),ho=po(.5,.95,Ne);function po(e,t,n){return r=>r<e?0:r>t?1:n(di(e,t,r))}function mo(e,t){e.min=t.min,e.max=t.max}function go(e,t){mo(e.x,t.x),mo(e.y,t.y)}function yo(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function vo(e,t,n,r,i){return e=G(e-=t,1/n,r),void 0!==i&&(e=G(e,1/i,r)),e}function bo(e,t,n,r,i){let[a,o,s]=n;!function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.5,i=arguments.length>4?arguments[4]:void 0,a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:e,o=arguments.length>6&&void 0!==arguments[6]?arguments[6]:e;pe.test(t)&&(t=parseFloat(t),t=q(o.min,o.max,t/100)-o.min);if("number"!==typeof t)return;let s=q(a.min,a.max,r);e===a&&(s-=t),e.min=vo(e.min,t,n,s,i),e.max=vo(e.max,t,n,s,i)}(e,t[a],t[o],t[s],t.scale,r,i)}const xo=["x","scaleX","originX"],wo=["y","scaleY","originY"];function So(e,t,n,r){bo(e.x,t,xo,n?n.x:void 0,r?r.x:void 0),bo(e.y,t,wo,n?n.y:void 0,r?r.y:void 0)}function ko(e){return 0===e.translate&&1===e.scale}function Eo(e){return ko(e.x)&&ko(e.y)}function jo(e,t){return e.min===t.min&&e.max===t.max}function To(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function Co(e,t){return To(e.x,t.x)&&To(e.y,t.y)}function Po(e){return wa(e.x)/wa(e.y)}function No(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class Fo{constructor(){this.members=[]}add(e){Ct(this.members,e),e.scheduleRender()}remove(e){if(Pt(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){const e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){const t=this.members.findIndex(t=>e===t);if(0===t)return!1;let n;for(let r=t;r>=0;r--){const e=this.members[r];if(!1!==e.isPresent){n=e;break}}return!!n&&(this.promote(n),!0)}promote(e,t){const n=this.lead;if(e!==n&&(this.prevLead=n,this.lead=e,e.show(),n)){n.instance&&n.scheduleRender(),e.scheduleRender(),e.resumeFrom=n,t&&(e.resumeFrom.preserveOpacity=!0),n.snapshot&&(e.snapshot=n.snapshot,e.snapshot.latestValues=n.animationValues||n.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);const{crossfade:r}=e.options;!1===r&&n.hide()}}exitAnimationComplete(){this.members.forEach(e=>{const{options:t,resumingFrom:n}=e;t.onExitComplete&&t.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}const _o={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},Ao=["","X","Y","Z"];let Ro=0;function Do(e,t,n,r){const{latestValues:i}=t;i[e]&&(n[e]=i[e],t.setStaticValue(e,0),r&&(r[e]=0))}function Mo(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;const{visualElement:t}=e.options;if(!t)return;const n=hr(t);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:t,layoutId:r}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",De,!(t||r))}const{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&Mo(r)}function Oo(e){let{attachResizeListener:t,defaultParent:n,measureScroll:r,checkIsScrollRoot:i,resetTransform:a}=e;return class{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===n||void 0===n?void 0:n();this.id=Ro++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,Ae.value&&(_o.nodes=_o.calculatedTargetDeltas=_o.calculatedProjections=0),this.nodes.forEach(Vo),this.nodes.forEach(Ko),this.nodes.forEach($o),this.nodes.forEach(Io),Ae.addProjectionMetrics&&Ae.addProjectionMetrics(_o)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=t?t.root||t:this,this.path=t?[...t.path,t]:[],this.parent=t,this.depth=t?t.depth+1:0;for(let n=0;n<this.path.length;n++)this.path[n].shouldResetTransform=!0;this.root===this&&(this.nodes=new io)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new Nt),this.eventHandlers.get(e).add(t)}notifyListeners(e){const t=this.eventHandlers.get(e);for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];t&&t.notify(...r)}hasListeners(e){return this.eventHandlers.has(e)}mount(e){if(this.instance)return;var n;this.isSVG=no(e)&&!(no(n=e)&&"svg"===n.tagName),this.instance=e;const{layoutId:r,layout:i,visualElement:a}=this.options;if(a&&!a.current&&a.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(i||r)&&(this.isLayoutDirty=!0),t){let n,r=0;const i=()=>this.root.updateBlockedByResize=!1;De.read(()=>{r=window.innerWidth}),t(e,()=>{const e=window.innerWidth;e!==r&&(r=e,this.root.updateBlockedByResize=!0,n&&n(),n=ao(i,250),$a.hasAnimatedSinceResize&&($a.hasAnimatedSinceResize=!1,this.nodes.forEach(qo)))})}r&&this.root.registerSharedNode(r,this),!1!==this.options.animate&&a&&(r||i)&&this.addEventListener("didUpdate",e=>{let{delta:t,hasLayoutChanged:n,hasRelativeLayoutChanged:r,layout:i}=e;if(this.isTreeAnimationBlocked())return this.target=void 0,void(this.relativeTarget=void 0);const o=this.options.transition||a.getDefaultTransition()||Jo,{onLayoutAnimationStart:s,onLayoutAnimationComplete:l}=a.getProps(),u=!this.targetLayout||!Co(this.targetLayout,i),d=!n&&r;if(this.options.layoutRoot||this.resumeFrom||d||n&&(u||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const e=c(c({},sr(o,"layout")),{},{onPlay:s,onComplete:l});(a.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,d)}else n||qo(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=i})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),Me(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Xo),this.animationId++)}getTransformTemplate(){const{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked())return void(this.options.onExitComplete&&this.options.onExitComplete());if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&Mo(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let i=0;i<this.path.length;i++){const e=this.path[i];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}const{layoutId:t,layout:n}=this.options;if(void 0===t&&!n)return;const r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){this.updateScheduled=!1;if(this.isUpdateBlocked())return this.unblockUpdate(),this.clearAllSnapshots(),void this.nodes.forEach(Bo);if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(Wo);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(Ho),this.nodes.forEach(Lo),this.nodes.forEach(zo)):this.nodes.forEach(Wo),this.clearAllSnapshots();const e=Tt.now();Oe.delta=le(0,1e3/60,e-Oe.timestamp),Oe.timestamp=e,Oe.isProcessing=!0,Le.update.process(Oe),Le.preRender.process(Oe),Le.render.process(Oe),Oe.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Mt.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(Uo),this.sharedNodes.forEach(Yo)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,De.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){De.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||wa(this.snapshot.measuredBox.x)||wa(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance)return;if(this.updateScroll(),(!this.options.alwaysMeasureLayout||!this.isLead())&&!this.isLayoutDirty)return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let n=0;n<this.path.length;n++){this.path[n].updateScroll()}const e=this.layout;this.layout=this.measure(!1),this.layoutCorrected={x:{min:0,max:0},y:{min:0,max:0}},this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"measure",t=Boolean(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&this.instance){const t=i(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:r(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!a)return;const e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!Eo(this.projectionDelta),n=this.getTransformTemplate(),r=n?n(this.latestValues,""):void 0,i=r!==this.prevTransformTemplateValue;e&&this.instance&&(t||X(this.latestValues)||i)&&(a(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];const t=this.measurePageBox();let n=this.removeElementScroll(t);var r;return e&&(n=this.removeTransform(n)),ns((r=n).x),ns(r.y),{animationId:this.root.animationId,measuredBox:t,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){var e;const{visualElement:t}=this.options;if(!t)return{x:{min:0,max:0},y:{min:0,max:0}};const n=t.measureViewportBox();if(!((null===(e=this.scroll)||void 0===e?void 0:e.wasRoot)||this.path.some(is))){const{scroll:e}=this.root;e&&(re(n.x,e.offset.x),re(n.y,e.offset.y))}return n}removeElementScroll(e){var t;const n={x:{min:0,max:0},y:{min:0,max:0}};if(go(n,e),null!==(t=this.scroll)&&void 0!==t&&t.wasRoot)return n;for(let r=0;r<this.path.length;r++){const t=this.path[r],{scroll:i,options:a}=t;t!==this.root&&i&&a.layoutScroll&&(i.wasRoot&&go(n,e),re(n.x,i.offset.x),re(n.y,i.offset.y))}return n}applyTransform(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const n={x:{min:0,max:0},y:{min:0,max:0}};go(n,e);for(let r=0;r<this.path.length;r++){const e=this.path[r];!t&&e.options.layoutScroll&&e.scroll&&e!==e.root&&ae(n,{x:-e.scroll.offset.x,y:-e.scroll.offset.y}),X(e.latestValues)&&ae(n,e.latestValues)}return X(this.latestValues)&&ae(n,this.latestValues),n}removeTransform(e){const t={x:{min:0,max:0},y:{min:0,max:0}};go(t,e);for(let n=0;n<this.path.length;n++){const e=this.path[n];if(!e.instance)continue;if(!X(e.latestValues))continue;$(e.latestValues)&&e.updateSnapshot();const r=Vt();go(r,e.measurePageBox()),So(t,e.latestValues,e.snapshot?e.snapshot.layoutBox:void 0,r)}return X(this.latestValues)&&So(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options=c(c(c({},this.options),e),{},{crossfade:void 0===e.crossfade||e.crossfade})}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==Oe.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(){var e;let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];const n=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=n.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=n.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=n.isSharedProjectionDirty);const r=Boolean(this.resumingFrom)||this!==n;if(!(t||r&&this.isSharedProjectionDirty||this.isProjectionDirty||null!==(e=this.parent)&&void 0!==e&&e.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:i,layoutId:a}=this.options;if(this.layout&&(i||a)){if(this.resolvedRelativeTargetAt=Oe.timestamp,!this.targetDelta&&!this.relativeTarget){const e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},Ta(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),go(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){var o,s,l;if(this.target||(this.target={x:{min:0,max:0},y:{min:0,max:0}},this.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}}),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),o=this.target,s=this.relativeTarget,l=this.relativeParent.target,Ea(o.x,s.x,l.x),Ea(o.y,s.y,l.y)):this.targetDelta?(Boolean(this.resumingFrom)?this.target=this.applyTransform(this.layout.layoutBox):go(this.target,this.layout.layoutBox),ee(this.target,this.targetDelta)):go(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const e=this.getClosestProjectingParent();e&&Boolean(e.resumingFrom)===Boolean(this.resumingFrom)&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},Ta(this.relativeTargetOrigin,this.target,e.target),go(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}Ae.value&&_o.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(this.parent&&!$(this.parent.latestValues)&&!Y(this.parent.latestValues))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return Boolean((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var e;const t=this.getLead(),n=Boolean(this.resumingFrom)||this!==t;let r=!0;if((this.isProjectionDirty||null!==(e=this.parent)&&void 0!==e&&e.isProjectionDirty)&&(r=!1),n&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(r=!1),this.resolvedRelativeTargetAt===Oe.timestamp&&(r=!1),r)return;const{layout:i,layoutId:a}=this.options;if(this.isTreeAnimating=Boolean(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!i&&!a)return;go(this.layoutCorrected,this.layout.layoutBox);const o=this.treeScale.x,s=this.treeScale.y;!function(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];const i=n.length;if(!i)return;let a,o;t.x=t.y=1;for(let s=0;s<i;s++){a=n[s],o=a.projectionDelta;const{visualElement:i}=a.options;i&&i.props.style&&"contents"===i.props.style.display||(r&&a.options.layoutScroll&&a.scroll&&a!==a.root&&ae(e,{x:-a.scroll.offset.x,y:-a.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,ee(e,o)),r&&X(a.latestValues)&&ae(e,a.latestValues))}t.x<ne&&t.x>te&&(t.x=1),t.y<ne&&t.y>te&&(t.y=1)}(this.layoutCorrected,this.treeScale,this.path,n),!t.layout||t.target||1===this.treeScale.x&&1===this.treeScale.y||(t.target=t.layout.layoutBox,t.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}});const{target:l}=t;l?(this.projectionDelta&&this.prevProjectionDelta?(yo(this.prevProjectionDelta.x,this.projectionDelta.x),yo(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),ka(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===o&&this.treeScale.y===s&&No(this.projectionDelta.x,this.prevProjectionDelta.x)&&No(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),Ae.value&&_o.calculatedProjections++):this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender())}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(){var e;let t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];if(null===(e=this.options.visualElement)||void 0===e||e.scheduleRender(),t){const e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDeltaWithTransform={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}}}setAnimationOrigin(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const n=this.snapshot,r=n?n.latestValues:{},i=c({},this.latestValues),a={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;const o={x:{min:0,max:0},y:{min:0,max:0}},s=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),l=this.getStack(),u=!l||l.members.length<=1,d=Boolean(s&&!u&&!0===this.options.crossfade&&!this.path.some(Zo));let f;this.animationProgress=0,this.mixTargetDelta=t=>{const n=t/1e3;var l,c,h,p,m,g;Qo(a.x,e.x,n),Qo(a.y,e.y,n),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Ta(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),h=this.relativeTarget,p=this.relativeTargetOrigin,m=o,g=n,Go(h.x,p.x,m.x,g),Go(h.y,p.y,m.y,g),f&&(l=this.relativeTarget,c=f,jo(l.x,c.x)&&jo(l.y,c.y))&&(this.isProjectionDirty=!1),f||(f={x:{min:0,max:0},y:{min:0,max:0}}),go(f,this.relativeTarget)),s&&(this.animationValues=i,function(e,t,n,r,i,a){var o,s;if(i)e.opacity=q(0,null!==(o=n.opacity)&&void 0!==o?o:1,fo(r)),e.opacityExit=q(null!==(s=t.opacity)&&void 0!==s?s:1,0,ho(r));else if(a){var l,u;e.opacity=q(null!==(l=t.opacity)&&void 0!==l?l:1,null!==(u=n.opacity)&&void 0!==u?u:1,r)}for(let c=0;c<so;c++){const i="border".concat(oo[c],"Radius");let a=co(t,i),o=co(n,i);void 0===a&&void 0===o||(a||(a=0),o||(o=0),0===a||0===o||uo(a)===uo(o)?(e[i]=Math.max(q(lo(a),lo(o),r),0),(pe.test(o)||pe.test(a))&&(e[i]+="%")):e[i]=o)}(t.rotate||n.rotate)&&(e.rotate=q(t.rotate||0,n.rotate||0,r))}(i,r,this.latestValues,n,d,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(e){var t,n;this.notifyListeners("animationStart"),null===(t=this.currentAnimation)||void 0===t||t.stop(),null===(n=this.resumingFrom)||void 0===n||null===(n=n.currentAnimation)||void 0===n||n.stop(),this.pendingAnimation&&(Me(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=De.update(()=>{$a.hasAnimatedSinceResize=!0,br.layout++,this.motionValue||(this.motionValue=Rt(0)),this.currentAnimation=function(e,t,n){const r=kt(e)?e:Rt(e);return r.start(Yi("",r,t,n)),r.animation}(this.motionValue,[0,1e3],c(c({},e),{},{velocity:0,isSync:!0,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onStop:()=>{br.layout--},onComplete:()=>{br.layout--,e.onComplete&&e.onComplete(),this.completeAnimation()}})),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const e=this.getLead();let{targetWithTransforms:t,target:n,layout:r,latestValues:i}=e;if(t&&n&&r){if(this!==e&&this.layout&&r&&rs(this.options.animationType,this.layout.layoutBox,r.layoutBox)){n=this.target||{x:{min:0,max:0},y:{min:0,max:0}};const t=wa(this.layout.layoutBox.x);n.x.min=e.target.x.min,n.x.max=n.x.min+t;const r=wa(this.layout.layoutBox.y);n.y.min=e.target.y.min,n.y.max=n.y.min+r}go(t,n),ae(t,i),ka(this.projectionDeltaWithTransform,this.layoutCorrected,t,i)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new Fo);this.sharedNodes.get(e).add(t);const n=t.options.initialPromotionConfig;t.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(t):void 0})}isLead(){const e=this.getStack();return!e||e.lead===this}getLead(){var e;const{layoutId:t}=this.options;return t&&(null===(e=this.getStack())||void 0===e?void 0:e.lead)||this}getPrevLead(){var e;const{layoutId:t}=this.options;return t?null===(e=this.getStack())||void 0===e?void 0:e.prevLead:void 0}getStack(){const{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote(){let{needsReset:e,transition:t,preserveFollowOpacity:n}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const r=this.getStack();r&&r.promote(this,n),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){const e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){const{visualElement:e}=this.options;if(!e)return;let t=!1;const{latestValues:n}=e;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(t=!0),!t)return;const r={};n.z&&Do("z",e,r,this.animationValues);for(let i=0;i<Ao.length;i++)Do("rotate".concat(Ao[i]),e,r,this.animationValues),Do("skew".concat(Ao[i]),e,r,this.animationValues);e.render();for(const i in r)e.setStaticValue(i,r[i]),this.animationValues&&(this.animationValues[i]=r[i]);e.scheduleRender()}applyProjectionStyles(e,t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return void(e.visibility="hidden");const n=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.visibility="",e.opacity="",e.pointerEvents=Wn(null===t||void 0===t?void 0:t.pointerEvents)||"",void(e.transform=n?n(this.latestValues,""):"none");const r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target)return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=Wn(null===t||void 0===t?void 0:t.pointerEvents)||""),void(this.hasProjected&&!X(this.latestValues)&&(e.transform=n?n({},""):"none",this.hasProjected=!1));e.visibility="";const i=r.animationValues||r.latestValues;this.applyTransformsToTarget();let a=function(e,t,n){let r="";const i=e.x.translate/t.x,a=e.y.translate/t.y,o=(null===n||void 0===n?void 0:n.z)||0;if((i||a||o)&&(r="translate3d(".concat(i,"px, ").concat(a,"px, ").concat(o,"px) ")),1===t.x&&1===t.y||(r+="scale(".concat(1/t.x,", ").concat(1/t.y,") ")),n){const{transformPerspective:e,rotate:t,rotateX:i,rotateY:a,skewX:o,skewY:s}=n;e&&(r="perspective(".concat(e,"px) ").concat(r)),t&&(r+="rotate(".concat(t,"deg) ")),i&&(r+="rotateX(".concat(i,"deg) ")),a&&(r+="rotateY(".concat(a,"deg) ")),o&&(r+="skewX(".concat(o,"deg) ")),s&&(r+="skewY(".concat(s,"deg) "))}const s=e.x.scale*t.x,l=e.y.scale*t.y;return 1===s&&1===l||(r+="scale(".concat(s,", ").concat(l,")")),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,i);n&&(a=n(i,a)),e.transform=a;const{x:o,y:s}=this.projectionDelta;var l,u;(e.transformOrigin="".concat(100*o.origin,"% ").concat(100*s.origin,"% 0"),r.animationValues)?e.opacity=r===this?null!==(l=null!==(u=i.opacity)&&void 0!==u?u:this.latestValues.opacity)&&void 0!==l?l:1:this.preserveOpacity?this.latestValues.opacity:i.opacityExit:e.opacity=r===this?void 0!==i.opacity?i.opacity:"":void 0!==i.opacityExit?i.opacityExit:0;for(const c in ln){if(void 0===i[c])continue;const{correct:t,applyTo:n,isCSSVariable:o}=ln[c],s="none"===a?i[c]:t(i[c],r);if(n){const t=n.length;for(let r=0;r<t;r++)e[n[r]]=s}else o?this.options.visualElement.renderState.vars[c]=s:e[c]=s}this.options.layoutId&&(e.pointerEvents=r===this?Wn(null===t||void 0===t?void 0:t.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>{var t;return null===(t=e.currentAnimation)||void 0===t?void 0:t.stop()}),this.root.nodes.forEach(Bo),this.root.sharedNodes.clear()}}}function Lo(e){e.updateLayout()}function zo(e){var t;const n=(null===(t=e.resumeFrom)||void 0===t?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:t,measuredBox:r}=e.layout,{animationType:i}=e.options,a=n.source!==e.layout.source;"size"===i?Ca(e=>{const r=a?n.measuredBox[e]:n.layoutBox[e],i=wa(r);r.min=t[e].min,r.max=r.min+i}):rs(i,n.layoutBox,t)&&Ca(r=>{const i=a?n.measuredBox[r]:n.layoutBox[r],o=wa(t[r]);i.max=i.min+o,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[r].max=e.relativeTarget[r].min+o)});const o={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};ka(o,t,n.layoutBox);const s={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};a?ka(s,e.applyTransform(r,!0),n.measuredBox):ka(s,t,n.layoutBox);const l=!Eo(o);let u=!1;if(!e.resumeFrom){const r=e.getClosestProjectingParent();if(r&&!r.resumeFrom){const{snapshot:i,layout:a}=r;if(i&&a){const o={x:{min:0,max:0},y:{min:0,max:0}};Ta(o,n.layoutBox,i.layoutBox);const s={x:{min:0,max:0},y:{min:0,max:0}};Ta(s,t,a.layoutBox),Co(o,s)||(u=!0),r.options.layoutRoot&&(e.relativeTarget=s,e.relativeTargetOrigin=o,e.relativeParent=r)}}}e.notifyListeners("didUpdate",{layout:t,snapshot:n,delta:s,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(e.isLead()){const{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function Vo(e){Ae.value&&_o.nodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=Boolean(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function Io(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function Uo(e){e.clearSnapshot()}function Bo(e){e.clearMeasurements()}function Wo(e){e.isLayoutDirty=!1}function Ho(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function qo(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function Ko(e){e.resolveTargetDelta()}function $o(e){e.calcProjection()}function Xo(e){e.resetSkewAndRotation()}function Yo(e){e.removeLeadSnapshot()}function Qo(e,t,n){e.translate=q(t.translate,0,n),e.scale=q(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function Go(e,t,n,r){e.min=q(t.min,n.min,r),e.max=q(t.max,n.max,r)}function Zo(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}const Jo={duration:.45,ease:[.4,0,.1,1]},es=e=>"undefined"!==typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),ts=es("applewebkit/")&&!es("chrome/")?Math.round:Ne;function ns(e){e.min=ts(e.min),e.max=ts(e.max)}function rs(e,t,n){return"position"===e||"preserve-aspect"===e&&(r=Po(t),i=Po(n),a=.2,!(Math.abs(r-i)<=a));var r,i,a}function is(e){var t;return e!==e.root&&(null===(t=e.scroll)||void 0===t?void 0:t.wasRoot)}const as=Oo({attachResizeListener:(e,t)=>ya(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),os={current:void 0},ss=Oo({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!os.current){const e=new as({});e.mount(window),e.setOptions({layoutScroll:!0}),os.current=e}return os.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>Boolean("fixed"===window.getComputedStyle(e).position)}),ls={pan:{Feature:class extends fa{constructor(){super(...arguments),this.removePointerDownListener=Ne}onPointerDown(e){this.session=new Fa(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Pa(this.node)})}createPanHandlers(){const{onPanSessionStart:e,onPanStart:t,onPan:n,onPanEnd:r}=this.node.getProps();return{onSessionStart:qa(e),onStart:qa(t),onMove:n,onEnd:(e,t)=>{delete this.session,r&&De.postRender(()=>r(e,t))}}}mount(){this.removePointerDownListener=xa(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},drag:{Feature:class extends fa{constructor(e){super(e),this.removeGroupControls=Ne,this.removeListeners=Ne,this.controls=new Wa(e)}mount(){const{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||Ne}unmount(){this.removeGroupControls(),this.removeListeners()}},ProjectionNode:ss,MeasureLayout:Ja}};function us(e,t){const n=function(e,t,n){if(e instanceof EventTarget)return[e];if("string"===typeof e){var r;let i=document;t&&(i=t.current);const a=null!==(r=null===n||void 0===n?void 0:n[e])&&void 0!==r?r:i.querySelectorAll(e);return a?Array.from(a):[]}return Array.from(e)}(e),r=new AbortController;return[n,c(c({passive:!0},t),{},{signal:r.signal}),()=>r.abort()]}function cs(e){return!("touch"===e.pointerType||ga())}function ds(e,t,n){const{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover","Start"===n);const i=r["onHover"+n];i&&De.postRender(()=>i(t,ba(t)))}function fs(e){return to(e)&&"offsetHeight"in e}const hs=(e,t)=>!!t&&(e===t||hs(e,t.parentElement)),ps=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);const ms=new WeakSet;function gs(e){return t=>{"Enter"===t.key&&e(t)}}function ys(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}function vs(e){return va(e)&&!ga()}function bs(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const[r,i,a]=us(e,n),o=e=>{const r=e.currentTarget;if(!vs(e))return;ms.add(r);const a=t(r,e),o=(e,t)=>{window.removeEventListener("pointerup",s),window.removeEventListener("pointercancel",l),ms.has(r)&&ms.delete(r),vs(e)&&"function"===typeof a&&a(e,{success:t})},s=e=>{o(e,r===window||r===document||n.useGlobalTarget||hs(r,e.target))},l=e=>{o(e,!1)};window.addEventListener("pointerup",s,i),window.addEventListener("pointercancel",l,i)};return r.forEach(e=>{var t;(n.useGlobalTarget?window:e).addEventListener("pointerdown",o,i),fs(e)&&(e.addEventListener("focus",e=>((e,t)=>{const n=e.currentTarget;if(!n)return;const r=gs(()=>{if(ms.has(n))return;ys(n,"down");const e=gs(()=>{ys(n,"up")});n.addEventListener("keyup",e,t),n.addEventListener("blur",()=>ys(n,"cancel"),t)});n.addEventListener("keydown",r,t),n.addEventListener("blur",()=>n.removeEventListener("keydown",r),t)})(e,i)),t=e,ps.has(t.tagName)||-1!==t.tabIndex||e.hasAttribute("tabindex")||(e.tabIndex=0))}),a}function xs(e,t,n){const{props:r}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap","Start"===n);const i=r["onTap"+("End"===n?"":n)];i&&De.postRender(()=>i(t,ba(t)))}const ws=["root"],Ss=new WeakMap,ks=new WeakMap,Es=e=>{const t=Ss.get(e.target);t&&t(e)},js=e=>{e.forEach(Es)};function Ts(e,t,n){const r=function(e){let{root:t}=e,n=d(e,ws);const r=t||document;ks.has(r)||ks.set(r,{});const i=ks.get(r),a=JSON.stringify(n);return i[a]||(i[a]=new IntersectionObserver(js,c({root:t},n))),i[a]}(t);return Ss.set(e,n),r.observe(e),()=>{Ss.delete(e),r.unobserve(e)}}const Cs={some:0,all:1};const Ps={inView:{Feature:class extends fa{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:e={}}=this.node.getProps(),{root:t,margin:n,amount:r="some",once:i}=e,a={root:t?t.current:void 0,rootMargin:n,threshold:"number"===typeof r?r:Cs[r]};return Ts(this.node.current,a,e=>{const{isIntersecting:t}=e;if(this.isInView===t)return;if(this.isInView=t,i&&!t&&this.hasEnteredView)return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);const{onViewportEnter:n,onViewportLeave:r}=this.node.getProps(),a=t?n:r;a&&a(e)})}mount(){this.startObserver()}update(){if("undefined"===typeof IntersectionObserver)return;const{props:e,prevProps:t}=this.node,n=["amount","margin","root"].some(function(e){let{viewport:t={}}=e,{viewport:n={}}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return e=>t[e]!==n[e]}(e,t));n&&this.startObserver()}unmount(){}}},tap:{Feature:class extends fa{mount(){const{current:e}=this.node;e&&(this.unmount=bs(e,(e,t)=>(xs(this.node,t,"Start"),(e,t)=>{let{success:n}=t;return xs(this.node,e,n?"End":"Cancel")}),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}},focus:{Feature:class extends fa{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=gr(ya(this.node.current,"focus",()=>this.onFocus()),ya(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}},hover:{Feature:class extends fa{mount(){const{current:e}=this.node;e&&(this.unmount=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const[r,i,a]=us(e,n),o=e=>{if(!cs(e))return;const{target:n}=e,r=t(n,e);if("function"!==typeof r||!n)return;const a=e=>{cs(e)&&(r(e),n.removeEventListener("pointerleave",a))};n.addEventListener("pointerleave",a,i)};return r.forEach(e=>{e.addEventListener("pointerenter",o,i)}),a}(e,(e,t)=>(ds(this.node,t,"Start"),e=>ds(this.node,e,"End"))))}unmount(){}}}},Ns={layout:{ProjectionNode:ss,MeasureLayout:Ja}},Fs=ar(c(c(c(c({},pa),Ps),ls),Ns),kn);function _s(e,t){if("function"===typeof e)return e(t);null!==e&&void 0!==e&&(e.current=t)}function As(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return i.useCallback(function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return e=>{let n=!1;const r=t.map(t=>{const r=_s(t,e);return n||"function"!==typeof r||(n=!0),r});if(n)return()=>{for(let e=0;e<r.length;e++){const n=r[e];"function"===typeof n?n():_s(t[e],null)}}}}(...t),t)}class Rs extends i.Component{getSnapshotBeforeUpdate(e){const t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){const e=t.offsetParent,n=fs(e)&&e.offsetWidth||0,r=this.props.sizeRef.current;r.height=t.offsetHeight||0,r.width=t.offsetWidth||0,r.top=t.offsetTop,r.left=t.offsetLeft,r.right=n-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function Ds(e){let{children:t,isPresent:n,anchorX:r,root:a}=e;const o=(0,i.useId)(),s=(0,i.useRef)(null),l=(0,i.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:u}=(0,i.useContext)(Tn),c=As(s,null===t||void 0===t?void 0:t.ref);return(0,i.useInsertionEffect)(()=>{const{width:e,height:t,top:i,left:c,right:d}=l.current;if(n||!s.current||!e||!t)return;const f="left"===r?"left: ".concat(c):"right: ".concat(d);s.current.dataset.motionPopId=o;const h=document.createElement("style");u&&(h.nonce=u);const p=null!==a&&void 0!==a?a:document.head;return p.appendChild(h),h.sheet&&h.sheet.insertRule('\n          [data-motion-pop-id="'.concat(o,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            ").concat(f,"px !important;\n            top: ").concat(i,"px !important;\n          }\n        ")),()=>{p.contains(h)&&p.removeChild(h)}},[n]),(0,j.jsx)(Rs,{isPresent:n,childRef:s,sizeRef:l,children:i.cloneElement(t,{ref:c})})}const Ms=e=>{let{children:t,initial:n,isPresent:r,onExitComplete:a,custom:o,presenceAffectsLayout:s,mode:l,anchorX:u,root:d}=e;const f=Bn(Os),h=(0,i.useId)();let p=!0,m=(0,i.useMemo)(()=>(p=!1,{id:h,initial:n,isPresent:r,custom:o,onExitComplete:e=>{f.set(e,!0);for(const t of f.values())if(!t)return;a&&a()},register:e=>(f.set(e,!1),()=>f.delete(e))}),[r,f,a]);return s&&p&&(m=c({},m)),(0,i.useMemo)(()=>{f.forEach((e,t)=>f.set(t,!1))},[r]),i.useEffect(()=>{!r&&!f.size&&a&&a()},[r]),"popLayout"===l&&(t=(0,j.jsx)(Ds,{isPresent:r,anchorX:u,root:d,children:t})),(0,j.jsx)(Un.Provider,{value:m,children:t})};function Os(){return new Map}const Ls=e=>e.key||"";function zs(e){const t=[];return i.Children.forEach(e,e=>{(0,i.isValidElement)(e)&&t.push(e)}),t}const Vs=e=>{let{children:t,custom:n,initial:r=!0,onExitComplete:a,presenceAffectsLayout:o=!0,mode:s="sync",propagate:l=!1,anchorX:u="left",root:c}=e;const[d,f]=Ka(l),h=(0,i.useMemo)(()=>zs(t),[t]),p=l&&!d?[]:h.map(Ls),m=(0,i.useRef)(!0),g=(0,i.useRef)(h),y=Bn(()=>new Map),[v,b]=(0,i.useState)(h),[x,w]=(0,i.useState)(h);er(()=>{m.current=!1,g.current=h;for(let e=0;e<x.length;e++){const t=Ls(x[e]);p.includes(t)?y.delete(t):!0!==y.get(t)&&y.set(t,!1)}},[x,p.length,p.join("-")]);const S=[];if(h!==v){let e=[...h];for(let t=0;t<x.length;t++){const n=x[t],r=Ls(n);p.includes(r)||(e.splice(t,0,n),S.push(n))}return"wait"===s&&S.length&&(e=S),w(zs(e)),b(h),null}const{forceRender:k}=(0,i.useContext)(En);return(0,j.jsx)(j.Fragment,{children:x.map(e=>{const t=Ls(e),i=!(l&&!d)&&(h===x||p.includes(t));return(0,j.jsx)(Ms,{isPresent:i,initial:!(m.current&&!r)&&void 0,custom:n,presenceAffectsLayout:o,mode:s,root:c,onExitComplete:i?void 0:()=>{if(!y.has(t))return;y.set(t,!0);let e=!0;y.forEach(t=>{t||(e=!1)}),e&&(null===k||void 0===k||k(),w(g.current),l&&(null===f||void 0===f||f()),a&&a())},anchorX:u,children:e},t)})})},Is=v("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]),Us=v("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]),Bs=v("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]),Ws=v("cloud",[["path",{d:"M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z",key:"p7xjir"}]]),Hs=v("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]),qs=v("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]),Ks=v("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]),$s=v("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]),Xs=v("thumbs-up",[["path",{d:"M7 10v12",key:"1qc93n"}],["path",{d:"M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2a3.13 3.13 0 0 1 3 3.88Z",key:"emmmcr"}]]),Ys=v("thumbs-down",[["path",{d:"M17 14V2",key:"8ymqnk"}],["path",{d:"M9 18.12 10 14H4.17a2 2 0 0 1-1.92-2.56l2.33-8A2 2 0 0 1 6.5 2H20a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-2.76a2 2 0 0 0-1.79 1.11L12 22a3.13 3.13 0 0 1-3-3.88Z",key:"m61m77"}]]),Qs=v("volume-x",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["line",{x1:"22",x2:"16",y1:"9",y2:"15",key:"1ewh16"}],["line",{x1:"16",x2:"22",y1:"9",y2:"15",key:"5ykzw1"}]]),Gs=v("volume-2",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["path",{d:"M16 9a5 5 0 0 1 0 6",key:"1q6k2b"}],["path",{d:"M19.364 18.364a9 9 0 0 0 0-12.728",key:"ijwkga"}]]),Zs=v("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),Js=e=>{let{message:t,showDownload:n,downloadUrl:r}=e;const a="user"===t.role,[o,s]=(0,i.useState)(!1),[l,u]=(0,i.useState)(!1),[d,f]=(0,i.useState)(!1),[h,p]=(0,i.useState)(!1),[m,g]=(0,i.useState)(""),[y,v]=(0,i.useState)(!1),b=(0,i.useRef)(null);(0,i.useEffect)(()=>()=>{d&&window.speechSynthesis.cancel()},[d]);const x=e=>{const t=[];let n={};return e.forEach(e=>{e.includes("\ud83d\udcf0")||e.includes("Title:")?(n.title&&(t.push(n),n={}),n.title=e.replace(/\ud83d\udcf0|Title:/g,"").trim()):e.includes("Source:")?n.source=e.replace("Source:","").trim():e.includes("Published:")?n.publishedAt=e.replace("Published:","").trim():e.includes("Description:")?n.description=e.replace("Description:","").trim():e.includes("URL:")&&(n.url=e.replace("URL:","").trim())}),n.title&&t.push(n),t.length>0?{articles:t}:null},w=e=>{const t={};return e.forEach(e=>{e.includes("\ud83c\udf24\ufe0f")||e.includes("Weather for")?t.city=e.replace(/\ud83c\udf24\ufe0f|Weather for|:/g,"").trim():e.includes("Temperature:")?t.temperature=e.replace("Temperature:","").trim():e.includes("Condition:")?t.condition=e.replace("Condition:","").trim():e.includes("Humidity:")?t.humidity=e.replace("Humidity:","").trim():e.includes("Wind:")&&(t.wind=e.replace("Wind:","").trim())}),Object.keys(t).length>0?t:null},S=e=>{const t={};return e.forEach(e=>{if(e.includes("\ud83d\udcb9")||e.includes("Stock:"))t.symbol=e.replace(/\ud83d\udcb9|Stock:|Price/g,"").trim();else if(e.includes("Current Price:")||e.includes("\u20b9")){const n=e.match(/\u20b9([\d,]+\.?\d*)/);n&&(t.price=n[1])}else e.includes("Change:")?t.change=e.replace("Change:","").trim():e.includes("Market Cap:")&&(t.marketCap=e.replace("Market Cap:","").trim())}),Object.keys(t).length>0?t:null},k=e=>{const t={};return e.forEach(e=>{e.includes("\ud83c\udfcf")||e.includes("Match:")?t.match=e.replace(/\ud83c\udfcf|Match:/g,"").trim():e.includes("Score:")?t.score=e.replace("Score:","").trim():e.includes("Status:")?t.status=e.replace("Status:","").trim():e.includes("Overs:")&&(t.overs=e.replace("Overs:","").trim())}),Object.keys(t).length>0?t:null},E=function(e){g(e),v(!0),setTimeout(()=>v(!1),3e3)},T=async e=>{try{const t=await fetch("/api/languages/detect",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({text:e})});if(t.ok){const e=await t.json(),n={hi:{name:"\u0939\u093f\u0902\u0926\u0940 (Hindi)",is_indian:!0},ta:{name:"\u0ba4\u0bae\u0bbf\u0bb4\u0bcd (Tamil)",is_indian:!0},te:{name:"\u0c24\u0c46\u0c32\u0c41\u0c17\u0c41 (Telugu)",is_indian:!0},ml:{name:"\u0d2e\u0d32\u0d2f\u0d3e\u0d33\u0d02 (Malayalam)",is_indian:!0},kn:{name:"\u0c95\u0ca8\u0ccd\u0ca8\u0ca1 (Kannada)",is_indian:!0},bn:{name:"\u09ac\u09be\u0982\u09b2\u09be (Bengali)",is_indian:!0},gu:{name:"\u0a97\u0ac1\u0a9c\u0ab0\u0abe\u0aa4\u0ac0 (Gujarati)",is_indian:!0},mr:{name:"\u092e\u0930\u093e\u0920\u0940 (Marathi)",is_indian:!0},pa:{name:"\u0a2a\u0a70\u0a1c\u0a3e\u0a2c\u0a40 (Punjabi)",is_indian:!0},or:{name:"\u0b13\u0b21\u0b3c\u0b3f\u0b06 (Odia)",is_indian:!0},as:{name:"\u0985\u09b8\u09ae\u09c0\u09af\u09bc\u09be (Assamese)",is_indian:!0},ur:{name:"\u0627\u0631\u062f\u0648 (Urdu)",is_indian:!0},en:{name:"English",is_indian:!1},ar:{name:"\u0627\u0644\u0639\u0631\u0628\u064a\u0629 (Arabic)",is_indian:!1},fa:{name:"\u0641\u0627\u0631\u0633\u06cc (Persian)",is_indian:!1},zh:{name:"\u4e2d\u6587 (Chinese)",is_indian:!1},ja:{name:"\u65e5\u672c\u8a9e (Japanese)",is_indian:!1},ko:{name:"\ud55c\uad6d\uc5b4 (Korean)",is_indian:!1},es:{name:"Espa\xf1ol (Spanish)",is_indian:!1},fr:{name:"Fran\xe7ais (French)",is_indian:!1},de:{name:"Deutsch (German)",is_indian:!1},it:{name:"Italiano (Italian)",is_indian:!1},pt:{name:"Portugu\xeas (Portuguese)",is_indian:!1},ru:{name:"\u0420\u0443\u0441\u0441\u043a\u0438\u0439 (Russian)",is_indian:!1}}[e.detected_language]||{name:"Unknown",is_indian:!1};return{detected_language:e.detected_language,language_name:n.name,is_supported:!0,is_indian_language:n.is_indian,can_generate_tts:!0,confidence:e.confidence||.8,detection_method:e.detection_method||"enhanced"}}console.warn("Enhanced language detection service unavailable, trying TTS service")}catch(a){console.error("Enhanced language detection failed:",a)}try{const t=await fetch("/api/tts/detect-language",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({text:e})});if(t.ok){return c(c({},await t.json()),{},{detection_method:"tts_service"})}console.warn("TTS language detection service unavailable, using fallback")}catch(a){console.error("TTS language detection failed:",a),"TypeError"===a.name&&a.message.includes("fetch")&&E("Cannot connect to language detection service")}const t={hi:/[\u0900-\u097F]/g,ta:/[\u0B80-\u0BFF]/g,te:/[\u0C00-\u0C7F]/g,ml:/[\u0D00-\u0D7F]/g,kn:/[\u0C80-\u0CFF]/g,bn:/[\u0980-\u09FF]/g,gu:/[\u0A80-\u0AFF]/g,mr:/[\u0900-\u097F]/g,pa:/[\u0A00-\u0A7F]/g,or:/[\u0B00-\u0B7F]/g,ar:/[\u0600-\u06FF]/g,fa:/[\u0600-\u06FF]/g,zh:/[\u4e00-\u9fff]/g,ja:/[\u3040-\u309f\u30a0-\u30ff\u4e00-\u9fff]/g,ko:/[\uac00-\ud7af]/g};let n="en",r="English",i=!1;for(const[o,s]of Object.entries(t))if(s.test(e)){n=o;const e={hi:{name:"\u0939\u093f\u0902\u0926\u0940 (Hindi)",is_indian:!0},ta:{name:"\u0ba4\u0bae\u0bbf\u0bb4\u0bcd (Tamil)",is_indian:!0},te:{name:"\u0c24\u0c46\u0c32\u0c41\u0c17\u0c41 (Telugu)",is_indian:!0},ml:{name:"\u0d2e\u0d32\u0d2f\u0d3e\u0d33\u0d02 (Malayalam)",is_indian:!0},kn:{name:"\u0c95\u0ca8\u0ccd\u0ca8\u0ca1 (Kannada)",is_indian:!0},bn:{name:"\u09ac\u09be\u0982\u09b2\u09be (Bengali)",is_indian:!0},gu:{name:"\u0a97\u0ac1\u0a9c\u0ab0\u0abe\u0aa4\u0ac0 (Gujarati)",is_indian:!0},mr:{name:"\u092e\u0930\u093e\u0920\u0940 (Marathi)",is_indian:!0},pa:{name:"\u0a2a\u0a70\u0a1c\u0a3e\u0a2c\u0a40 (Punjabi)",is_indian:!0},or:{name:"\u0b13\u0b21\u0b3c\u0b3f\u0b06 (Odia)",is_indian:!0},ar:{name:"\u0627\u0644\u0639\u0631\u0628\u064a\u0629 (Arabic)",is_indian:!1},fa:{name:"\u0641\u0627\u0631\u0633\u06cc (Persian)",is_indian:!1},zh:{name:"\u4e2d\u6587 (Chinese)",is_indian:!1},ja:{name:"\u65e5\u672c\u8a9e (Japanese)",is_indian:!1},ko:{name:"\ud55c\uad6d\uc5b4 (Korean)",is_indian:!1}}[o]||{name:"Unknown",is_indian:!1};r=e.name,i=e.is_indian;break}return{detected_language:n,language_name:r,is_supported:!0,is_indian_language:i,can_generate_tts:!0,confidence:.6,detection_method:"fallback_unicode"}},C=async(e,t)=>new Promise(n=>{try{const r=new SpeechSynthesisUtterance(e);r.lang=N(t.detected_language);const i=()=>{const e=window.speechSynthesis.getVoices();console.log("Available voices: ".concat(e.length,", Target language: ").concat(r.lang));let t=e.find(e=>e.lang===r.lang&&(e.name.includes("Natural")||e.name.includes("Enhanced")||e.name.includes("Premium")||e.name.includes("Neural")||e.name.includes("Wavenet")||!e.localService));if(t||(t=e.find(e=>e.lang===r.lang)),!t){const n=r.lang.split("-")[0];t=e.find(e=>e.lang.startsWith(n)&&(e.name.includes("Natural")||e.name.includes("Enhanced")||e.name.includes("Premium")||e.name.includes("Neural")||!e.localService))}if(!t){const n=r.lang.split("-")[0];t=e.find(e=>e.lang.startsWith(n))}t||(t=e.find(e=>e.lang.startsWith("en")&&(e.name.includes("Natural")||e.name.includes("Enhanced")||e.name.includes("Premium")||e.name.includes("Neural")||!e.localService))),t||(t=e.find(e=>e.lang.startsWith("en"))),!t&&e.length>0&&(t=e[0]),t?(r.voice=t,console.log("Selected voice: ".concat(t.name," (").concat(t.lang,")"))):console.warn("No suitable voice found, using default")};window.speechSynthesis.getVoices().length>0?i():window.speechSynthesis.onvoiceschanged=i,r.rate=.9,r.pitch=1,r.volume=1,r.onstart=()=>{f(!0),n(!0)},r.onend=()=>f(!1),r.onerror=e=>{console.error("Web Speech API error:",e.error),f(!1),n(!1)},b.current=r,window.speechSynthesis.speak(r),setTimeout(()=>{d||n(!1)},1e3)}catch(r){console.error("Web Speech API failed:",r),n(!1)}}),P=async(e,t)=>{try{const n=await fetch("http://localhost:5000/api/tts/generate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({text:e,language_code:t.detected_language})});if(n.ok){const e=await n.blob(),r=URL.createObjectURL(e),i=new Audio(r);return i.onplay=()=>{f(!0),E("Playing in ".concat(t.language_name))},i.onended=()=>{f(!1),URL.revokeObjectURL(r),E("Speech completed")},i.onerror=()=>(f(!1),URL.revokeObjectURL(r),E("Audio playback failed",!0),!1),b.current=i,await i.play(),!0}{const e=await n.json();return console.error("Backend TTS failed:",e.error),e.error.includes("not supported")?E("".concat(t.language_name," is not supported for text-to-speech"),!0):E("Text-to-speech service unavailable",!0),f(!1),!1}}catch(n){return console.error("Backend TTS request failed:",n),"TypeError"===n.name&&n.message.includes("fetch")?E("Cannot connect to text-to-speech service",!0):E("Text-to-speech failed",!0),f(!1),!1}},N=e=>({en:"en-US","en-us":"en-US","en-gb":"en-GB","en-au":"en-AU","en-in":"en-IN",hi:"hi-IN",bn:"bn-IN",gu:"gu-IN",kn:"kn-IN",ml:"ml-IN",mr:"mr-IN",ta:"ta-IN",te:"te-IN",ur:"ur-PK",pa:"pa-IN",or:"or-IN",as:"as-IN",ne:"ne-NP",si:"si-LK",fr:"fr-FR",es:"es-ES",de:"de-DE",it:"it-IT",pt:"pt-PT",ru:"ru-RU",nl:"nl-NL",sv:"sv-SE",da:"da-DK",no:"nb-NO",fi:"fi-FI",pl:"pl-PL",cs:"cs-CZ",sk:"sk-SK",hu:"hu-HU",ro:"ro-RO",bg:"bg-BG",hr:"hr-HR",sr:"sr-RS",sl:"sl-SI",et:"et-EE",lv:"lv-LV",lt:"lt-LT",el:"el-GR",ar:"ar-SA",fa:"fa-IR",he:"he-IL",tr:"tr-TR",sw:"sw-KE",am:"am-ET",zh:"zh-CN","zh-cn":"zh-CN","zh-tw":"zh-TW",ja:"ja-JP",ko:"ko-KR",th:"th-TH",vi:"vi-VN",id:"id-ID",ms:"ms-MY",tl:"tl-PH"}[e]||"en-US"),F=a?null:(e=>{const t=e.toLowerCase(),n={docx:["convert to word","convert to docx","make it word","word document","docx format"],pdf:["convert to pdf","make it pdf","pdf format","pdf document"],excel:["convert to excel","make it excel","excel format","xlsx format","spreadsheet"],csv:["convert to csv","csv format","comma separated"]},r=["download","save","export","get file"];for(const[i,a]of Object.entries(n))if(a.some(e=>t.includes(e))){return{format:i,wantsDownload:r.some(e=>t.includes(e))}}return null})(t.content),_=n||F&&F.wantsDownload;return(0,j.jsxs)(Fs.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3},className:"flex gap-4 ".concat(a?"justify-end":"justify-start"," mb-8"),children:[!a&&(0,j.jsx)(Fs.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.1,type:"spring",stiffness:200},className:"flex-shrink-0 w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg",children:(0,j.jsx)("span",{className:"text-white text-lg font-bold",children:"S"})}),(0,j.jsxs)("div",{className:"max-w-[80%] ".concat(a?"order-first":""),children:[a&&t.file&&(0,j.jsx)("div",{className:"mb-3",children:(0,j.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-xl p-3 flex items-center gap-3",children:"image"===t.file.type?(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)("img",{src:t.file.url,alt:t.file.name,className:"w-10 h-10 object-cover rounded-lg"}),(0,j.jsxs)("div",{className:"flex-1",children:[(0,j.jsx)("p",{className:"text-sm font-medium text-gray-800",children:t.file.name}),(0,j.jsx)("p",{className:"text-xs text-gray-500",children:"Image"})]})]}):(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)("div",{className:"w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center",children:(0,j.jsx)(Is,{className:"w-5 h-5 text-blue-600"})}),(0,j.jsxs)("div",{className:"flex-1",children:[(0,j.jsx)("p",{className:"text-sm font-medium text-gray-800",children:t.file.name}),(0,j.jsx)("p",{className:"text-xs text-gray-500",children:"Document"})]})]})})}),(0,j.jsxs)(Fs.div,{initial:{scale:.95},animate:{scale:1},transition:{delay:.2},className:"px-6 py-4 rounded-2xl shadow-sm ".concat(a?"bg-blue-600 text-white ml-auto max-w-[80%]":"bg-white text-gray-800 border border-gray-200 shadow-md hover:shadow-lg transition-shadow duration-200"),children:[(0,j.jsx)("div",{className:"text-sm leading-relaxed whitespace-pre-wrap",children:t.content}),!a&&(()=>{const e=(e=>{const t=e.toLowerCase();return e.includes("\ud83d\udcf0")||e.includes("News Update")||t.includes("breaking news")?"news":e.includes("\ud83c\udf24\ufe0f")||e.includes("Weather")||t.includes("temperature")||t.includes("forecast")?"weather":e.includes("\ud83d\udcb9")||e.includes("Stock")||t.includes("market price")||t.includes("\u20b9")?"stock":e.includes("\ud83c\udfcf")||e.includes("Cricket")||t.includes("match")||t.includes("score")?"cricket":null})(t.content),n=e?((e,t)=>{try{const n=e.split("\n");switch(t){case"news":return x(n);case"weather":return w(n);case"stock":return S(n);case"cricket":return k(n);default:return null}}catch(n){return console.warn("Failed to parse live data:",n),null}})(t.content,e):null;return n&&"news"===e&&n.articles?(0,j.jsxs)("div",{className:"mt-4 space-y-3",children:[(0,j.jsxs)("div",{className:"flex items-center gap-2 text-xs font-medium text-blue-600",children:[(0,j.jsx)(Us,{size:14}),(0,j.jsx)("span",{children:"Live News Updates"})]}),n.articles.slice(0,3).map((e,t)=>(0,j.jsxs)(Fs.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.1*t},className:"p-3 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,j.jsx)("h4",{className:"font-medium text-gray-800 text-sm mb-1",children:e.title}),e.description&&(0,j.jsx)("p",{className:"text-xs text-gray-600 mb-2",children:e.description}),(0,j.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-500",children:[(0,j.jsx)("span",{children:e.source}),e.url&&(0,j.jsxs)("a",{href:e.url,target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-1 text-blue-600 hover:text-blue-800",children:[(0,j.jsx)(Bs,{size:10}),"Read more"]})]})]},t))]}):n&&"weather"===e?(0,j.jsxs)("div",{className:"mt-4",children:[(0,j.jsxs)("div",{className:"flex items-center gap-2 text-xs font-medium text-blue-600 mb-3",children:[(0,j.jsx)(Ws,{size:14}),(0,j.jsx)("span",{children:"Weather Information"})]}),(0,j.jsx)("div",{className:"p-4 bg-gradient-to-br from-blue-50 to-cyan-50 border border-blue-200 rounded-lg",children:(0,j.jsxs)("div",{className:"grid grid-cols-2 gap-3 text-sm",children:[n.city&&(0,j.jsxs)("div",{children:[(0,j.jsx)("span",{className:"font-medium text-gray-700",children:"Location:"}),(0,j.jsx)("span",{className:"ml-2 text-gray-600",children:n.city})]}),n.temperature&&(0,j.jsxs)("div",{children:[(0,j.jsx)("span",{className:"font-medium text-gray-700",children:"Temperature:"}),(0,j.jsx)("span",{className:"ml-2 text-gray-600",children:n.temperature})]}),n.condition&&(0,j.jsxs)("div",{children:[(0,j.jsx)("span",{className:"font-medium text-gray-700",children:"Condition:"}),(0,j.jsx)("span",{className:"ml-2 text-gray-600",children:n.condition})]}),n.humidity&&(0,j.jsxs)("div",{children:[(0,j.jsx)("span",{className:"font-medium text-gray-700",children:"Humidity:"}),(0,j.jsx)("span",{className:"ml-2 text-gray-600",children:n.humidity})]}),n.wind&&(0,j.jsxs)("div",{className:"col-span-2",children:[(0,j.jsx)("span",{className:"font-medium text-gray-700",children:"Wind:"}),(0,j.jsx)("span",{className:"ml-2 text-gray-600",children:n.wind})]})]})})]}):n&&"stock"===e?(0,j.jsxs)("div",{className:"mt-4",children:[(0,j.jsxs)("div",{className:"flex items-center gap-2 text-xs font-medium text-green-600 mb-3",children:[(0,j.jsx)(Hs,{size:14}),(0,j.jsx)("span",{children:"Stock Information"})]}),(0,j.jsx)("div",{className:"p-4 bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200 rounded-lg",children:(0,j.jsxs)("div",{className:"space-y-2 text-sm",children:[n.symbol&&(0,j.jsxs)("div",{className:"flex justify-between",children:[(0,j.jsx)("span",{className:"font-medium text-gray-700",children:"Symbol:"}),(0,j.jsx)("span",{className:"text-gray-600",children:n.symbol})]}),n.price&&(0,j.jsxs)("div",{className:"flex justify-between",children:[(0,j.jsx)("span",{className:"font-medium text-gray-700",children:"Price:"}),(0,j.jsxs)("span",{className:"text-gray-600 font-mono",children:["\u20b9",n.price]})]}),n.change&&(0,j.jsxs)("div",{className:"flex justify-between",children:[(0,j.jsx)("span",{className:"font-medium text-gray-700",children:"Change:"}),(0,j.jsx)("span",{className:"font-mono ".concat(n.change.includes("+")?"text-green-600":"text-red-600"),children:n.change})]}),n.marketCap&&(0,j.jsxs)("div",{className:"flex justify-between",children:[(0,j.jsx)("span",{className:"font-medium text-gray-700",children:"Market Cap:"}),(0,j.jsx)("span",{className:"text-gray-600",children:n.marketCap})]})]})})]}):n&&"cricket"===e?(0,j.jsxs)("div",{className:"mt-4",children:[(0,j.jsxs)("div",{className:"flex items-center gap-2 text-xs font-medium text-orange-600 mb-3",children:[(0,j.jsx)(qs,{size:14}),(0,j.jsx)("span",{children:"Cricket Match"})]}),(0,j.jsx)("div",{className:"p-4 bg-gradient-to-br from-orange-50 to-yellow-50 border border-orange-200 rounded-lg",children:(0,j.jsxs)("div",{className:"space-y-2 text-sm",children:[n.match&&(0,j.jsxs)("div",{children:[(0,j.jsx)("span",{className:"font-medium text-gray-700",children:"Match:"}),(0,j.jsx)("span",{className:"ml-2 text-gray-600",children:n.match})]}),n.score&&(0,j.jsxs)("div",{children:[(0,j.jsx)("span",{className:"font-medium text-gray-700",children:"Score:"}),(0,j.jsx)("span",{className:"ml-2 text-gray-600 font-mono",children:n.score})]}),n.status&&(0,j.jsxs)("div",{children:[(0,j.jsx)("span",{className:"font-medium text-gray-700",children:"Status:"}),(0,j.jsx)("span",{className:"ml-2 text-gray-600",children:n.status})]}),n.overs&&(0,j.jsxs)("div",{children:[(0,j.jsx)("span",{className:"font-medium text-gray-700",children:"Overs:"}),(0,j.jsx)("span",{className:"ml-2 text-gray-600 font-mono",children:n.overs})]})]})})]}):null})(),!a&&_&&r&&(0,j.jsx)(Fs.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.3},className:"mt-4 pt-3 border-t border-gray-200",children:(0,j.jsxs)(Fs.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:async()=>{if(r)try{const e=await fetch(r,{method:"GET",credentials:"same-origin"});if(!e.ok)throw new Error("Download failed: ".concat(e.status," ").concat(e.statusText));const t=await e.blob(),n=window.URL.createObjectURL(t),i=document.createElement("a");i.href=n;const a=e.headers.get("content-disposition");let o="download";if(a){const e=a.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);e&&e[1]&&(o=e[1].replace(/['"]/g,""))}else r.includes("/api/convert_to_excel")||r.includes("format=xlsx")?o="download.xlsx":r.includes("/api/convert_to_pdf")||r.includes("format=pdf")?o="download.pdf":r.includes("/api/convert_to_docx")||r.includes("format=docx")?o="download.docx":r.includes("format=csv")?o="download.csv":r.includes("format=txt")?o="download.txt":r.includes("format=json")&&(o="download.json");i.download=o,document.body.appendChild(i),i.click(),document.body.removeChild(i),window.URL.revokeObjectURL(n),console.log("File downloaded successfully: ".concat(o))}catch(e){console.error("Download failed:",e),alert("Download failed: ".concat(e.message))}},className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white text-sm rounded-xl hover:bg-blue-700 transition-all duration-200 shadow-md hover:shadow-lg",children:[(0,j.jsx)(Ks,{className:"w-4 h-4"}),"Download"]})}),!a&&t.metadata&&(0,j.jsxs)("div",{className:"flex flex-wrap gap-1 mt-2",children:[t.metadata.used_agent&&(0,j.jsx)("span",{className:"px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full",children:"\ud83e\udd16 Agent"}),t.metadata.used_rag&&(0,j.jsx)("span",{className:"px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full",children:"\ud83d\udcda Knowledge"}),"image_comprehensive"===t.metadata.analysis_type&&(0,j.jsx)("span",{className:"px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full",children:"\ud83d\uddbc\ufe0f Deep Image Analysis"}),"file_comprehensive"===t.metadata.analysis_type&&(0,j.jsx)("span",{className:"px-2 py-1 bg-orange-100 text-orange-700 text-xs rounded-full",children:"\ud83d\udcc4 Deep File Analysis"}),t.metadata.analyzer_used&&(0,j.jsxs)("span",{className:"px-2 py-1 bg-indigo-100 text-indigo-700 text-xs rounded-full",children:["\ud83d\udd0d ",t.metadata.analyzer_used]}),t.metadata.file_info&&(0,j.jsxs)("span",{className:"px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full",children:["\ud83d\udcce ",t.metadata.file_info.name]}),t.metadata.has_live_data&&(0,j.jsx)("span",{className:"px-2 py-1 bg-red-100 text-red-700 text-xs rounded-full",children:"\ud83d\udd34 Live Data"}),t.metadata.live_data_type&&(0,j.jsxs)("span",{className:"px-2 py-1 bg-yellow-100 text-yellow-700 text-xs rounded-full",children:["news"===t.metadata.live_data_type&&"\ud83d\udcf0 News","weather"===t.metadata.live_data_type&&"\ud83c\udf24\ufe0f Weather","stock"===t.metadata.live_data_type&&"\ud83d\udcb9 Stocks","cricket"===t.metadata.live_data_type&&"\ud83c\udfcf Cricket"]}),t.metadata.live_data_source&&(0,j.jsxs)("span",{className:"px-2 py-1 bg-cyan-100 text-cyan-700 text-xs rounded-full",children:["\ud83d\udce1 ",t.metadata.live_data_source]})]})]}),(0,j.jsx)("div",{className:"text-xs text-gray-500 mt-1 ".concat(a?"text-right":"text-left"),children:new Date(t.timestamp).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}),!a&&(0,j.jsxs)(Fs.div,{initial:{opacity:0,y:5},animate:{opacity:1,y:0},transition:{delay:.4},className:"flex items-center gap-1 mt-2 relative",children:[(0,j.jsx)(Fs.button,{whileHover:{scale:1.1},whileTap:{scale:.95},onClick:async()=>{try{await navigator.clipboard.writeText(t.content),p(!0),setTimeout(()=>p(!1),2e3)}catch(e){console.error("Failed to copy text: ",e)}},className:"p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-all duration-200 cursor-pointer group relative",title:"Copy message",children:(0,j.jsx)($s,{className:"w-4 h-4"})}),(0,j.jsx)(Fs.button,{whileHover:{scale:1.1},whileTap:{scale:.95},onClick:()=>{s(!o),l&&u(!1)},className:"p-2 rounded-lg transition-all duration-200 cursor-pointer ".concat(o?"text-blue-600 bg-blue-50 hover:bg-blue-100":"text-gray-500 hover:text-blue-600 hover:bg-gray-100"),title:"Like message",children:(0,j.jsx)(Xs,{className:"w-4 h-4"})}),(0,j.jsx)(Fs.button,{whileHover:{scale:1.1},whileTap:{scale:.95},onClick:()=>{u(!l),o&&s(!1)},className:"p-2 rounded-lg transition-all duration-200 cursor-pointer ".concat(l?"text-red-600 bg-red-50 hover:bg-red-100":"text-gray-500 hover:text-red-600 hover:bg-gray-100"),title:"Dislike message",children:(0,j.jsx)(Ys,{className:"w-4 h-4"})}),(0,j.jsxs)("div",{className:"relative flex items-center",children:[(0,j.jsx)(Fs.button,{whileHover:{scale:1.1},whileTap:{scale:.95},onClick:async()=>{if(d)window.speechSynthesis.cancel(),b.current&&b.current.pause&&b.current.pause(),f(!1),E("Speech stopped");else try{f(!0),E("Detecting language...");const e=await T(t.content),n=e.confidence?" (".concat(Math.round(100*e.confidence),"% confidence)"):"";e.detection_method&&" via ".concat(e.detection_method);e.is_indian_language?E("\ud83c\uddee\ud83c\uddf3 Speaking in ".concat(e.language_name).concat(n,"...")):E("\ud83c\udf10 Speaking in ".concat(e.language_name).concat(n,"..."));if(!await C(t.content,e)){E("Using enhanced TTS for ".concat(e.language_name,"..."));await P(t.content,e)||(E("Text-to-speech is not available for this language",!0),f(!1))}}catch(e){console.error("TTS Error:",e),E("Failed to read text aloud",!0),f(!1)}},className:"p-2 rounded-lg transition-all duration-200 cursor-pointer ".concat(d?"text-green-600 bg-green-50 hover:bg-green-100":"text-gray-500 hover:text-green-600 hover:bg-gray-100"),title:d?"Stop reading":"Read aloud",children:d?(0,j.jsx)(Qs,{className:"w-4 h-4"}):(0,j.jsx)(Gs,{className:"w-4 h-4"})}),d&&(0,j.jsx)(Fs.div,{initial:{opacity:0,x:-10},animate:{opacity:1,x:0},exit:{opacity:0,x:-10},className:"ml-2 px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full font-medium",children:"\ud83d\udd0a Speaking"})]}),h&&(0,j.jsxs)(Fs.div,{initial:{opacity:0,scale:.8,y:-10},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.8,y:-10},className:"absolute left-0 top-12 bg-gray-800 text-white text-xs px-3 py-1 rounded-lg shadow-lg z-10",children:["Copied!",(0,j.jsx)("div",{className:"absolute -top-1 left-4 w-2 h-2 bg-gray-800 transform rotate-45"})]}),y&&(0,j.jsxs)(Fs.div,{initial:{opacity:0,scale:.8,y:-10},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.8,y:-10},className:"absolute left-0 top-12 text-white text-xs px-3 py-1 rounded-lg shadow-lg z-10 ".concat(m.includes("failed")||m.includes("not available")||m.includes("Cannot connect")?"bg-red-600":"bg-blue-600"),children:[m,(0,j.jsx)("div",{className:"absolute -top-1 left-4 w-2 h-2 transform rotate-45 ".concat(m.includes("failed")||m.includes("not available")||m.includes("Cannot connect")?"bg-red-600":"bg-blue-600")})]})]})]}),a&&(0,j.jsx)(Fs.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.1,type:"spring",stiffness:200},className:"flex-shrink-0 w-10 h-10 bg-gray-500 rounded-full flex items-center justify-center shadow-lg",children:(0,j.jsx)(Zs,{className:"w-5 h-5 text-white"})})]})};function el(e,t){return function(){return e.apply(t,arguments)}}const{toString:tl}=Object.prototype,{getPrototypeOf:nl}=Object,{iterator:rl,toStringTag:il}=Symbol,al=(ol=Object.create(null),e=>{const t=tl.call(e);return ol[t]||(ol[t]=t.slice(8,-1).toLowerCase())});var ol;const sl=e=>(e=e.toLowerCase(),t=>al(t)===e),ll=e=>t=>typeof t===e,{isArray:ul}=Array,cl=ll("undefined");function dl(e){return null!==e&&!cl(e)&&null!==e.constructor&&!cl(e.constructor)&&pl(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const fl=sl("ArrayBuffer");const hl=ll("string"),pl=ll("function"),ml=ll("number"),gl=e=>null!==e&&"object"===typeof e,yl=e=>{if("object"!==al(e))return!1;const t=nl(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(il in e)&&!(rl in e)},vl=sl("Date"),bl=sl("File"),xl=sl("Blob"),wl=sl("FileList"),Sl=sl("URLSearchParams"),[kl,El,jl,Tl]=["ReadableStream","Request","Response","Headers"].map(sl);function Cl(e,t){let n,r,{allOwnKeys:i=!1}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(null!==e&&"undefined"!==typeof e)if("object"!==typeof e&&(e=[e]),ul(e))for(n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else{if(dl(e))return;const r=i?Object.getOwnPropertyNames(e):Object.keys(e),a=r.length;let o;for(n=0;n<a;n++)o=r[n],t.call(null,e[o],o,e)}}function Pl(e,t){if(dl(e))return null;t=t.toLowerCase();const n=Object.keys(e);let r,i=n.length;for(;i-- >0;)if(r=n[i],t===r.toLowerCase())return r;return null}const Nl="undefined"!==typeof globalThis?globalThis:"undefined"!==typeof self?self:"undefined"!==typeof window?window:global,Fl=e=>!cl(e)&&e!==Nl;const _l=(Al="undefined"!==typeof Uint8Array&&nl(Uint8Array),e=>Al&&e instanceof Al);var Al;const Rl=sl("HTMLFormElement"),Dl=(e=>{let{hasOwnProperty:t}=e;return(e,n)=>t.call(e,n)})(Object.prototype),Ml=sl("RegExp"),Ol=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};Cl(n,(n,i)=>{let a;!1!==(a=t(n,i,e))&&(r[i]=a||n)}),Object.defineProperties(e,r)};const Ll=sl("AsyncFunction"),zl=((e,t)=>{return e?setImmediate:t?(n="axios@".concat(Math.random()),r=[],Nl.addEventListener("message",e=>{let{source:t,data:i}=e;t===Nl&&i===n&&r.length&&r.shift()()},!1),e=>{r.push(e),Nl.postMessage(n,"*")}):e=>setTimeout(e);var n,r})("function"===typeof setImmediate,pl(Nl.postMessage)),Vl="undefined"!==typeof queueMicrotask?queueMicrotask.bind(Nl):"undefined"!==typeof process&&process.nextTick||zl,Il={isArray:ul,isArrayBuffer:fl,isBuffer:dl,isFormData:e=>{let t;return e&&("function"===typeof FormData&&e instanceof FormData||pl(e.append)&&("formdata"===(t=al(e))||"object"===t&&pl(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return t="undefined"!==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&fl(e.buffer),t},isString:hl,isNumber:ml,isBoolean:e=>!0===e||!1===e,isObject:gl,isPlainObject:yl,isEmptyObject:e=>{if(!gl(e)||dl(e))return!1;try{return 0===Object.keys(e).length&&Object.getPrototypeOf(e)===Object.prototype}catch(t){return!1}},isReadableStream:kl,isRequest:El,isResponse:jl,isHeaders:Tl,isUndefined:cl,isDate:vl,isFile:bl,isBlob:xl,isRegExp:Ml,isFunction:pl,isStream:e=>gl(e)&&pl(e.pipe),isURLSearchParams:Sl,isTypedArray:_l,isFileList:wl,forEach:Cl,merge:function e(){const{caseless:t,skipUndefined:n}=Fl(this)&&this||{},r={},i=(i,a)=>{const o=t&&Pl(r,a)||a;yl(r[o])&&yl(i)?r[o]=e(r[o],i):yl(i)?r[o]=e({},i):ul(i)?r[o]=i.slice():n&&cl(i)||(r[o]=i)};for(let a=0,o=arguments.length;a<o;a++)arguments[a]&&Cl(arguments[a],i);return r},extend:function(e,t,n){let{allOwnKeys:r}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return Cl(t,(t,r)=>{n&&pl(t)?e[r]=el(t,n):e[r]=t},{allOwnKeys:r}),e},trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,t,n,r)=>{let i,a,o;const s={};if(t=t||{},null==e)return t;do{for(i=Object.getOwnPropertyNames(e),a=i.length;a-- >0;)o=i[a],r&&!r(o,e,t)||s[o]||(t[o]=e[o],s[o]=!0);e=!1!==n&&nl(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:al,kindOfTest:sl,endsWith:(e,t,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return-1!==r&&r===n},toArray:e=>{if(!e)return null;if(ul(e))return e;let t=e.length;if(!ml(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},forEachEntry:(e,t)=>{const n=(e&&e[rl]).call(e);let r;for(;(r=n.next())&&!r.done;){const n=r.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let n;const r=[];for(;null!==(n=e.exec(t));)r.push(n);return r},isHTMLForm:Rl,hasOwnProperty:Dl,hasOwnProp:Dl,reduceDescriptors:Ol,freezeMethods:e=>{Ol(e,(t,n)=>{if(pl(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=e[n];pl(r)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))})},toObjectSet:(e,t)=>{const n={},r=e=>{e.forEach(e=>{n[e]=!0})};return ul(e)?r(e):r(String(e).split(t)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(e,t,n){return t.toUpperCase()+n}),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e=+e)?e:t,findKey:Pl,global:Nl,isContextDefined:Fl,isSpecCompliantForm:function(e){return!!(e&&pl(e.append)&&"FormData"===e[il]&&e[rl])},toJSONObject:e=>{const t=new Array(10),n=(e,r)=>{if(gl(e)){if(t.indexOf(e)>=0)return;if(dl(e))return e;if(!("toJSON"in e)){t[r]=e;const i=ul(e)?[]:{};return Cl(e,(e,t)=>{const a=n(e,r+1);!cl(a)&&(i[t]=a)}),t[r]=void 0,i}}return e};return n(e,0)},isAsyncFn:Ll,isThenable:e=>e&&(gl(e)||pl(e))&&pl(e.then)&&pl(e.catch),setImmediate:zl,asap:Vl,isIterable:e=>null!=e&&pl(e[rl])};function Ul(e,t,n,r,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),i&&(this.response=i,this.status=i.status?i.status:null)}Il.inherits(Ul,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:Il.toJSONObject(this.config),code:this.code,status:this.status}}});const Bl=Ul.prototype,Wl={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Wl[e]={value:e}}),Object.defineProperties(Ul,Wl),Object.defineProperty(Bl,"isAxiosError",{value:!0}),Ul.from=(e,t,n,r,i,a)=>{const o=Object.create(Bl);Il.toFlatObject(e,o,function(e){return e!==Error.prototype},e=>"isAxiosError"!==e);const s=e&&e.message?e.message:"Error",l=null==t&&e?e.code:t;return Ul.call(o,s,l,n,r,i),e&&null==o.cause&&Object.defineProperty(o,"cause",{value:e,configurable:!0}),o.name=e&&e.name||"Error",a&&Object.assign(o,a),o};const Hl=Ul;function ql(e){return Il.isPlainObject(e)||Il.isArray(e)}function Kl(e){return Il.endsWith(e,"[]")?e.slice(0,-2):e}function $l(e,t,n){return e?e.concat(t).map(function(e,t){return e=Kl(e),!n&&t?"["+e+"]":e}).join(n?".":""):t}const Xl=Il.toFlatObject(Il,{},null,function(e){return/^is[A-Z]/.test(e)});const Yl=function(e,t,n){if(!Il.isObject(e))throw new TypeError("target must be an object");t=t||new FormData;const r=(n=Il.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(e,t){return!Il.isUndefined(t[e])})).metaTokens,i=n.visitor||u,a=n.dots,o=n.indexes,s=(n.Blob||"undefined"!==typeof Blob&&Blob)&&Il.isSpecCompliantForm(t);if(!Il.isFunction(i))throw new TypeError("visitor must be a function");function l(e){if(null===e)return"";if(Il.isDate(e))return e.toISOString();if(Il.isBoolean(e))return e.toString();if(!s&&Il.isBlob(e))throw new Hl("Blob is not supported. Use a Buffer instead.");return Il.isArrayBuffer(e)||Il.isTypedArray(e)?s&&"function"===typeof Blob?new Blob([e]):Buffer.from(e):e}function u(e,n,i){let s=e;if(e&&!i&&"object"===typeof e)if(Il.endsWith(n,"{}"))n=r?n:n.slice(0,-2),e=JSON.stringify(e);else if(Il.isArray(e)&&function(e){return Il.isArray(e)&&!e.some(ql)}(e)||(Il.isFileList(e)||Il.endsWith(n,"[]"))&&(s=Il.toArray(e)))return n=Kl(n),s.forEach(function(e,r){!Il.isUndefined(e)&&null!==e&&t.append(!0===o?$l([n],r,a):null===o?n:n+"[]",l(e))}),!1;return!!ql(e)||(t.append($l(i,n,a),l(e)),!1)}const c=[],d=Object.assign(Xl,{defaultVisitor:u,convertValue:l,isVisitable:ql});if(!Il.isObject(e))throw new TypeError("data must be an object");return function e(n,r){if(!Il.isUndefined(n)){if(-1!==c.indexOf(n))throw Error("Circular reference detected in "+r.join("."));c.push(n),Il.forEach(n,function(n,a){!0===(!(Il.isUndefined(n)||null===n)&&i.call(t,n,Il.isString(a)?a.trim():a,r,d))&&e(n,r?r.concat(a):[a])}),c.pop()}}(e),t};function Ql(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(e){return t[e]})}function Gl(e,t){this._pairs=[],e&&Yl(e,this,t)}const Zl=Gl.prototype;Zl.append=function(e,t){this._pairs.push([e,t])},Zl.toString=function(e){const t=e?function(t){return e.call(this,t,Ql)}:Ql;return this._pairs.map(function(e){return t(e[0])+"="+t(e[1])},"").join("&")};const Jl=Gl;function eu(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+")}function tu(e,t,n){if(!t)return e;const r=n&&n.encode||eu;Il.isFunction(n)&&(n={serialize:n});const i=n&&n.serialize;let a;if(a=i?i(t,n):Il.isURLSearchParams(t)?t.toString():new Jl(t,n).toString(r),a){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+a}return e}const nu=class{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){Il.forEach(this.handlers,function(t){null!==t&&e(t)})}},ru={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},iu={isBrowser:!0,classes:{URLSearchParams:"undefined"!==typeof URLSearchParams?URLSearchParams:Jl,FormData:"undefined"!==typeof FormData?FormData:null,Blob:"undefined"!==typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},au="undefined"!==typeof window&&"undefined"!==typeof document,ou="object"===typeof navigator&&navigator||void 0,su=au&&(!ou||["ReactNative","NativeScript","NS"].indexOf(ou.product)<0),lu="undefined"!==typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"===typeof self.importScripts,uu=au&&window.location.href||"http://localhost",cu=c(c({},r),iu);const du=function(e){function t(e,n,r,i){let a=e[i++];if("__proto__"===a)return!0;const o=Number.isFinite(+a),s=i>=e.length;if(a=!a&&Il.isArray(r)?r.length:a,s)return Il.hasOwnProp(r,a)?r[a]=[r[a],n]:r[a]=n,!o;r[a]&&Il.isObject(r[a])||(r[a]=[]);return t(e,n,r[a],i)&&Il.isArray(r[a])&&(r[a]=function(e){const t={},n=Object.keys(e);let r;const i=n.length;let a;for(r=0;r<i;r++)a=n[r],t[a]=e[a];return t}(r[a])),!o}if(Il.isFormData(e)&&Il.isFunction(e.entries)){const n={};return Il.forEachEntry(e,(e,r)=>{t(function(e){return Il.matchAll(/\w+|\[(\w*)]/g,e).map(e=>"[]"===e[0]?"":e[1]||e[0])}(e),r,n,0)}),n}return null};const fu={transitional:ru,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const n=t.getContentType()||"",r=n.indexOf("application/json")>-1,i=Il.isObject(e);i&&Il.isHTMLForm(e)&&(e=new FormData(e));if(Il.isFormData(e))return r?JSON.stringify(du(e)):e;if(Il.isArrayBuffer(e)||Il.isBuffer(e)||Il.isStream(e)||Il.isFile(e)||Il.isBlob(e)||Il.isReadableStream(e))return e;if(Il.isArrayBufferView(e))return e.buffer;if(Il.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let a;if(i){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(e,t){return Yl(e,new cu.classes.URLSearchParams,c({visitor:function(e,t,n,r){return cu.isNode&&Il.isBuffer(e)?(this.append(t,e.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},t))}(e,this.formSerializer).toString();if((a=Il.isFileList(e))||n.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return Yl(a?{"files[]":e}:e,t&&new t,this.formSerializer)}}return i||r?(t.setContentType("application/json",!1),function(e,t,n){if(Il.isString(e))try{return(t||JSON.parse)(e),Il.trim(e)}catch(r){if("SyntaxError"!==r.name)throw r}return(n||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const t=this.transitional||fu.transitional,n=t&&t.forcedJSONParsing,r="json"===this.responseType;if(Il.isResponse(e)||Il.isReadableStream(e))return e;if(e&&Il.isString(e)&&(n&&!this.responseType||r)){const n=!(t&&t.silentJSONParsing)&&r;try{return JSON.parse(e,this.parseReviver)}catch(i){if(n){if("SyntaxError"===i.name)throw Hl.from(i,Hl.ERR_BAD_RESPONSE,this,null,this.response);throw i}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:cu.classes.FormData,Blob:cu.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};Il.forEach(["delete","get","head","post","put","patch"],e=>{fu.headers[e]={}});const hu=fu,pu=Il.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),mu=Symbol("internals");function gu(e){return e&&String(e).trim().toLowerCase()}function yu(e){return!1===e||null==e?e:Il.isArray(e)?e.map(yu):String(e)}function vu(e,t,n,r,i){return Il.isFunction(r)?r.call(this,t,n):(i&&(t=n),Il.isString(t)?Il.isString(r)?-1!==t.indexOf(r):Il.isRegExp(r)?r.test(t):void 0:void 0)}class bu{constructor(e){e&&this.set(e)}set(e,t,n){const r=this;function i(e,t,n){const i=gu(t);if(!i)throw new Error("header name must be a non-empty string");const a=Il.findKey(r,i);(!a||void 0===r[a]||!0===n||void 0===n&&!1!==r[a])&&(r[a||t]=yu(e))}const a=(e,t)=>Il.forEach(e,(e,n)=>i(e,n,t));if(Il.isPlainObject(e)||e instanceof this.constructor)a(e,t);else if(Il.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim()))a((e=>{const t={};let n,r,i;return e&&e.split("\n").forEach(function(e){i=e.indexOf(":"),n=e.substring(0,i).trim().toLowerCase(),r=e.substring(i+1).trim(),!n||t[n]&&pu[n]||("set-cookie"===n?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t})(e),t);else if(Il.isObject(e)&&Il.isIterable(e)){let n,r,i={};for(const t of e){if(!Il.isArray(t))throw TypeError("Object iterator must return a key-value pair");i[r=t[0]]=(n=i[r])?Il.isArray(n)?[...n,t[1]]:[n,t[1]]:t[1]}a(i,t)}else null!=e&&i(t,e,n);return this}get(e,t){if(e=gu(e)){const n=Il.findKey(this,e);if(n){const e=this[n];if(!t)return e;if(!0===t)return function(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}(e);if(Il.isFunction(t))return t.call(this,e,n);if(Il.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=gu(e)){const n=Il.findKey(this,e);return!(!n||void 0===this[n]||t&&!vu(0,this[n],n,t))}return!1}delete(e,t){const n=this;let r=!1;function i(e){if(e=gu(e)){const i=Il.findKey(n,e);!i||t&&!vu(0,n[i],i,t)||(delete n[i],r=!0)}}return Il.isArray(e)?e.forEach(i):i(e),r}clear(e){const t=Object.keys(this);let n=t.length,r=!1;for(;n--;){const i=t[n];e&&!vu(0,this[i],i,e,!0)||(delete this[i],r=!0)}return r}normalize(e){const t=this,n={};return Il.forEach(this,(r,i)=>{const a=Il.findKey(n,i);if(a)return t[a]=yu(r),void delete t[i];const o=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,t,n)=>t.toUpperCase()+n)}(i):String(i).trim();o!==i&&delete t[i],t[o]=yu(r),n[o]=!0}),this}concat(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.constructor.concat(this,...t)}toJSON(e){const t=Object.create(null);return Il.forEach(this,(n,r)=>{null!=n&&!1!==n&&(t[r]=e&&Il.isArray(n)?n.join(", "):n)}),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(e=>{let[t,n]=e;return t+": "+n}).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e){const t=new this(e);for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];return r.forEach(e=>t.set(e)),t}static accessor(e){const t=(this[mu]=this[mu]={accessors:{}}).accessors,n=this.prototype;function r(e){const r=gu(e);t[r]||(!function(e,t){const n=Il.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(e,n,i){return this[r].call(this,t,e,n,i)},configurable:!0})})}(n,e),t[r]=!0)}return Il.isArray(e)?e.forEach(r):r(e),this}}bu.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),Il.reduceDescriptors(bu.prototype,(e,t)=>{let{value:n}=e,r=t[0].toUpperCase()+t.slice(1);return{get:()=>n,set(e){this[r]=e}}}),Il.freezeMethods(bu);const xu=bu;function wu(e,t){const n=this||hu,r=t||n,i=xu.from(r.headers);let a=r.data;return Il.forEach(e,function(e){a=e.call(n,a,i.normalize(),t?t.status:void 0)}),i.normalize(),a}function Su(e){return!(!e||!e.__CANCEL__)}function ku(e,t,n){Hl.call(this,null==e?"canceled":e,Hl.ERR_CANCELED,t,n),this.name="CanceledError"}Il.inherits(ku,Hl,{__CANCEL__:!0});const Eu=ku;function ju(e,t,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?t(new Hl("Request failed with status code "+n.status,[Hl.ERR_BAD_REQUEST,Hl.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}const Tu=function(e,t){e=e||10;const n=new Array(e),r=new Array(e);let i,a=0,o=0;return t=void 0!==t?t:1e3,function(s){const l=Date.now(),u=r[o];i||(i=l),n[a]=s,r[a]=l;let c=o,d=0;for(;c!==a;)d+=n[c++],c%=e;if(a=(a+1)%e,a===o&&(o=(o+1)%e),l-i<t)return;const f=u&&l-u;return f?Math.round(1e3*d/f):void 0}};const Cu=function(e,t){let n,r,i=0,a=1e3/t;const o=function(t){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Date.now();i=a,n=null,r&&(clearTimeout(r),r=null),e(...t)};return[function(){const e=Date.now(),t=e-i;for(var s=arguments.length,l=new Array(s),u=0;u<s;u++)l[u]=arguments[u];t>=a?o(l,e):(n=l,r||(r=setTimeout(()=>{r=null,o(n)},a-t)))},()=>n&&o(n)]},Pu=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3,r=0;const i=Tu(50,250);return Cu(n=>{const a=n.loaded,o=n.lengthComputable?n.total:void 0,s=a-r,l=i(s);r=a;e({loaded:a,total:o,progress:o?a/o:void 0,bytes:s,rate:l||void 0,estimated:l&&o&&a<=o?(o-a)/l:void 0,event:n,lengthComputable:null!=o,[t?"download":"upload"]:!0})},n)},Nu=(e,t)=>{const n=null!=e;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},Fu=e=>function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return Il.asap(()=>e(...n))},_u=cu.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,cu.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(cu.origin),cu.navigator&&/(msie|trident)/i.test(cu.navigator.userAgent)):()=>!0,Au=cu.hasStandardBrowserEnv?{write(e,t,n,r,i,a){const o=[e+"="+encodeURIComponent(t)];Il.isNumber(n)&&o.push("expires="+new Date(n).toGMTString()),Il.isString(r)&&o.push("path="+r),Il.isString(i)&&o.push("domain="+i),!0===a&&o.push("secure"),document.cookie=o.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function Ru(e,t,n){let r=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t);return e&&(r||0==n)?function(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t}const Du=e=>e instanceof xu?c({},e):e;function Mu(e,t){t=t||{};const n={};function r(e,t,n,r){return Il.isPlainObject(e)&&Il.isPlainObject(t)?Il.merge.call({caseless:r},e,t):Il.isPlainObject(t)?Il.merge({},t):Il.isArray(t)?t.slice():t}function i(e,t,n,i){return Il.isUndefined(t)?Il.isUndefined(e)?void 0:r(void 0,e,0,i):r(e,t,0,i)}function a(e,t){if(!Il.isUndefined(t))return r(void 0,t)}function o(e,t){return Il.isUndefined(t)?Il.isUndefined(e)?void 0:r(void 0,e):r(void 0,t)}function s(n,i,a){return a in t?r(n,i):a in e?r(void 0,n):void 0}const l={url:a,method:a,data:a,baseURL:o,transformRequest:o,transformResponse:o,paramsSerializer:o,timeout:o,timeoutMessage:o,withCredentials:o,withXSRFToken:o,adapter:o,responseType:o,xsrfCookieName:o,xsrfHeaderName:o,onUploadProgress:o,onDownloadProgress:o,decompress:o,maxContentLength:o,maxBodyLength:o,beforeRedirect:o,transport:o,httpAgent:o,httpsAgent:o,cancelToken:o,socketPath:o,responseEncoding:o,validateStatus:s,headers:(e,t,n)=>i(Du(e),Du(t),0,!0)};return Il.forEach(Object.keys(c(c({},e),t)),function(r){const a=l[r]||i,o=a(e[r],t[r],r);Il.isUndefined(o)&&a!==s||(n[r]=o)}),n}const Ou=e=>{const t=Mu({},e);let{data:n,withXSRFToken:r,xsrfHeaderName:i,xsrfCookieName:a,headers:o,auth:s}=t;if(t.headers=o=xu.from(o),t.url=tu(Ru(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),s&&o.set("Authorization","Basic "+btoa((s.username||"")+":"+(s.password?unescape(encodeURIComponent(s.password)):""))),Il.isFormData(n))if(cu.hasStandardBrowserEnv||cu.hasStandardBrowserWebWorkerEnv)o.setContentType(void 0);else if(Il.isFunction(n.getHeaders)){const e=n.getHeaders(),t=["content-type","content-length"];Object.entries(e).forEach(e=>{let[n,r]=e;t.includes(n.toLowerCase())&&o.set(n,r)})}if(cu.hasStandardBrowserEnv&&(r&&Il.isFunction(r)&&(r=r(t)),r||!1!==r&&_u(t.url))){const e=i&&a&&Au.read(a);e&&o.set(i,e)}return t},Lu="undefined"!==typeof XMLHttpRequest&&function(e){return new Promise(function(t,n){const r=Ou(e);let i=r.data;const a=xu.from(r.headers).normalize();let o,s,l,u,c,{responseType:d,onUploadProgress:f,onDownloadProgress:h}=r;function p(){u&&u(),c&&c(),r.cancelToken&&r.cancelToken.unsubscribe(o),r.signal&&r.signal.removeEventListener("abort",o)}let m=new XMLHttpRequest;function g(){if(!m)return;const r=xu.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders());ju(function(e){t(e),p()},function(e){n(e),p()},{data:d&&"text"!==d&&"json"!==d?m.response:m.responseText,status:m.status,statusText:m.statusText,headers:r,config:e,request:m}),m=null}m.open(r.method.toUpperCase(),r.url,!0),m.timeout=r.timeout,"onloadend"in m?m.onloadend=g:m.onreadystatechange=function(){m&&4===m.readyState&&(0!==m.status||m.responseURL&&0===m.responseURL.indexOf("file:"))&&setTimeout(g)},m.onabort=function(){m&&(n(new Hl("Request aborted",Hl.ECONNABORTED,e,m)),m=null)},m.onerror=function(t){const r=t&&t.message?t.message:"Network Error",i=new Hl(r,Hl.ERR_NETWORK,e,m);i.event=t||null,n(i),m=null},m.ontimeout=function(){let t=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const i=r.transitional||ru;r.timeoutErrorMessage&&(t=r.timeoutErrorMessage),n(new Hl(t,i.clarifyTimeoutError?Hl.ETIMEDOUT:Hl.ECONNABORTED,e,m)),m=null},void 0===i&&a.setContentType(null),"setRequestHeader"in m&&Il.forEach(a.toJSON(),function(e,t){m.setRequestHeader(t,e)}),Il.isUndefined(r.withCredentials)||(m.withCredentials=!!r.withCredentials),d&&"json"!==d&&(m.responseType=r.responseType),h&&([l,c]=Pu(h,!0),m.addEventListener("progress",l)),f&&m.upload&&([s,u]=Pu(f),m.upload.addEventListener("progress",s),m.upload.addEventListener("loadend",u)),(r.cancelToken||r.signal)&&(o=t=>{m&&(n(!t||t.type?new Eu(null,e,m):t),m.abort(),m=null)},r.cancelToken&&r.cancelToken.subscribe(o),r.signal&&(r.signal.aborted?o():r.signal.addEventListener("abort",o)));const y=function(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(r.url);y&&-1===cu.protocols.indexOf(y)?n(new Hl("Unsupported protocol "+y+":",Hl.ERR_BAD_REQUEST,e)):m.send(i||null)})},zu=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let n,r=new AbortController;const i=function(e){if(!n){n=!0,o();const t=e instanceof Error?e:this.reason;r.abort(t instanceof Hl?t:new Eu(t instanceof Error?t.message:t))}};let a=t&&setTimeout(()=>{a=null,i(new Hl("timeout ".concat(t," of ms exceeded"),Hl.ETIMEDOUT))},t);const o=()=>{e&&(a&&clearTimeout(a),a=null,e.forEach(e=>{e.unsubscribe?e.unsubscribe(i):e.removeEventListener("abort",i)}),e=null)};e.forEach(e=>e.addEventListener("abort",i));const{signal:s}=r;return s.unsubscribe=()=>Il.asap(o),s}};function Vu(e,t){this.v=e,this.k=t}function Iu(e){return function(){return new Uu(e.apply(this,arguments))}}function Uu(e){var t,n;function r(t,n){try{var a=e[t](n),o=a.value,s=o instanceof Vu;Promise.resolve(s?o.v:o).then(function(n){if(s){var l="return"===t?"return":"next";if(!o.k||n.done)return r(l,n);n=e[l](n).value}i(a.done?"return":"normal",n)},function(e){r("throw",e)})}catch(e){i("throw",e)}}function i(e,i){switch(e){case"return":t.resolve({value:i,done:!0});break;case"throw":t.reject(i);break;default:t.resolve({value:i,done:!1})}(t=t.next)?r(t.key,t.arg):n=null}this._invoke=function(e,i){return new Promise(function(a,o){var s={key:e,arg:i,resolve:a,reject:o,next:null};n?n=n.next=s:(t=n=s,r(e,i))})},"function"!=typeof e.return&&(this.return=void 0)}function Bu(e){return new Vu(e,0)}function Wu(e){var t={},n=!1;function r(t,r){return n=!0,r=new Promise(function(n){n(e[t](r))}),{done:!1,value:new Vu(r,1)}}return t["undefined"!=typeof Symbol&&Symbol.iterator||"@@iterator"]=function(){return this},t.next=function(e){return n?(n=!1,e):r("next",e)},"function"==typeof e.throw&&(t.throw=function(e){if(n)throw n=!1,e;return r("throw",e)}),"function"==typeof e.return&&(t.return=function(e){return n?(n=!1,e):r("return",e)}),t}function Hu(e){var t,n,r,i=2;for("undefined"!=typeof Symbol&&(n=Symbol.asyncIterator,r=Symbol.iterator);i--;){if(n&&null!=(t=e[n]))return t.call(e);if(r&&null!=(t=e[r]))return new qu(t.call(e));n="@@asyncIterator",r="@@iterator"}throw new TypeError("Object is not async iterable")}function qu(e){function t(e){if(Object(e)!==e)return Promise.reject(new TypeError(e+" is not an object."));var t=e.done;return Promise.resolve(e.value).then(function(e){return{value:e,done:t}})}return qu=function(e){this.s=e,this.n=e.next},qu.prototype={s:null,n:null,next:function(){return t(this.n.apply(this.s,arguments))},return:function(e){var n=this.s.return;return void 0===n?Promise.resolve({value:e,done:!0}):t(n.apply(this.s,arguments))},throw:function(e){var n=this.s.return;return void 0===n?Promise.reject(e):t(n.apply(this.s,arguments))}},new qu(e)}Uu.prototype["function"==typeof Symbol&&Symbol.asyncIterator||"@@asyncIterator"]=function(){return this},Uu.prototype.next=function(e){return this._invoke("next",e)},Uu.prototype.throw=function(e){return this._invoke("throw",e)},Uu.prototype.return=function(e){return this._invoke("return",e)};const Ku=function*(e,t){let n=e.byteLength;if(!t||n<t)return void(yield e);let r,i=0;for(;i<n;)r=i+t,yield e.slice(i,r),i=r},$u=function(){var e=Iu(function*(e,t){var n,r=!1,i=!1;try{for(var a,o=Hu(Xu(e));r=!(a=yield Bu(o.next())).done;r=!1){const e=a.value;yield*Wu(Hu(Ku(e,t)))}}catch(s){i=!0,n=s}finally{try{r&&null!=o.return&&(yield Bu(o.return()))}finally{if(i)throw n}}});return function(t,n){return e.apply(this,arguments)}}(),Xu=function(){var e=Iu(function*(e){if(e[Symbol.asyncIterator])return void(yield*Wu(Hu(e)));const t=e.getReader();try{for(;;){const{done:e,value:n}=yield Bu(t.read());if(e)break;yield n}}finally{yield Bu(t.cancel())}});return function(t){return e.apply(this,arguments)}}(),Yu=(e,t,n,r)=>{const i=$u(e,t);let a,o=0,s=e=>{a||(a=!0,r&&r(e))};return new ReadableStream({async pull(e){try{const{done:t,value:r}=await i.next();if(t)return s(),void e.close();let a=r.byteLength;if(n){let e=o+=a;n(e)}e.enqueue(new Uint8Array(r))}catch(t){throw s(t),t}},cancel:e=>(s(e),i.return())},{highWaterMark:2})},{isFunction:Qu}=Il,Gu=(e=>{let{Request:t,Response:n}=e;return{Request:t,Response:n}})(Il.global),{ReadableStream:Zu,TextEncoder:Ju}=Il.global,ec=function(e){try{for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return!!e(...n)}catch(i){return!1}},tc=e=>{e=Il.merge.call({skipUndefined:!0},Gu,e);const{fetch:t,Request:n,Response:r}=e,i=t?Qu(t):"function"===typeof fetch,a=Qu(n),o=Qu(r);if(!i)return!1;const s=i&&Qu(Zu),l=i&&("function"===typeof Ju?(u=new Ju,e=>u.encode(e)):async e=>new Uint8Array(await new n(e).arrayBuffer()));var u;const d=a&&s&&ec(()=>{let e=!1;const t=new n(cu.origin,{body:new Zu,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),f=o&&s&&ec(()=>Il.isReadableStream(new r("").body)),h={stream:f&&(e=>e.body)};i&&["text","arrayBuffer","blob","formData","stream"].forEach(e=>{!h[e]&&(h[e]=(t,n)=>{let r=t&&t[e];if(r)return r.call(t);throw new Hl("Response type '".concat(e,"' is not supported"),Hl.ERR_NOT_SUPPORT,n)})});const p=async(e,t)=>{const r=Il.toFiniteNumber(e.getContentLength());return null==r?(async e=>{if(null==e)return 0;if(Il.isBlob(e))return e.size;if(Il.isSpecCompliantForm(e)){const t=new n(cu.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return Il.isArrayBufferView(e)||Il.isArrayBuffer(e)?e.byteLength:(Il.isURLSearchParams(e)&&(e+=""),Il.isString(e)?(await l(e)).byteLength:void 0)})(t):r};return async e=>{let{url:i,method:o,data:s,signal:l,cancelToken:u,timeout:m,onDownloadProgress:g,onUploadProgress:y,responseType:v,headers:b,withCredentials:x="same-origin",fetchOptions:w}=Ou(e),S=t||fetch;v=v?(v+"").toLowerCase():"text";let k=zu([l,u&&u.toAbortSignal()],m),E=null;const j=k&&k.unsubscribe&&(()=>{k.unsubscribe()});let T;try{if(y&&d&&"get"!==o&&"head"!==o&&0!==(T=await p(b,s))){let e,t=new n(i,{method:"POST",body:s,duplex:"half"});if(Il.isFormData(s)&&(e=t.headers.get("content-type"))&&b.setContentType(e),t.body){const[e,n]=Nu(T,Pu(Fu(y)));s=Yu(t.body,65536,e,n)}}Il.isString(x)||(x=x?"include":"omit");const t=a&&"credentials"in n.prototype,l=c(c({},w),{},{signal:k,method:o.toUpperCase(),headers:b.normalize().toJSON(),body:s,duplex:"half",credentials:t?x:void 0});E=a&&new n(i,l);let u=await(a?S(E,w):S(i,l));const m=f&&("stream"===v||"response"===v);if(f&&(g||m&&j)){const e={};["status","statusText","headers"].forEach(t=>{e[t]=u[t]});const t=Il.toFiniteNumber(u.headers.get("content-length")),[n,i]=g&&Nu(t,Pu(Fu(g),!0))||[];u=new r(Yu(u.body,65536,n,()=>{i&&i(),j&&j()}),e)}v=v||"text";let C=await h[Il.findKey(h,v)||"text"](u,e);return!m&&j&&j(),await new Promise((t,n)=>{ju(t,n,{data:C,headers:xu.from(u.headers),status:u.status,statusText:u.statusText,config:e,request:E})})}catch(C){if(j&&j(),C&&"TypeError"===C.name&&/Load failed|fetch/i.test(C.message))throw Object.assign(new Hl("Network Error",Hl.ERR_NETWORK,e,E),{cause:C.cause||C});throw Hl.from(C,C&&C.code,e,E)}}},nc=new Map,rc=e=>{let t=e?e.env:{};const{fetch:n,Request:r,Response:i}=t,a=[r,i,n];let o,s,l=a.length,u=nc;for(;l--;)o=a[l],s=u.get(o),void 0===s&&u.set(o,s=l?new Map:tc(t)),u=s;return s},ic=(rc(),{http:null,xhr:Lu,fetch:{get:rc}});Il.forEach(ic,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(n){}Object.defineProperty(e,"adapterName",{value:t})}});const ac=e=>"- ".concat(e),oc=e=>Il.isFunction(e)||null===e||!1===e,sc=(e,t)=>{e=Il.isArray(e)?e:[e];const{length:n}=e;let r,i;const a={};for(let o=0;o<n;o++){let n;if(r=e[o],i=r,!oc(r)&&(i=ic[(n=String(r)).toLowerCase()],void 0===i))throw new Hl("Unknown adapter '".concat(n,"'"));if(i&&(Il.isFunction(i)||(i=i.get(t))))break;a[n||"#"+o]=i}if(!i){const e=Object.entries(a).map(e=>{let[t,n]=e;return"adapter ".concat(t," ")+(!1===n?"is not supported by the environment":"is not available in the build")});let t=n?e.length>1?"since :\n"+e.map(ac).join("\n"):" "+ac(e[0]):"as no adapter specified";throw new Hl("There is no suitable adapter to dispatch the request "+t,"ERR_NOT_SUPPORT")}return i};function lc(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Eu(null,e)}function uc(e){lc(e),e.headers=xu.from(e.headers),e.data=wu.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1);return sc(e.adapter||hu.adapter,e)(e).then(function(t){return lc(e),t.data=wu.call(e,e.transformResponse,t),t.headers=xu.from(t.headers),t},function(t){return Su(t)||(lc(e),t&&t.response&&(t.response.data=wu.call(e,e.transformResponse,t.response),t.response.headers=xu.from(t.response.headers))),Promise.reject(t)})}const cc="1.12.2",dc={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{dc[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}});const fc={};dc.transitional=function(e,t,n){function r(e,t){return"[Axios v"+cc+"] Transitional option '"+e+"'"+t+(n?". "+n:"")}return(n,i,a)=>{if(!1===e)throw new Hl(r(i," has been removed"+(t?" in "+t:"")),Hl.ERR_DEPRECATED);return t&&!fc[i]&&(fc[i]=!0,console.warn(r(i," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,i,a)}},dc.spelling=function(e){return(t,n)=>(console.warn("".concat(n," is likely a misspelling of ").concat(e)),!0)};const hc={assertOptions:function(e,t,n){if("object"!==typeof e)throw new Hl("options must be an object",Hl.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let i=r.length;for(;i-- >0;){const a=r[i],o=t[a];if(o){const t=e[a],n=void 0===t||o(t,a,e);if(!0!==n)throw new Hl("option "+a+" must be "+n,Hl.ERR_BAD_OPTION_VALUE);continue}if(!0!==n)throw new Hl("Unknown option "+a,Hl.ERR_BAD_OPTION)}},validators:dc},pc=hc.validators;class mc{constructor(e){this.defaults=e||{},this.interceptors={request:new nu,response:new nu}}async request(e,t){try{return await this._request(e,t)}catch(n){if(n instanceof Error){let e={};Error.captureStackTrace?Error.captureStackTrace(e):e=new Error;const t=e.stack?e.stack.replace(/^.+\n/,""):"";try{n.stack?t&&!String(n.stack).endsWith(t.replace(/^.+\n.+\n/,""))&&(n.stack+="\n"+t):n.stack=t}catch(r){}}throw n}}_request(e,t){"string"===typeof e?(t=t||{}).url=e:t=e||{},t=Mu(this.defaults,t);const{transitional:n,paramsSerializer:r,headers:i}=t;void 0!==n&&hc.assertOptions(n,{silentJSONParsing:pc.transitional(pc.boolean),forcedJSONParsing:pc.transitional(pc.boolean),clarifyTimeoutError:pc.transitional(pc.boolean)},!1),null!=r&&(Il.isFunction(r)?t.paramsSerializer={serialize:r}:hc.assertOptions(r,{encode:pc.function,serialize:pc.function},!0)),void 0!==t.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),hc.assertOptions(t,{baseUrl:pc.spelling("baseURL"),withXsrfToken:pc.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let a=i&&Il.merge(i.common,i[t.method]);i&&Il.forEach(["delete","get","head","post","put","patch","common"],e=>{delete i[e]}),t.headers=xu.concat(a,i);const o=[];let s=!0;this.interceptors.request.forEach(function(e){"function"===typeof e.runWhen&&!1===e.runWhen(t)||(s=s&&e.synchronous,o.unshift(e.fulfilled,e.rejected))});const l=[];let u;this.interceptors.response.forEach(function(e){l.push(e.fulfilled,e.rejected)});let c,d=0;if(!s){const e=[uc.bind(this),void 0];for(e.unshift(...o),e.push(...l),c=e.length,u=Promise.resolve(t);d<c;)u=u.then(e[d++],e[d++]);return u}c=o.length;let f=t;for(;d<c;){const e=o[d++],t=o[d++];try{f=e(f)}catch(h){t.call(this,h);break}}try{u=uc.call(this,f)}catch(h){return Promise.reject(h)}for(d=0,c=l.length;d<c;)u=u.then(l[d++],l[d++]);return u}getUri(e){return tu(Ru((e=Mu(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}}Il.forEach(["delete","get","head","options"],function(e){mc.prototype[e]=function(t,n){return this.request(Mu(n||{},{method:e,url:t,data:(n||{}).data}))}}),Il.forEach(["post","put","patch"],function(e){function t(t){return function(n,r,i){return this.request(Mu(i||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}mc.prototype[e]=t(),mc.prototype[e+"Form"]=t(!0)});const gc=mc;class yc{constructor(e){if("function"!==typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise(function(e){t=e});const n=this;this.promise.then(e=>{if(!n._listeners)return;let t=n._listeners.length;for(;t-- >0;)n._listeners[t](e);n._listeners=null}),this.promise.then=e=>{let t;const r=new Promise(e=>{n.subscribe(e),t=e}).then(e);return r.cancel=function(){n.unsubscribe(t)},r},e(function(e,r,i){n.reason||(n.reason=new Eu(e,r,i),t(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){const e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let e;return{token:new yc(function(t){e=t}),cancel:e}}}const vc=yc;const bc={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(bc).forEach(e=>{let[t,n]=e;bc[n]=t});const xc=bc;const wc=function e(t){const n=new gc(t),r=el(gc.prototype.request,n);return Il.extend(r,gc.prototype,n,{allOwnKeys:!0}),Il.extend(r,n,null,{allOwnKeys:!0}),r.create=function(n){return e(Mu(t,n))},r}(hu);wc.Axios=gc,wc.CanceledError=Eu,wc.CancelToken=vc,wc.isCancel=Su,wc.VERSION=cc,wc.toFormData=Yl,wc.AxiosError=Hl,wc.Cancel=wc.CanceledError,wc.all=function(e){return Promise.all(e)},wc.spread=function(e){return function(t){return e.apply(null,t)}},wc.isAxiosError=function(e){return Il.isObject(e)&&!0===e.isAxiosError},wc.mergeConfig=Mu,wc.AxiosHeaders=xu,wc.formToJSON=e=>du(Il.isHTMLForm(e)?new FormData(e):e),wc.getAdapter=sc,wc.HttpStatusCode=xc,wc.default=wc;const Sc=wc,kc=Sc.create({baseURL:"http://localhost:5000/api",headers:{"Content-Type":"application/json"}}),Ec=e=>kc.post("/chat",e),jc=e=>kc.post("/chat/agent",e),Tc=e=>kc.get("/conversations?user_id=".concat(e)),Cc=e=>kc.get("/conversation/".concat(e)),Pc=e=>kc.post("/auth/login",e),Nc=v("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),Fc=v("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),_c=v("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]),Ac=v("mic-off",[["path",{d:"M12 19v3",key:"npa21l"}],["path",{d:"M15 9.34V5a3 3 0 0 0-5.68-1.33",key:"1gzdoj"}],["path",{d:"M16.95 16.95A7 7 0 0 1 5 12v-2",key:"cqa7eg"}],["path",{d:"M18.89 13.23A7 7 0 0 0 19 12v-2",key:"16hl24"}],["path",{d:"m2 2 20 20",key:"1ooewy"}],["path",{d:"M9 9v3a3 3 0 0 0 5.12 2.12",key:"r2i35w"}]]),Rc=v("mic",[["path",{d:"M12 19v3",key:"npa21l"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["rect",{x:"9",y:"2",width:"6",height:"13",rx:"3",key:"s6n7sd"}]]),Dc=v("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]),Mc=e=>{let{conversationId:t,messages:n,onNewMessage:r,onConversationCreate:a,onConversationUpdate:o,user:s}=e;const[l,u]=(0,i.useState)(""),[c,d]=(0,i.useState)(!1),[f,h]=(0,i.useState)(!1),[p,m]=(0,i.useState)("knowledge"),[g,y]=(0,i.useState)(!1),v=(0,i.useRef)(null),[x,w]=(0,i.useState)(!1),[S,k]=(0,i.useState)(null),[E,T]=(0,i.useState)(null),[C,P]=(0,i.useState)(!1),[N,F]=(0,i.useState)(!1),_=(0,i.useRef)(null);(0,i.useEffect)(()=>{if(n.length>0){"assistant"!==n[n.length-1].role&&c||setTimeout(()=>function(){var e;let t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];null===(e=v.current)||void 0===e||e.scrollIntoView({behavior:t?"smooth":"auto"})}(),100)}},[n,c]),(0,i.useEffect)(()=>{const e=window.SpeechRecognition||window.webkitSpeechRecognition;if(e){F(!0);const t=new e;t.continuous=!0,t.interimResults=!0,t.lang=navigator.language||"en-US",t.maxAlternatives=1,"webkitSpeechRecognition"in window&&(t.webkitGrammar="");let n="",r=!1;t.onresult=e=>{let t="",r="";for(let n=e.resultIndex;n<e.results.length;n++){const i=e.results[n][0].transcript;e.results[n].isFinal?r+=i:t+=i}r&&(n+=r);u(n+t)},t.onend=()=>{C&&!r?(r=!0,setTimeout(()=>{try{C&&t.start()}catch(e){console.log("Could not restart recognition:",e),P(!1)}finally{r=!1}},100)):C||(u(n.trim()),n="")},t.onerror=e=>{switch(console.error("Speech recognition error:",e.error),e.error){case"no-speech":case"audio-capture":case"network":break;case"not-allowed":case"service-not-allowed":P(!1),alert("Microphone access denied. Please enable microphone permissions.");break;default:C&&setTimeout(()=>{try{t.start()}catch(e){P(!1)}},1e3)}},t.onstart=()=>{console.log("Voice recognition started"),C||(n="")},_.current=t}return()=>{_.current&&_.current.stop()}},[]);const A=e=>{const t=document.createElement("input");t.type="file",t.accept="image"===e?"image/*":".pdf,.doc,.docx,.txt,.csv,.json",t.onchange=t=>{const n=t.target.files[0];if(n)if(k(n),"image"===e&&n.type.startsWith("image/")){const e=new FileReader;e.onload=e=>{T({type:"image",url:e.target.result,name:n.name})},e.readAsDataURL(n)}else T({type:"document",name:n.name,size:n.size})},t.click(),w(!1)},R=async()=>{if(!l.trim()&&!S||c)return;const e=l.trim();u(""),d(!0),y(!0);const n=new FormData;n.append("user_id",s.id),t&&n.append("conversation_id",t),n.append("message",e||"Please analyze this file."),S&&n.append("file",S);const i={role:"user",content:e,timestamp:(new Date).toISOString(),file:E?{name:E.name,type:E.type,url:E.url}:null};r(i);const f=S;k(null),T(null);try{var h,m,g,v,b,x,w;let i;if(f){const t=f.name.split(".").pop().toLowerCase(),r=["jpg","jpeg","png","gif","webp","svg","bmp","tiff"].includes(t);let a="/api/upload";if(r?(a="/api/analyze/image",n.append("query",e||"Provide comprehensive image analysis including visual description, objects, text extraction, technical details, and contextual insights.")):(a="/api/analyze/file",n.append("query",e||"Provide comprehensive file analysis including content summary, structure, tone, quality assessment, and practical insights.")),n.append("analysis_mode","comprehensive"),i=await fetch("http://localhost:5000".concat(a),{method:"POST",body:n}),!i.ok){const e=await i.json();throw new Error(e.error||"Upload failed")}i=await i.json(),i.analysisMetadata={endpoint:a.replace("/api/",""),analysisType:r?"image_comprehensive":"file_comprehensive",fileName:f.name,fileSize:f.size,processingTime:Date.now()}}else{const n="knowledge"===p;i="agent"===p?await jc({user_id:s.id,conversation_id:t,message:e}):await Ec({user_id:s.id,conversation_id:t,message:e,use_rag:n}),i=i.data}const l={role:"assistant",content:i.response,timestamp:(new Date).toISOString(),metadata:{used_agent:i.used_agent,used_rag:i.used_rag,document_info:i.document_info,analysis_type:(null===(h=i.metadata)||void 0===h?void 0:h.analysis_type)||(null===(m=i.analysisMetadata)||void 0===m?void 0:m.analysisType),analyzer_used:null===(g=i.metadata)||void 0===g?void 0:g.analyzer_used,file_type:null===(v=i.metadata)||void 0===v?void 0:v.file_type,image_metadata:null===(b=i.metadata)||void 0===b?void 0:b.image_metadata,technical_analysis:null===(x=i.metadata)||void 0===x?void 0:x.technical_analysis,processing_endpoint:null===(w=i.analysisMetadata)||void 0===w?void 0:w.endpoint,file_info:i.analysisMetadata?{name:i.analysisMetadata.fileName,size:i.analysisMetadata.fileSize,processing_time:i.analysisMetadata.processingTime}:null,has_live_data:i.has_live_data||!1,live_data_type:i.live_data_type,live_data_source:i.live_data_source,live_data_timestamp:i.live_data_timestamp},downloadUrl:i.download_url};r(l),o&&o(),!t&&i.conversation_id&&a(i.conversation_id)}catch(j){console.error("Error sending message:",j);const e={role:"assistant",content:"Sorry, I encountered an error. Please try again.",timestamp:(new Date).toISOString()};r(e)}finally{d(!1),y(!1)}};return(0,j.jsxs)("div",{className:"flex-1 flex flex-col h-screen bg-gray-50",children:[(0,j.jsx)(Fs.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"border-b border-gray-200 p-6 bg-white/95 backdrop-blur-sm sticky top-0 z-10",children:(0,j.jsxs)("div",{className:"max-w-5xl mx-auto flex items-center justify-between",children:[(0,j.jsxs)("div",{className:"flex items-center gap-4",children:[(0,j.jsx)(Fs.div,{whileHover:{scale:1.05},className:"w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg",children:(0,j.jsx)("span",{className:"text-white text-xl font-bold",children:"S"})}),(0,j.jsx)("h1",{className:"text-2xl font-semibold text-gray-800",children:"Sozhaa Tech AI"})]}),(0,j.jsxs)("div",{className:"flex bg-gray-100 rounded-xl p-1 shadow-sm",children:[(0,j.jsx)("button",{onClick:()=>m("knowledge"),className:"px-6 py-2 rounded-lg text-sm font-medium transition-all duration-200 ".concat("knowledge"===p?"bg-white text-blue-600 shadow-md":"text-gray-600 hover:text-gray-800 hover:bg-gray-50"),children:"Knowledge"}),(0,j.jsx)("button",{onClick:()=>m("agent"),className:"px-6 py-2 rounded-lg text-sm font-medium transition-all duration-200 ".concat("agent"===p?"bg-white text-blue-600 shadow-md":"text-gray-600 hover:text-gray-800 hover:bg-gray-50"),children:"Agent"})]})]})}),(0,j.jsx)(Vs,{children:g&&(0,j.jsx)(Fs.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.9},className:"fixed top-24 left-1/2 transform -translate-x-1/2 z-50",children:(0,j.jsxs)("div",{className:"bg-white border border-gray-200 rounded-xl px-6 py-3 shadow-lg flex items-center gap-3",children:[(0,j.jsx)(Nc,{className:"w-5 h-5 text-blue-600 animate-spin"}),(0,j.jsx)("span",{className:"text-gray-700 font-medium",children:"Agent is working..."})]})})}),(0,j.jsx)("div",{className:"flex-1 overflow-y-auto px-6 py-8",children:(0,j.jsxs)("div",{className:"max-w-5xl mx-auto",children:[0===n.length?(0,j.jsxs)(Fs.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"flex flex-col items-center justify-center h-full text-center py-24",children:[(0,j.jsx)(Fs.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring",stiffness:200},className:"w-24 h-24 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mb-8 shadow-xl",children:(0,j.jsx)("span",{className:"text-white text-4xl font-bold",children:"S"})}),(0,j.jsx)(Fs.h2,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.4},className:"text-4xl font-semibold text-gray-800 mb-4",children:"Hello! I'm Sozhaa Tech AI"}),(0,j.jsx)(Fs.p,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.6},className:"text-gray-600 text-xl mb-12 max-w-lg",children:"How can I help you today? Ask me anything or upload a file to get started."}),(0,j.jsxs)(Fs.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.8},className:"grid grid-cols-1 md:grid-cols-2 gap-6 max-w-2xl",children:[(0,j.jsxs)(Fs.div,{whileHover:{scale:1.02,y:-2},className:"p-6 bg-white rounded-2xl border border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all duration-200 cursor-pointer",children:[(0,j.jsx)("h3",{className:"font-semibold text-gray-800 mb-2 text-lg",children:"\ud83d\udca1 Ask Questions"}),(0,j.jsx)("p",{className:"text-gray-600",children:"Get answers on any topic with AI-powered insights"})]}),(0,j.jsxs)(Fs.div,{whileHover:{scale:1.02,y:-2},className:"p-6 bg-white rounded-2xl border border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all duration-200 cursor-pointer",children:[(0,j.jsx)("h3",{className:"font-semibold text-gray-800 mb-2 text-lg",children:"\ud83d\udcc4 Analyze Files"}),(0,j.jsx)("p",{className:"text-gray-600",children:"Upload documents, images, and get detailed analysis"})]})]})]}):(0,j.jsx)(Fs.div,{initial:{opacity:0},animate:{opacity:1},className:"space-y-8",children:n.map((e,t)=>(0,j.jsx)(Fs.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*t},children:(0,j.jsx)(Js,{message:e,showDownload:!!e.downloadUrl,downloadUrl:e.downloadUrl})},t))}),(0,j.jsx)("div",{ref:v})]})}),E&&(0,j.jsx)("div",{className:"px-4 pb-2",children:(0,j.jsx)("div",{className:"max-w-3xl mx-auto",children:(0,j.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-xl p-4 flex items-center gap-3",children:["image"===E.type?(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)("img",{src:E.url,alt:E.name,className:"w-12 h-12 object-cover rounded-lg"}),(0,j.jsxs)("div",{className:"flex-1",children:[(0,j.jsx)("p",{className:"text-sm font-medium text-gray-800",children:E.name}),(0,j.jsx)("p",{className:"text-xs text-gray-500",children:"Image ready to analyze"})]})]}):(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center",children:(0,j.jsx)(Is,{className:"w-6 h-6 text-blue-600"})}),(0,j.jsxs)("div",{className:"flex-1",children:[(0,j.jsx)("p",{className:"text-sm font-medium text-gray-800",children:E.name}),(0,j.jsxs)("p",{className:"text-xs text-gray-500",children:[(E.size/1024).toFixed(1)," KB \u2022 Ready to analyze"]})]})]}),(0,j.jsx)("button",{onClick:()=>{k(null),T(null)},className:"p-1 hover:bg-gray-200 rounded-full transition-colors",children:(0,j.jsx)(Fc,{className:"w-4 h-4 text-gray-500"})})]})})}),(0,j.jsx)(Fs.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"border-t border-gray-100 p-6 bg-white/95 backdrop-blur-sm",children:(0,j.jsxs)("div",{className:"max-w-5xl mx-auto",children:[(0,j.jsx)("div",{className:"relative bg-white rounded-3xl border-2 border-gray-200 focus-within:border-blue-500 focus-within:shadow-xl transition-all duration-300 shadow-lg hover:shadow-xl",children:(0,j.jsxs)("div",{className:"flex items-end gap-3 p-5",children:[(0,j.jsxs)("div",{className:"relative",children:[(0,j.jsx)("button",{onClick:()=>w(!x),disabled:c,className:"p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-full transition-all duration-200 disabled:opacity-50",children:(0,j.jsx)(b,{className:"w-5 h-5"})}),x&&(0,j.jsxs)("div",{className:"absolute bottom-full left-0 mb-2 bg-white rounded-xl shadow-lg border border-gray-200 py-2 min-w-[160px] z-50",children:[(0,j.jsxs)("button",{onClick:()=>A("image"),className:"w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center gap-3 text-gray-700",children:[(0,j.jsx)(_c,{className:"w-4 h-4"}),"Upload Image"]}),(0,j.jsxs)("button",{onClick:()=>A("document"),className:"w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center gap-3 text-gray-700",children:[(0,j.jsx)(Is,{className:"w-4 h-4"}),"Upload File"]})]})]}),(0,j.jsx)("div",{className:"flex-1",children:(0,j.jsx)("textarea",{value:l,onChange:e=>{u(e.target.value),e.target.style.height="auto",e.target.style.height=Math.min(e.target.scrollHeight,200)+"px"},onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),R())},placeholder:"Message Sozhaa Tech AI...",rows:"1",disabled:c,className:"w-full bg-transparent border-none outline-none resize-none text-gray-800 placeholder-gray-400 text-lg leading-relaxed py-2",style:{minHeight:"48px",maxHeight:"200px",fontFamily:"system-ui, -apple-system, sans-serif",overflow:"hidden"}})}),N&&(0,j.jsxs)("div",{className:"relative",children:[(0,j.jsxs)("button",{onClick:()=>{if(N&&_.current)if(C){console.log("Stopping voice recognition"),P(!1);try{_.current.stop()}catch(e){console.error("Error stopping voice recognition:",e)}}else{console.log("Starting voice recognition");try{P(!0),_.current.start()}catch(e){console.error("Error starting voice recognition:",e),P(!1),"InvalidStateError"===e.name&&(console.log("Recognition already running, trying to stop and restart"),_.current.stop(),setTimeout(()=>{try{_.current.start(),P(!0)}catch(e){console.error("Failed to restart recognition:",e)}},100))}}else console.log("Voice not supported or recognition not available")},disabled:c,className:"p-2 rounded-full transition-all duration-200 relative ".concat(C?"text-white bg-red-600 hover:bg-red-700 shadow-lg":"text-gray-500 hover:text-blue-600 hover:bg-blue-50"," disabled:opacity-50"),title:C?"Stop recording":"Start voice input",children:[C?(0,j.jsx)(Ac,{className:"w-5 h-5"}):(0,j.jsx)(Rc,{className:"w-5 h-5"}),C&&(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-red-400 rounded-full animate-ping"}),(0,j.jsx)("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"})]})]}),C&&(0,j.jsx)("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-red-600 text-white text-xs rounded whitespace-nowrap animate-fadeIn",children:"\ud83c\udfa4 Recording... Click to stop"})]}),(0,j.jsx)("button",{onClick:R,disabled:c||!l.trim()&&!S,className:"p-2 rounded-full transition-all duration-200 ".concat(!l.trim()&&!S||c?"text-gray-400 bg-gray-200":"text-white bg-blue-600 hover:bg-blue-700"," disabled:opacity-50"),children:c?(0,j.jsx)("div",{className:"w-5 h-5 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin"}):(0,j.jsx)(Dc,{className:"w-5 h-5"})})]})}),(0,j.jsx)("div",{className:"flex items-center justify-center mt-4",children:(0,j.jsxs)("div",{className:"flex gap-1 bg-gray-100 rounded-full p-1",children:[(0,j.jsx)("button",{onClick:()=>m("knowledge"),className:"px-4 py-2 text-sm rounded-full transition-all duration-200 font-medium ".concat("knowledge"===p?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-800"),children:"\ud83d\udca1 Knowledge"}),(0,j.jsx)("button",{onClick:()=>m("agent"),className:"px-4 py-2 text-sm rounded-full transition-all duration-200 font-medium ".concat("agent"===p?"bg-white text-purple-600 shadow-sm":"text-gray-600 hover:text-gray-800"),children:"\ud83e\udd16 Agent"})]})}),(0,j.jsx)("div",{className:"text-center mt-3",children:(0,j.jsx)("p",{className:"text-xs text-gray-500",children:"Sozhaa Tech AI can make mistakes. Consider checking important information."})})]})})]})},Oc=e=>{let{onLogin:t}=e;const[n,r]=(0,i.useState)("deepak"),[a,o]=(0,i.useState)("<EMAIL>"),[s,l]=(0,i.useState)(!1);return(0,j.jsxs)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-b from-indigo-900 via-purple-900 to-indigo-700 relative overflow-hidden",children:[(0,j.jsxs)("div",{className:"absolute inset-0",children:[(0,j.jsx)("div",{className:"absolute top-20 left-10 w-4 h-4 bg-white/20 rounded-full animate-float-slow"}),(0,j.jsx)("div",{className:"absolute top-32 right-20 w-2 h-2 bg-pink-300/30 rounded-full animate-float-delayed"}),(0,j.jsx)("div",{className:"absolute top-60 right-10 w-6 h-6 bg-purple-300/20 rounded-full animate-float"}),(0,j.jsx)("div",{className:"absolute bottom-40 left-16 w-3 h-3 bg-blue-300/25 rounded-full animate-float-slow"}),(0,j.jsx)("div",{className:"absolute bottom-20 left-1/3 w-5 h-5 bg-white/15 rounded-full animate-float-delayed"}),(0,j.jsx)("div",{className:"absolute top-10 right-1/3 w-2 h-2 bg-indigo-300/30 rounded-full animate-float"}),(0,j.jsx)("div",{className:"absolute top-1/2 left-8 w-4 h-4 bg-pink-200/20 rounded-full animate-float-slow"}),(0,j.jsx)("div",{className:"absolute bottom-60 right-1/4 w-3 h-3 bg-purple-200/25 rounded-full animate-float"}),(0,j.jsx)("div",{className:"absolute top-16 left-1/4 w-1 h-1 bg-white/40 rounded-full animate-twinkle"}),(0,j.jsx)("div",{className:"absolute top-40 right-1/3 w-1 h-1 bg-white/50 rounded-full animate-twinkle-delayed"}),(0,j.jsx)("div",{className:"absolute bottom-32 left-1/2 w-1 h-1 bg-white/45 rounded-full animate-twinkle"}),(0,j.jsx)("div",{className:"absolute top-3/4 right-12 w-1 h-1 bg-white/35 rounded-full animate-twinkle-delayed"}),(0,j.jsx)("div",{className:"absolute top-1/3 left-12 w-1 h-1 bg-white/40 rounded-full animate-twinkle"}),(0,j.jsx)("div",{className:"absolute bottom-16 right-1/2 w-1 h-1 bg-white/50 rounded-full animate-twinkle-delayed"})]}),(0,j.jsx)("div",{className:"relative z-10 w-full max-w-md px-6",children:(0,j.jsxs)("div",{className:"bg-white/10 backdrop-blur-xl rounded-3xl p-8 shadow-2xl border border-white/20 ring-1 ring-white/10",children:[(0,j.jsxs)("div",{className:"text-center mb-8",children:[(0,j.jsxs)("div",{className:"flex items-center justify-center gap-4 mb-4",children:[(0,j.jsx)("div",{className:"text-5xl animate-pulse",children:"\ud83e\udd16"}),(0,j.jsxs)("h1",{className:"text-3xl font-bold text-white",children:["Sozhaa ",(0,j.jsx)("span",{className:"text-pink-400",children:"AI"})]})]}),(0,j.jsx)("p",{className:"text-indigo-200 text-lg font-medium",children:"Your Intelligent Conversation Partner"})]}),(0,j.jsxs)("form",{onSubmit:async e=>{if(e.preventDefault(),n.trim()&&a.trim()){l(!0);try{await t(n.trim(),a.trim())}catch(r){console.error("Login error:",r)}finally{l(!1)}}},className:"space-y-6",children:[(0,j.jsxs)("div",{children:[(0,j.jsxs)("label",{htmlFor:"username",className:"flex items-center gap-2 mb-2 text-indigo-200 font-semibold",children:[(0,j.jsx)("span",{className:"text-lg",children:"\ud83d\udc64"}),"Username"]}),(0,j.jsx)("input",{type:"text",id:"username",value:n,onChange:e=>r(e.target.value),placeholder:"Enter your username",required:!0,className:"w-full px-4 py-3 rounded-xl bg-white/20 border border-white/30 text-white placeholder-gray-300 focus:ring-2 focus:ring-pink-400 focus:border-pink-400 focus:outline-none transition-all duration-300"})]}),(0,j.jsxs)("div",{children:[(0,j.jsxs)("label",{htmlFor:"email",className:"flex items-center gap-2 mb-2 text-indigo-200 font-semibold",children:[(0,j.jsx)("span",{className:"text-lg",children:"\ud83d\udce7"}),"Email Address"]}),(0,j.jsx)("input",{type:"email",id:"email",value:a,onChange:e=>o(e.target.value),placeholder:"Enter your email address",required:!0,className:"w-full px-4 py-3 rounded-xl bg-white/20 border border-white/30 text-white placeholder-gray-300 focus:ring-2 focus:ring-pink-400 focus:border-pink-400 focus:outline-none transition-all duration-300"})]}),(0,j.jsx)("button",{type:"submit",disabled:s,className:"w-full py-4 bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-400 hover:to-purple-500 disabled:from-gray-400 disabled:to-gray-500 text-white font-bold rounded-xl transition-all duration-300 hover:scale-105 shadow-lg shadow-pink-500/30 hover:shadow-pink-500/50 disabled:hover:scale-100 disabled:hover:shadow-none flex items-center justify-center gap-3 text-lg",children:s?(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)("div",{className:"w-6 h-6 border-2 border-white/30 border-t-white rounded-full animate-spin"}),"Connecting..."]}):(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)("span",{className:"text-xl",children:"\ud83d\ude80"}),"Start Chatting"]})}),(0,j.jsxs)("div",{className:"text-center mt-6 pt-6 border-t border-white/20",children:[(0,j.jsx)("p",{className:"text-indigo-200 text-sm mb-3",children:"Try with demo credentials:"}),(0,j.jsx)("button",{type:"button",onClick:()=>{r("deepak"),o("<EMAIL>")},className:"px-6 py-2 border border-indigo-300/40 text-indigo-100 hover:bg-white/10 rounded-lg font-semibold transition-all duration-300 text-sm",children:"Use Demo Account"})]})]}),(0,j.jsxs)("div",{className:"grid grid-cols-3 gap-4 mt-8 pt-6 border-t border-white/20",children:[(0,j.jsxs)("div",{className:"text-center",children:[(0,j.jsx)("div",{className:"text-2xl mb-2",children:"\ud83d\udcac"}),(0,j.jsx)("span",{className:"text-xs text-indigo-200 font-medium",children:"Smart Conversations"})]}),(0,j.jsxs)("div",{className:"text-center",children:[(0,j.jsx)("div",{className:"text-2xl mb-2",children:"\ud83e\udde0"}),(0,j.jsx)("span",{className:"text-xs text-indigo-200 font-medium",children:"AI Powered"})]}),(0,j.jsxs)("div",{className:"text-center",children:[(0,j.jsx)("div",{className:"text-2xl mb-2",children:"\ud83d\udcda"}),(0,j.jsx)("span",{className:"text-xs text-indigo-200 font-medium",children:"Knowledge Base"})]})]})]})})]})},Lc=v("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]),zc=v("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]),Vc=e=>{let{user:t,onClose:n,onPreferencesUpdate:r}=e;const[a,o]=(0,i.useState)({conversation_tone:"friendly",response_style:"balanced",preferred_language:"en",custom_instructions:""}),[s,l]=(0,i.useState)({}),[u,d]=(0,i.useState)({}),[f,h]=(0,i.useState)(!0),[p,m]=(0,i.useState)(!1),[g,y]=(0,i.useState)(null),[v,b]=(0,i.useState)(!1);(0,i.useEffect)(()=>{x()},[t]);const x=async()=>{try{h(!0);const e=await Sc.get("http://localhost:5000/api/user/preferences?user_id=".concat(t.id));o(e.data.preferences),l(e.data.available_tones),d(e.data.available_styles),y(null)}catch(g){console.error("Error loading preferences:",g),y("Failed to load preferences")}finally{h(!1)}},w=(e,t)=>{o(n=>c(c({},n),{},{[e]:t}))};return f?(0,j.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,j.jsx)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:(0,j.jsxs)("div",{className:"flex items-center justify-center",children:[(0,j.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"}),(0,j.jsx)("span",{className:"ml-3 text-gray-600",children:"Loading preferences..."})]})})}):(0,j.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,j.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,j.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,j.jsxs)("div",{className:"flex items-center gap-3",children:[(0,j.jsx)(k,{className:"text-primary-600",size:24}),(0,j.jsx)("h2",{className:"text-xl font-bold text-gray-800",children:"User Preferences"})]}),(0,j.jsx)("button",{onClick:n,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,j.jsx)(Fc,{size:24})})]}),(0,j.jsxs)("div",{className:"p-6 space-y-6",children:[(0,j.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,j.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,j.jsx)(Zs,{className:"text-gray-600",size:20}),(0,j.jsx)("h3",{className:"font-medium text-gray-800",children:"User Information"})]}),(0,j.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,j.jsxs)("p",{children:[(0,j.jsx)("strong",{children:"Username:"})," ",t.username]}),(0,j.jsxs)("p",{children:[(0,j.jsx)("strong",{children:"Email:"})," ",t.email]})]})]}),(0,j.jsxs)("div",{children:[(0,j.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Conversation Tone"}),(0,j.jsx)("select",{value:a.conversation_tone,onChange:e=>w("conversation_tone",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",children:Object.entries(s).map(e=>{let[t,n]=e;return(0,j.jsxs)("option",{value:t,children:[n.name," - ",n.description]},t)})})]}),(0,j.jsxs)("div",{children:[(0,j.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Response Style"}),(0,j.jsx)("select",{value:a.response_style,onChange:e=>w("response_style",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",children:Object.entries(u).map(e=>{let[t,n]=e;return(0,j.jsxs)("option",{value:t,children:[n.name," - ",n.description]},t)})})]}),(0,j.jsxs)("div",{children:[(0,j.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Preferred Language"}),(0,j.jsxs)("select",{value:a.preferred_language,onChange:e=>w("preferred_language",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",children:[(0,j.jsx)("option",{value:"en",children:"English"}),(0,j.jsx)("option",{value:"es",children:"Spanish"}),(0,j.jsx)("option",{value:"fr",children:"French"}),(0,j.jsx)("option",{value:"de",children:"German"}),(0,j.jsx)("option",{value:"it",children:"Italian"}),(0,j.jsx)("option",{value:"pt",children:"Portuguese"}),(0,j.jsx)("option",{value:"ru",children:"Russian"}),(0,j.jsx)("option",{value:"ja",children:"Japanese"}),(0,j.jsx)("option",{value:"ko",children:"Korean"}),(0,j.jsx)("option",{value:"zh",children:"Chinese"}),(0,j.jsx)("option",{value:"ar",children:"Arabic"}),(0,j.jsx)("option",{value:"hi",children:"Hindi"})]})]}),(0,j.jsxs)("div",{children:[(0,j.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Custom Instructions"}),(0,j.jsx)("textarea",{value:a.custom_instructions,onChange:e=>w("custom_instructions",e.target.value),placeholder:"Add any specific instructions for how the AI should respond to you...",rows:4,maxLength:1e3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"}),(0,j.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:[a.custom_instructions.length,"/1000 characters"]})]}),g&&(0,j.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:(0,j.jsx)("p",{className:"text-red-800 text-sm",children:g})}),v&&(0,j.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3",children:(0,j.jsx)("p",{className:"text-green-800 text-sm",children:"Preferences saved successfully!"})})]}),(0,j.jsxs)("div",{className:"flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50",children:[(0,j.jsxs)("button",{onClick:async()=>{if(window.confirm("Are you sure you want to reset all preferences to defaults?"))try{m(!0),y(null);const e=await Sc.post("http://localhost:5000/api/user/preferences/reset",{user_id:t.id});e.data.success&&(o(e.data.preferences),b(!0),setTimeout(()=>b(!1),3e3),r&&r(e.data.preferences))}catch(g){var e,n;console.error("Error resetting preferences:",g),y((null===(e=g.response)||void 0===e||null===(n=e.data)||void 0===n?void 0:n.error)||"Failed to reset preferences")}finally{m(!1)}},disabled:p,className:"flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-200 rounded-lg transition-colors disabled:opacity-50",children:[(0,j.jsx)(Lc,{size:16}),"Reset to Defaults"]}),(0,j.jsxs)("div",{className:"flex gap-3",children:[(0,j.jsx)("button",{onClick:n,disabled:p,className:"px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-200 rounded-lg transition-colors disabled:opacity-50",children:"Cancel"}),(0,j.jsxs)("button",{onClick:async()=>{try{m(!0),y(null);const e=await Sc.post("http://localhost:5000/api/user/preferences",{user_id:t.id,preferences:a});e.data.success&&(b(!0),setTimeout(()=>b(!1),3e3),r&&r(e.data.preferences))}catch(g){var e,n;console.error("Error saving preferences:",g),y((null===(e=g.response)||void 0===e||null===(n=e.data)||void 0===n?void 0:n.error)||"Failed to save preferences")}finally{m(!1)}},disabled:p,className:"flex items-center gap-2 px-4 py-2 bg-primary-500 hover:bg-primary-600 text-white rounded-lg transition-colors disabled:opacity-50",children:[p?(0,j.jsx)("div",{className:"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"}):(0,j.jsx)(zc,{size:16}),p?"Saving...":"Save Preferences"]})]})]})]})})};const Ic=function(){const[e,t]=(0,i.useState)(null),[n,r]=(0,i.useState)([]),[a,o]=(0,i.useState)(null),[s,l]=(0,i.useState)([]),[u,c]=(0,i.useState)(!1);(0,i.useEffect)(()=>{const e=localStorage.getItem("user");if(e){const n=JSON.parse(e);t(n),d(n.id)}},[]);const d=async e=>{try{const t=await Tc(e);r(t.data.conversations)}catch(t){console.error("Error loading conversations:",t)}},f=async(e,n)=>{try{const r={id:(await Pc({username:e,email:n})).data.user_id,username:e,email:n};t(r),localStorage.setItem("user",JSON.stringify(r)),d(r.id)}catch(r){console.error("Login error:",r),alert("Login failed. Please try again.")}};return e?(0,j.jsxs)("div",{className:"flex h-screen bg-gray-50",children:[(0,j.jsx)(T,{conversations:n,currentConversation:a,onSelectConversation:async e=>{o(e);try{const t=await Cc(e);l(t.data.messages)}catch(t){console.error("Error loading conversation:",t)}},onNewConversation:()=>{o(null),l([])},onLogout:()=>{localStorage.removeItem("user"),t(null),r([]),o(null),l([]),c(!1)},onOpenPreferences:()=>{c(!0)},user:e}),(0,j.jsx)(Mc,{conversationId:a,messages:s,onNewMessage:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;l(null!==t?n=>{const r=[...n];return r[t]=e,r}:t=>[...t,e])},onConversationCreate:t=>{o(t),d(e.id)},onConversationUpdate:async()=>{e&&await d(e.id)},user:e}),u&&(0,j.jsx)(Vc,{user:e,onClose:()=>{c(!1)},onPreferencesUpdate:e=>{console.log("Preferences updated:",e)}})]}):(0,j.jsx)(Oc,{onLogin:f})};a.createRoot(document.getElementById("root")).render((0,j.jsx)(i.StrictMode,{children:(0,j.jsx)(Ic,{})}))})();
//# sourceMappingURL=main.66e38d15.js.map
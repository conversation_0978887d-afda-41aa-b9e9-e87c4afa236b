from flask_sqlalchemy import SQLAlchemy

db = SQLAlchemy()

class User(db.Model):
    __tablename__ = 'users'
    id = db.Column(db.String(100), primary_key=True)
    username = db.Column(db.String(100), unique=True, nullable=False)
    email = db.Column(db.String(100), unique=True, nullable=False)
    created_at = db.Column(db.DateTime, server_default=db.func.now())

    # Personalization preferences - these will be added via migration
    conversation_tone = db.Column(db.String(50), default='friendly', nullable=True)
    response_style = db.Column(db.String(50), default='balanced', nullable=True)
    preferred_language = db.Column(db.String(10), default='en', nullable=True)
    custom_instructions = db.Column(db.Text, default='', nullable=True)

class Conversation(db.Model):
    __tablename__ = 'conversations'
    id = db.Column(db.String(100), primary_key=True)
    user_id = db.Column(db.String(100), db.ForeignKey('users.id'), nullable=False)
    title = db.Column(db.String(200), nullable=False)
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())

class Message(db.Model):
    __tablename__ = 'messages'
    id = db.Column(db.String(100), primary_key=True)
    conversation_id = db.Column(db.String(100), db.ForeignKey('conversations.id'), nullable=False)
    role = db.Column(db.String(20), nullable=False)  # 'user' or 'assistant'
    content = db.Column(db.Text, nullable=False)
    timestamp = db.Column(db.DateTime, server_default=db.func.now())
    
    conversation = db.relationship('Conversation', backref=db.backref('messages', lazy=True))

class UserPreference(db.Model):
    __tablename__ = 'user_preferences'
    id = db.Column(db.String(100), primary_key=True)
    user_id = db.Column(db.String(100), db.ForeignKey('users.id'), nullable=False)
    preference_key = db.Column(db.String(100), nullable=False)
    preference_value = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())
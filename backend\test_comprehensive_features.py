#!/usr/bin/env python3
"""
Comprehensive Feature Testing Suite for Enhanced Gemini AI Application
Tests all major enhancements: multilingual support, live data, image analysis, file analysis
"""

import os
import sys
import json
import time
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add backend to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_api_endpoint(endpoint, method='GET', data=None, files=None):
    """Test an API endpoint"""
    base_url = "http://localhost:5000"
    url = f"{base_url}{endpoint}"
    
    try:
        if method == 'GET':
            response = requests.get(url, timeout=30)
        elif method == 'POST':
            if files:
                response = requests.post(url, data=data, files=files, timeout=30)
            else:
                response = requests.post(url, json=data, timeout=30)
        
        return {
            "success": response.status_code == 200,
            "status_code": response.status_code,
            "response": response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text[:500]
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

def main():
    print("🧪 Comprehensive Feature Testing Suite")
    print("=" * 60)
    print("Testing Enhanced Gemini AI Application Features")
    print("=" * 60)
    
    # Test categories
    tests = {
        "🌐 Multilingual Language Detection": [
            {
                "name": "English Text Detection",
                "endpoint": "/api/languages/detect",
                "method": "POST",
                "data": {"text": "Hello, how are you today?"}
            },
            {
                "name": "Hindi Text Detection",
                "endpoint": "/api/languages/detect", 
                "method": "POST",
                "data": {"text": "नमस्ते, आप कैसे हैं?"}
            },
            {
                "name": "Tamil Text Detection",
                "endpoint": "/api/languages/detect",
                "method": "POST", 
                "data": {"text": "வணக்கம், நீங்கள் எப்படி இருக்கிறீர்கள்?"}
            },
            {
                "name": "Arabic Text Detection",
                "endpoint": "/api/languages/detect",
                "method": "POST",
                "data": {"text": "مرحبا، كيف حالك؟"}
            }
        ],
        
        "🔴 Live Data Retrieval": [
            {
                "name": "News Data",
                "endpoint": "/api/news",
                "method": "GET"
            },
            {
                "name": "Weather Data", 
                "endpoint": "/api/weather",
                "method": "GET"
            },
            {
                "name": "Stock Data",
                "endpoint": "/api/stocks",
                "method": "GET"
            },
            {
                "name": "Cricket Data",
                "endpoint": "/api/cricket", 
                "method": "GET"
            }
        ],
        
        "🎤 Speech Services": [
            {
                "name": "TTS Language Detection",
                "endpoint": "/api/tts/detect-language",
                "method": "POST",
                "data": {"text": "This is a test for text-to-speech language detection"}
            },
            {
                "name": "TTS Synthesis Test",
                "endpoint": "/api/tts/synthesize",
                "method": "POST", 
                "data": {"text": "Hello world", "language": "en"}
            }
        ],
        
        "🔧 System Health": [
            {
                "name": "Health Check",
                "endpoint": "/api/health",
                "method": "GET"
            },
            {
                "name": "Gemini Configuration",
                "endpoint": "/api/gemini/status",
                "method": "GET"
            }
        ]
    }
    
    # Run tests
    total_tests = 0
    passed_tests = 0
    
    for category, test_list in tests.items():
        print(f"\n{category}")
        print("-" * 50)
        
        for test in test_list:
            total_tests += 1
            print(f"Testing: {test['name']}...", end=" ")
            
            result = test_api_endpoint(
                test['endpoint'], 
                test.get('method', 'GET'),
                test.get('data'),
                test.get('files')
            )
            
            if result['success']:
                print("✅ PASS")
                passed_tests += 1
                
                # Show sample response for interesting endpoints
                if 'detect' in test['endpoint'] or 'news' in test['endpoint']:
                    response_preview = str(result['response'])[:100]
                    print(f"   📄 Response: {response_preview}...")
                    
            else:
                print("❌ FAIL")
                if 'error' in result:
                    print(f"   ⚠️  Error: {result['error']}")
                else:
                    print(f"   ⚠️  Status: {result['status_code']}")
    
    # Summary
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 All tests passed! System is fully functional.")
    elif passed_tests >= total_tests * 0.8:
        print("✅ Most tests passed. System is largely functional.")
    else:
        print("⚠️  Many tests failed. Please check system configuration.")
    
    print("\n📝 Test Categories Covered:")
    print("   • Multilingual language detection (4 languages)")
    print("   • Live data retrieval (news, weather, stocks, cricket)")
    print("   • Speech services (TTS detection and synthesis)")
    print("   • System health and configuration")
    
    print("\n🔍 Manual Testing Recommended:")
    print("   • Upload image files to test deep image analysis")
    print("   • Upload document files to test comprehensive file analysis")
    print("   • Test voice input with different languages")
    print("   • Test chat responses with live data queries")
    
    return passed_tests >= total_tests * 0.7

if __name__ == "__main__":
    print("⚠️  Note: This test requires the Flask backend to be running on localhost:5000")
    print("   Start the backend with: python app.py")
    print("   Press Enter to continue or Ctrl+C to exit...")
    
    try:
        input()
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 Testing cancelled by user")
        sys.exit(1)

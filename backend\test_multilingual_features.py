#!/usr/bin/env python3
"""
Multilingual Feature Testing Suite
Tests language detection, translation, and multilingual response generation
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add backend to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_language_service():
    """Test the language service directly"""
    try:
        from services.language_service import LanguageService
        
        print("🌍 Testing Language Service")
        print("-" * 40)
        
        service = LanguageService()
        
        # Test cases with different languages
        test_texts = [
            ("Hello, how are you?", "en", "English"),
            ("नमस्ते, आप कैसे हैं?", "hi", "Hindi"),
            ("வணக்கம், நீங்கள் எப்படி இருக்கிறீர்கள்?", "ta", "Tamil"),
            ("مرحبا، كيف حالك؟", "ar", "Arabic"),
            ("你好，你好吗？", "zh", "Chinese"),
            ("こんにちは、元気ですか？", "ja", "Japanese"),
            ("안녕하세요, 어떻게 지내세요?", "ko", "Korean"),
            ("Bonjour, comment allez-vous?", "fr", "French"),
            ("Ho<PERSON>, ¿cómo estás?", "es", "Spanish"),
            ("Hallo, wie geht es dir?", "de", "German"),
            ("Ciao, come stai?", "it", "Italian"),
            ("Olá, como você está?", "pt", "Portuguese"),
            ("Привет, как дела?", "ru", "Russian"),
            ("হ্যালো, আপনি কেমন আছেন?", "bn", "Bengali"),
            ("ગુજરાતી ભાષામાં આપનું સ્વાગત છે", "gu", "Gujarati"),
            ("ਸਤ ਸ੍ਰੀ ਅਕਾਲ, ਤੁਸੀਂ ਕਿਵੇਂ ਹੋ?", "pa", "Punjabi"),
            ("నమస్కారం, మీరు ఎలా ఉన్నారు?", "te", "Telugu"),
            ("ನಮಸ್ಕಾರ, ನೀವು ಹೇಗಿದ್ದೀರಿ?", "kn", "Kannada"),
            ("നമസ്കാരം, നിങ്ങൾ എങ്ങനെയുണ്ട്?", "ml", "Malayalam"),
            ("नमस्कार, तुम्ही कसे आहात?", "mr", "Marathi"),
            ("ନମସ୍କାର, ଆପଣ କେମିତି ଅଛନ୍ତି?", "or", "Odia")
        ]
        
        success_count = 0
        total_count = len(test_texts)
        
        for text, expected_lang, lang_name in test_texts:
            try:
                result = service.detect_and_prepare_message(text)
                detected = result.get('detected_language', 'unknown')
                confidence = result.get('confidence', 0)
                
                # Check if detection is correct or close
                is_correct = (detected == expected_lang or 
                            detected.startswith(expected_lang) or 
                            expected_lang.startswith(detected))
                
                status = "✅" if is_correct else "⚠️"
                print(f"{status} {lang_name}: {detected} (confidence: {confidence:.2f})")
                
                if is_correct:
                    success_count += 1
                    
            except Exception as e:
                print(f"❌ {lang_name}: Error - {e}")
        
        print(f"\n📊 Language Detection Results: {success_count}/{total_count}")
        return success_count >= total_count * 0.7
        
    except Exception as e:
        print(f"❌ Language Service Test Failed: {e}")
        return False

def test_gemini_multilingual():
    """Test Gemini's multilingual capabilities"""
    try:
        from config.gemini_config import GeminiConfig
        
        print("\n🤖 Testing Gemini Multilingual Responses")
        print("-" * 40)
        
        if not GeminiConfig.is_initialized():
            GeminiConfig.initialize()
        
        model = GeminiConfig.create_detailed_model('chat')
        
        # Test multilingual queries
        test_queries = [
            ("Explain artificial intelligence in Hindi", "Hindi"),
            ("தமிழில் செயற்கை நுண்ணறிவு பற்றி விளக்கவும்", "Tamil"),
            ("Explain AI in Arabic", "Arabic"),
            ("Explique l'IA en français", "French"),
            ("Explica la IA en español", "Spanish")
        ]
        
        success_count = 0
        
        for query, language in test_queries:
            try:
                response = model.generate_content(query)
                response_text = response.text if hasattr(response, 'text') else str(response)
                
                if response_text and len(response_text) > 50:
                    print(f"✅ {language}: Response generated ({len(response_text)} chars)")
                    success_count += 1
                else:
                    print(f"⚠️ {language}: Short or empty response")
                    
            except Exception as e:
                print(f"❌ {language}: Error - {e}")
        
        print(f"\n📊 Multilingual Response Results: {success_count}/{len(test_queries)}")
        return success_count >= len(test_queries) * 0.6
        
    except Exception as e:
        print(f"❌ Gemini Multilingual Test Failed: {e}")
        return False

def main():
    print("🌍 Comprehensive Multilingual Testing Suite")
    print("=" * 60)
    
    results = []
    
    # Test 1: Language Service
    print("\n1️⃣ Language Detection Service")
    results.append(test_language_service())
    
    # Test 2: Gemini Multilingual
    print("\n2️⃣ Gemini Multilingual Responses")
    results.append(test_gemini_multilingual())
    
    # Summary
    passed_tests = sum(results)
    total_tests = len(results)
    
    print("\n" + "=" * 60)
    print(f"📊 Overall Results: {passed_tests}/{total_tests} test categories passed")
    
    if passed_tests == total_tests:
        print("🎉 All multilingual tests passed!")
        print("✅ System supports comprehensive multilingual functionality")
    elif passed_tests >= total_tests * 0.5:
        print("✅ Most multilingual tests passed")
        print("⚠️ Some features may need fine-tuning")
    else:
        print("❌ Multilingual tests failed")
        print("🔧 Please check language service configuration")
    
    print("\n🌟 Supported Languages Tested:")
    print("   • Indian Languages: Hindi, Tamil, Telugu, Kannada, Malayalam, Bengali, Gujarati, Punjabi, Marathi, Odia")
    print("   • International: English, Arabic, Chinese, Japanese, Korean, French, Spanish, German, Italian, Portuguese, Russian")
    
    return passed_tests >= total_tests * 0.5

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Speech Recognition Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .language-selector {
            margin-bottom: 20px;
        }
        select {
            padding: 10px;
            font-size: 16px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-right: 10px;
        }
        .controls {
            text-align: center;
            margin: 30px 0;
        }
        .record-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 18px;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .record-btn:hover {
            background: #45a049;
        }
        .record-btn.recording {
            background: #f44336;
            animation: pulse 1s infinite;
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        .status {
            text-align: center;
            margin: 20px 0;
            font-weight: bold;
        }
        .transcript {
            background: #f9f9f9;
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 5px;
            min-height: 100px;
            margin: 20px 0;
        }
        .interim {
            color: #666;
            font-style: italic;
        }
        .final {
            color: #333;
            font-weight: bold;
        }
        .language-info {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 14px;
        }
        .error {
            background: #ffebee;
            color: #c62828;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Enhanced Speech Recognition Test for Indian Languages</h1>
        
        <div class="language-selector">
            <label for="languageSelect">Select Language:</label>
            <select id="languageSelect">
                <option value="auto">🌐 Auto-detect</option>
                <option value="en-US">English (US)</option>
                <option value="en-IN">English (India)</option>
                <option value="hi-IN">हिंदी (Hindi)</option>
                <option value="ta-IN">தமிழ் (Tamil)</option>
                <option value="te-IN">తెలుగు (Telugu)</option>
                <option value="ml-IN">മലയാളം (Malayalam)</option>
                <option value="kn-IN">ಕನ್ನಡ (Kannada)</option>
                <option value="bn-IN">বাংলা (Bengali)</option>
                <option value="gu-IN">ગુજરાતી (Gujarati)</option>
                <option value="mr-IN">मराठी (Marathi)</option>
                <option value="pa-IN">ਪੰਜਾਬੀ (Punjabi)</option>
                <option value="ur-IN">اردو (Urdu)</option>
            </select>
        </div>

        <div class="controls">
            <button id="recordBtn" class="record-btn">🎤 Start Recording</button>
        </div>

        <div id="status" class="status">Ready to record</div>
        
        <div id="languageInfo" class="language-info" style="display: none;"></div>
        
        <div id="error" class="error" style="display: none;"></div>

        <div class="transcript">
            <div id="finalTranscript" class="final"></div>
            <div id="interimTranscript" class="interim"></div>
        </div>

        <div id="instructions">
            <h3>Instructions:</h3>
            <ul>
                <li>Select your preferred language or use auto-detect</li>
                <li>Click the microphone button to start recording</li>
                <li>Speak clearly in your chosen language</li>
                <li>The system will display real-time transcription</li>
                <li>Click stop to end recording</li>
            </ul>
            
            <h3>Test Phrases:</h3>
            <ul>
                <li><strong>Hindi:</strong> नमस्ते, मेरा नाम राम है। आप कैसे हैं?</li>
                <li><strong>Tamil:</strong> வணக்கம், என் பெயர் ராம். நீங்கள் எப்படி இருக்கிறீர்கள்?</li>
                <li><strong>Telugu:</strong> నమస్కారం, నా పేరు రామ్. మీరు ఎలా ఉన్నారు?</li>
                <li><strong>Malayalam:</strong> നമസ്കാരം, എന്റെ പേര് രാം. നിങ്ങൾ എങ്ങനെയുണ്ട്?</li>
                <li><strong>Kannada:</strong> ನಮಸ್ಕಾರ, ನನ್ನ ಹೆಸರು ರಾಮ್. ನೀವು ಹೇಗಿದ್ದೀರಿ?</li>
            </ul>
        </div>
    </div>

    <script>
        class EnhancedSpeechRecognition {
            constructor() {
                this.recognition = null;
                this.isRecording = false;
                this.selectedLanguage = 'auto';
                this.detectedLanguage = 'en-US';
                this.finalTranscript = '';
                this.interimTranscript = '';
                
                this.initializeElements();
                this.initializeSpeechRecognition();
                this.bindEvents();
            }

            initializeElements() {
                this.recordBtn = document.getElementById('recordBtn');
                this.languageSelect = document.getElementById('languageSelect');
                this.status = document.getElementById('status');
                this.languageInfo = document.getElementById('languageInfo');
                this.error = document.getElementById('error');
                this.finalTranscriptEl = document.getElementById('finalTranscript');
                this.interimTranscriptEl = document.getElementById('interimTranscript');
            }

            initializeSpeechRecognition() {
                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                
                if (!SpeechRecognition) {
                    this.showError('Speech recognition not supported in this browser');
                    return;
                }

                this.recognition = new SpeechRecognition();
                this.recognition.continuous = true;
                this.recognition.interimResults = true;
                this.recognition.maxAlternatives = 3;
                
                // Set initial language
                this.recognition.lang = 'en-US';
                
                this.recognition.onstart = () => {
                    this.isRecording = true;
                    this.updateUI();
                    this.showStatus('Listening...');
                    this.hideError();
                };

                this.recognition.onresult = (event) => {
                    this.handleResults(event);
                };

                this.recognition.onerror = (event) => {
                    this.handleError(event);
                };

                this.recognition.onend = () => {
                    if (this.isRecording) {
                        // Auto-restart if still recording
                        try {
                            this.recognition.start();
                        } catch (e) {
                            this.stopRecording();
                        }
                    }
                };
            }

            bindEvents() {
                this.recordBtn.addEventListener('click', () => {
                    if (this.isRecording) {
                        this.stopRecording();
                    } else {
                        this.startRecording();
                    }
                });

                this.languageSelect.addEventListener('change', (e) => {
                    this.selectedLanguage = e.target.value;
                    this.updateLanguage();
                });
            }

            startRecording() {
                if (!this.recognition) return;
                
                try {
                    this.finalTranscript = '';
                    this.interimTranscript = '';
                    this.updateTranscript();
                    
                    // Set language before starting
                    this.updateLanguage();
                    
                    this.recognition.start();
                } catch (error) {
                    this.showError('Failed to start recording: ' + error.message);
                }
            }

            stopRecording() {
                if (this.recognition && this.isRecording) {
                    this.isRecording = false;
                    this.recognition.stop();
                    this.updateUI();
                    this.showStatus('Recording stopped');
                }
            }

            updateLanguage() {
                if (!this.recognition) return;
                
                let targetLang = this.selectedLanguage;
                if (targetLang === 'auto') {
                    targetLang = this.detectedLanguage || 'en-US';
                }
                
                try {
                    this.recognition.lang = targetLang;
                    console.log('Language set to:', targetLang);
                    this.showLanguageInfo(`Recognition language: ${targetLang}`);
                } catch (error) {
                    console.warn('Failed to set language:', error);
                    this.recognition.lang = 'en-US';
                    this.showError('Language not supported, using English');
                }
            }

            handleResults(event) {
                let newFinalTranscript = '';
                let newInterimTranscript = '';

                for (let i = event.resultIndex; i < event.results.length; i++) {
                    const result = event.results[i];
                    const transcript = result[0].transcript;
                    
                    if (result.isFinal) {
                        newFinalTranscript += transcript;
                    } else {
                        newInterimTranscript += transcript;
                    }
                }

                if (newFinalTranscript) {
                    this.finalTranscript += newFinalTranscript;
                }
                
                this.interimTranscript = newInterimTranscript;
                this.updateTranscript();
                
                // Auto-detect language if in auto mode
                if (this.selectedLanguage === 'auto' && (newFinalTranscript || newInterimTranscript)) {
                    this.detectLanguage(newFinalTranscript || newInterimTranscript);
                }
            }

            detectLanguage(text) {
                // Simple language detection based on Unicode ranges
                const patterns = {
                    'hi-IN': /[\u0900-\u097F]/,
                    'ta-IN': /[\u0B80-\u0BFF]/,
                    'te-IN': /[\u0C00-\u0C7F]/,
                    'ml-IN': /[\u0D00-\u0D7F]/,
                    'kn-IN': /[\u0C80-\u0CFF]/,
                    'bn-IN': /[\u0980-\u09FF]/,
                    'gu-IN': /[\u0A80-\u0AFF]/,
                    'pa-IN': /[\u0A00-\u0A7F]/,
                    'ur-IN': /[\u0600-\u06FF]/,
                };

                for (const [lang, pattern] of Object.entries(patterns)) {
                    if (pattern.test(text)) {
                        if (this.detectedLanguage !== lang) {
                            this.detectedLanguage = lang;
                            this.updateLanguage();
                            console.log('Detected language:', lang);
                        }
                        return;
                    }
                }
                
                // Default to English if no pattern matches
                if (this.detectedLanguage !== 'en-US') {
                    this.detectedLanguage = 'en-US';
                    this.updateLanguage();
                }
            }

            handleError(event) {
                console.error('Speech recognition error:', event.error);
                
                if (event.error === 'language-not-supported') {
                    this.showError(`Language not supported. Switching to English.`);
                    this.recognition.lang = 'en-US';
                    this.detectedLanguage = 'en-US';
                } else {
                    this.showError(`Recognition error: ${event.error}`);
                }
                
                this.stopRecording();
            }

            updateUI() {
                if (this.isRecording) {
                    this.recordBtn.textContent = '⏹️ Stop Recording';
                    this.recordBtn.classList.add('recording');
                } else {
                    this.recordBtn.textContent = '🎤 Start Recording';
                    this.recordBtn.classList.remove('recording');
                }
            }

            updateTranscript() {
                this.finalTranscriptEl.textContent = this.finalTranscript;
                this.interimTranscriptEl.textContent = this.interimTranscript;
            }

            showStatus(message) {
                this.status.textContent = message;
            }

            showLanguageInfo(message) {
                this.languageInfo.textContent = message;
                this.languageInfo.style.display = 'block';
            }

            showError(message) {
                this.error.textContent = message;
                this.error.style.display = 'block';
            }

            hideError() {
                this.error.style.display = 'none';
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new EnhancedSpeechRecognition();
        });
    </script>
</body>
</html>

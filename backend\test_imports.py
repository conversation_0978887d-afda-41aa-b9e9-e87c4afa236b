#!/usr/bin/env python3
"""
Test script to verify all imports work correctly
"""

print("Testing imports...")

try:
    from flask import Flask
    print("✓ Flask imported successfully")
except ImportError as e:
    print(f"✗ Flask import failed: {e}")

try:
    from flask_socketio import SocketIO, emit
    print("✓ Flask-SocketIO imported successfully")
except ImportError as e:
    print(f"✗ Flask-SocketIO import failed: {e}")

try:
    from services.streaming_asr_service import StreamingASRService
    print("✓ StreamingASRService imported successfully")
except ImportError as e:
    print(f"✗ StreamingASRService import failed: {e}")

try:
    from services.speech_service import SpeechToTextService
    print("✓ SpeechToTextService imported successfully")
except ImportError as e:
    print(f"✗ SpeechToTextService import failed: {e}")

print("\nTesting basic Flask-SocketIO setup...")

try:
    app = Flask(__name__)
    socketio = SocketIO(app, cors_allowed_origins="*")
    print("✓ Flask-SocketIO initialized successfully")
except Exception as e:
    print(f"✗ Flask-SocketIO initialization failed: {e}")

print("\nTesting StreamingASRService...")

try:
    streaming_service = StreamingASRService()
    print("✓ StreamingASRService created successfully")
except Exception as e:
    print(f"✗ StreamingASRService creation failed: {e}")

print("\nAll tests completed!")

import React, { useState } from 'react';
import { MoreVertical, Edit2, Trash2, LogOut, Plus, Settings } from 'lucide-react';

const Sidebar = ({
  conversations,
  currentConversation,
  onSelectConversation,
  onNewConversation,
  onLogout,
  onOpenPreferences,
  user
}) => {
  const [editingChat, setEditingChat] = useState(null);
  const [editTitle, setEditTitle] = useState('');
  const [showDropdown, setShowDropdown] = useState(null);

  const handleRenameChat = (chatId, currentTitle) => {
    setEditingChat(chatId);
    setEditTitle(currentTitle);
    setShowDropdown(null);
  };

  const handleSaveRename = (chatId) => {
    // TODO: Implement API call to rename chat
    console.log('Renaming chat', chatId, 'to', editTitle);
    setEditingChat(null);
    setEditTitle('');
  };

  const handleDeleteChat = (chatId) => {
    // TODO: Implement API call to delete chat
    console.log('Deleting chat', chatId);
    setShowDropdown(null);
  };

  const handleKeyPress = (e, chatId) => {
    if (e.key === 'Enter') {
      handleSaveRename(chatId);
    } else if (e.key === 'Escape') {
      setEditingChat(null);
      setEditTitle('');
    }
  };

  return (
    <div className="w-80 h-screen bg-gradient-to-b from-slate-900 via-slate-800 to-slate-900 text-white flex flex-col shadow-2xl">
      {/* Header */}
      <div className="p-6 border-b border-white/10 bg-white/5 backdrop-blur-sm">
        <div className="flex items-center gap-3 mb-6">
          <div className="text-3xl animate-pulse-slow">🤖</div>
          <h2 className="text-xl font-bold bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent">
            Sozhaa Tech AI
          </h2>
        </div>
        <button
          onClick={onNewConversation}
          className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-gradient-to-r from-primary-500 to-secondary-500 hover:from-primary-600 hover:to-secondary-600 text-white rounded-xl font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg"
        >
          <Plus size={20} />
          New Chat
        </button>
      </div>

      {/* Conversations List */}
      <div className="flex-1 overflow-y-auto custom-scrollbar px-4 py-2">
        {conversations.map(conversation => (
          <div
            key={conversation.id}
            className={`group relative p-3 mb-2 rounded-xl cursor-pointer transition-all duration-300 hover:bg-white/10 ${
              currentConversation === conversation.id
                ? 'bg-gradient-to-r from-primary-500/30 to-secondary-500/30 border border-primary-400/50'
                : 'bg-white/5 hover:bg-white/10'
            }`}
          >
            <div
              className="flex-1"
              onClick={() => onSelectConversation(conversation.id)}
            >
              {editingChat === conversation.id ? (
                <input
                  type="text"
                  value={editTitle}
                  onChange={(e) => setEditTitle(e.target.value)}
                  onKeyDown={(e) => handleKeyPress(e, conversation.id)}
                  onBlur={() => handleSaveRename(conversation.id)}
                  className="w-full bg-transparent border-b border-white/30 text-white text-sm font-medium focus:outline-none focus:border-primary-400"
                  autoFocus
                />
              ) : (
                <div className="text-sm font-medium text-white truncate">
                  {conversation.title}
                </div>
              )}
              <div className="text-xs text-white/60 mt-1">
                {new Date(conversation.updated_at).toLocaleDateString()}
              </div>
            </div>

            {/* Three-dot menu */}
            <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setShowDropdown(showDropdown === conversation.id ? null : conversation.id);
                }}
                className="p-1 hover:bg-white/20 rounded-md transition-colors"
              >
                <MoreVertical size={16} className="text-white/70" />
              </button>

              {showDropdown === conversation.id && (
                <div className="absolute right-0 top-8 bg-slate-800 border border-white/20 rounded-lg shadow-xl z-10 min-w-[120px]">
                  <button
                    onClick={() => handleRenameChat(conversation.id, conversation.title)}
                    className="w-full flex items-center gap-2 px-3 py-2 text-sm text-white hover:bg-white/10 transition-colors"
                  >
                    <Edit2 size={14} />
                    Rename
                  </button>
                  <button
                    onClick={() => handleDeleteChat(conversation.id)}
                    className="w-full flex items-center gap-2 px-3 py-2 text-sm text-red-400 hover:bg-red-500/20 transition-colors"
                  >
                    <Trash2 size={14} />
                    Delete
                  </button>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Footer with User Info and Logout */}
      <div className="p-4 border-t border-white/10 bg-white/5 backdrop-blur-sm">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-full bg-gradient-to-r from-primary-500 to-secondary-500 flex items-center justify-center text-lg font-bold">
              {user?.username?.charAt(0).toUpperCase() || '👤'}
            </div>
            <div className="text-sm font-medium text-white">
              {user?.username}
            </div>
          </div>
        </div>
        <div className="space-y-2">
          <button
            onClick={onOpenPreferences}
            className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-primary-500/20 hover:bg-primary-500/30 text-primary-400 hover:text-primary-300 rounded-lg transition-all duration-300 text-sm font-medium"
          >
            <Settings size={16} />
            Preferences
          </button>
          <button
            onClick={onLogout}
            className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-red-500/20 hover:bg-red-500/30 text-red-400 hover:text-red-300 rounded-lg transition-all duration-300 text-sm font-medium"
          >
            <LogOut size={16} />
            Logout
          </button>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
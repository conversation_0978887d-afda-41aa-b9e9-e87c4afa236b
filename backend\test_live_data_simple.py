#!/usr/bin/env python3
"""
Simple test script to verify live data functionality without Flask dependencies
"""

import os
import sys
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_live_data_service():
    """Test the LiveDataService directly"""
    try:
        from services.live_data_service import LiveDataService
        
        print("🧪 Testing LiveDataService...")
        service = LiveDataService()
        
        # Test weather
        print("\n🌦️ Testing weather data...")
        weather_result = service.fetch_weather("Chennai")
        print(f"Weather result: {json.dumps(weather_result, indent=2)}")
        
        # Test news
        print("\n📰 Testing news data...")
        news_result = service.fetch_news("India")
        print(f"News result: {json.dumps(news_result, indent=2)}")
        
        # Test cricket
        print("\n🏏 Testing cricket data...")
        cricket_result = service.fetch_cricket()
        print(f"Cricket result: {json.dumps(cricket_result, indent=2)}")
        
        # Test stock
        print("\n📈 Testing stock data...")
        stock_result = service.fetch_stock("TCS")
        print(f"Stock result: {json.dumps(stock_result, indent=2)}")
        
        print("\n✅ LiveDataService test completed!")
        
    except Exception as e:
        print(f"❌ LiveDataService test failed: {e}")
        import traceback
        traceback.print_exc()

def test_gemini_integration():
    """Test Gemini integration with live data"""
    try:
        from services.gemini_web_service import GeminiWebService
        
        print("\n🤖 Testing Gemini integration...")
        service = GeminiWebService()
        
        # Test live data query
        print("\n🔍 Testing live data query...")
        result = service.fetch_live_data_with_real_apis("What's the weather in Chennai?")
        print(f"Gemini result: {json.dumps(result, indent=2)}")
        
        print("\n✅ Gemini integration test completed!")
        
    except Exception as e:
        print(f"❌ Gemini integration test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 Starting Live Data Tests...")
    print("=" * 50)
    
    # Test 1: LiveDataService
    test_live_data_service()
    
    # Test 2: Gemini Integration
    test_gemini_integration()
    
    print("\n" + "=" * 50)
    print("🏁 All tests completed!")

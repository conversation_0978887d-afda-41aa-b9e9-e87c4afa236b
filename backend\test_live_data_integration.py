#!/usr/bin/env python3
"""
Test script for the new live data integration
Tests all four data sources: News, Weather, Stocks, Cricket
"""

import os
import sys
import json
from dotenv import load_dotenv

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
load_dotenv()

# Import our services
from services.live_data_service import LiveDataService
from services.gemini_web_service import GeminiWebService

def test_live_data_service():
    """Test the LiveDataService directly"""
    print("=" * 60)
    print("🧪 TESTING LIVE DATA SERVICE DIRECTLY")
    print("=" * 60)
    
    service = LiveDataService()
    
    # Test queries for each intent
    test_queries = [
        ("latest news in India", "news"),
        ("weather in Chennai", "weather"),
        ("TCS stock price", "stocks"),
        ("cricket score today", "cricket")
    ]
    
    for query, expected_intent in test_queries:
        print(f"\n📝 Testing: '{query}' (Expected: {expected_intent})")
        print("-" * 50)
        
        # Test intent detection
        detected_intent = service.detect_intent(query)
        print(f"🎯 Detected Intent: {detected_intent}")
        
        if detected_intent != expected_intent:
            print(f"⚠️  Intent mismatch! Expected {expected_intent}, got {detected_intent}")
        
        # Test data fetching
        result = service.fetch_live_data(query)
        
        if result['success']:
            print(f"✅ Data fetched successfully from {result['source']}")
            print(f"📊 Data preview: {str(result['data'])[:200]}...")
        else:
            print(f"❌ Data fetch failed: {result['error']}")

def test_gemini_web_service():
    """Test the GeminiWebService with live data integration"""
    print("\n" + "=" * 60)
    print("🧪 TESTING GEMINI WEB SERVICE WITH LIVE DATA")
    print("=" * 60)
    
    service = GeminiWebService()
    
    # Test queries that should trigger live data
    live_queries = [
        "What's the latest news in India today?",
        "Show me Chennai weather right now",
        "Give me TCS stock price update",
        "What's the current cricket score?"
    ]
    
    for query in live_queries:
        print(f"\n📝 Testing: '{query}'")
        print("-" * 50)
        
        try:
            response = service.get_comprehensive_response(query)
            print(f"✅ Response received:")
            print(f"📄 {response[:300]}...")
            
            # Check if response contains live indicators
            live_indicators = ['✅', '📰', '🌦️', '💹', '🏏', '2024', '2025']
            has_live_content = any(indicator in response for indicator in live_indicators)
            
            if has_live_content:
                print("🌟 Response contains live content indicators!")
            else:
                print("⚠️  Response may not contain live content")
                
        except Exception as e:
            print(f"❌ Error: {e}")

def test_individual_apis():
    """Test individual API endpoints"""
    print("\n" + "=" * 60)
    print("🧪 TESTING INDIVIDUAL API ENDPOINTS")
    print("=" * 60)
    
    service = LiveDataService()
    
    # Test News API
    print("\n📰 Testing News API...")
    news_result = service.fetch_news("latest news")
    if news_result['success']:
        print(f"✅ News API working - {len(news_result['data'])} articles fetched")
        for i, article in enumerate(news_result['data'][:2], 1):
            print(f"   {i}. {article['title'][:60]}... - {article['source']}")
    else:
        print(f"❌ News API failed: {news_result['error']}")
    
    # Test Weather API
    print("\n🌦️ Testing Weather API...")
    weather_result = service.fetch_weather("Chennai weather")
    if weather_result['success']:
        data = weather_result['data']
        print(f"✅ Weather API working - {data['city']}: {data['temperature']}°C")
    else:
        print(f"❌ Weather API failed: {weather_result['error']}")
    
    # Test Stock API
    print("\n💹 Testing Stock API...")
    stock_result = service.fetch_stock("TCS stock")
    if stock_result['success']:
        data = stock_result['data']
        print(f"✅ Stock API working - {data['shortName']}: ₹{data['regularMarketPrice']}")
    else:
        print(f"❌ Stock API failed: {stock_result['error']}")
    
    # Test Cricket API
    print("\n🏏 Testing Cricket API...")
    cricket_result = service.fetch_cricket("cricket matches")
    if cricket_result['success']:
        print(f"✅ Cricket API working - {len(cricket_result['data'])} matches found")
        for i, match in enumerate(cricket_result['data'][:2], 1):
            print(f"   {i}. {match['name'][:50]}... - {match['status']}")
    else:
        print(f"❌ Cricket API failed: {cricket_result['error']}")

def main():
    """Run all tests"""
    print("🚀 STARTING LIVE DATA INTEGRATION TESTS")
    print("=" * 60)
    
    # Check environment variables
    print("🔧 Checking environment variables...")
    required_vars = ['GEMINI_API_KEY', 'NEWS_API_KEY', 'CRICKET_API_KEY']
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
        else:
            print(f"✅ {var}: {'*' * 10}{os.getenv(var)[-4:]}")
    
    if missing_vars:
        print(f"⚠️  Missing environment variables: {', '.join(missing_vars)}")
        print("Some tests may fail due to missing API keys.")
    
    # Run tests
    try:
        test_individual_apis()
        test_live_data_service()
        test_gemini_web_service()
        
        print("\n" + "=" * 60)
        print("🎉 ALL TESTS COMPLETED!")
        print("=" * 60)
        print("✅ If you see live data above, the integration is working!")
        print("🌐 Real-time data is now being fetched from external APIs")
        print("🤖 Gemini is summarizing the verified live data")
        
    except Exception as e:
        print(f"\n❌ Test execution failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

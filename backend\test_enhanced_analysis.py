#!/usr/bin/env python3
"""
Test enhanced image analysis without Gemini API
"""

import sys
import os
sys.path.append('.')

from PIL import Image
import tempfile

def test_enhanced_analysis():
    """Test enhanced image analysis"""
    print("🧪 Testing Enhanced Image Analysis...")
    
    try:
        # Import services
        from services.image_analyzer import ImageAnalyzer
        
        print("✅ ImageAnalyzer imported successfully")
        
        # Initialize service
        image_analyzer = ImageAnalyzer()
        print("✅ ImageAnalyzer initialized successfully")
        
        # Create test images with different properties
        test_images = [
            ("Bright Color Image", Image.new('RGB', (1920, 1080), color=(255, 100, 50))),
            ("Dark Grayscale", Image.new('L', (800, 600), color=50)),
            ("High Contrast", Image.new('RGB', (1024, 768), color=(255, 255, 255))),
            ("Small Image", Image.new('RGB', (320, 240), color=(100, 150, 200)))
        ]
        
        for name, test_image in test_images:
            print(f"\n🖼️ Testing: {name}")
            
            # Test enhanced image info
            basic_info = image_analyzer._get_basic_image_info(test_image)
            
            print(f"  📏 Dimensions: {basic_info['width']} × {basic_info['height']}")
            print(f"  🎨 Mode: {basic_info['mode_description']}")
            print(f"  📊 Resolution: {basic_info['resolution_category']}")
            print(f"  📐 Aspect Ratio: {basic_info['aspect_ratio']}")
            
            # Test advanced analysis
            advanced = basic_info.get('advanced_analysis', {})
            if 'color_analysis' in advanced:
                color_info = advanced['color_analysis']
                print(f"  🌈 Average Color: {color_info.get('average_color', 'N/A')}")
                print(f"  🎯 Dominant Channel: {color_info.get('dominant_channel', 'N/A')}")
            
            if 'brightness_contrast' in advanced:
                brightness_info = advanced['brightness_contrast']
                print(f"  💡 Brightness: {brightness_info.get('brightness_level', 'N/A')}")
                print(f"  🔍 Contrast: {brightness_info.get('contrast_level', 'N/A')}")
            
            if 'image_quality' in advanced:
                quality_info = advanced['image_quality']
                print(f"  ⭐ Quality: {quality_info.get('overall_assessment', 'N/A')}")
        
        # Test quota message generation
        print(f"\n📝 Testing quota message generation...")
        sample_info = image_analyzer._get_basic_image_info(test_images[0][1])
        quota_message = image_analyzer._generate_detailed_quota_message(sample_info)
        
        print("✅ Quota message generated successfully")
        print(f"📄 Message length: {len(quota_message)} characters")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in enhanced analysis test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run enhanced analysis tests"""
    print("🧪 Enhanced Image Analysis Test")
    print("=" * 50)
    
    success = test_enhanced_analysis()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ Enhanced image analysis is working!")
        print("\n📝 What users get now when quota is exceeded:")
        print("  • Detailed technical image properties")
        print("  • Color analysis (average color, dominant channel)")
        print("  • Brightness and contrast analysis")
        print("  • Texture and detail assessment")
        print("  • Image quality evaluation")
        print("  • Clear explanation of what's missing")
        print("  • Solutions to get full AI analysis")
        print("\n🎯 Users now get meaningful information even without Gemini API!")
    else:
        print("❌ Some issues detected in enhanced analysis")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

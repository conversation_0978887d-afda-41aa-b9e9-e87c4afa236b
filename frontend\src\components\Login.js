import React, { useState } from 'react';

const Login = ({ onLogin }) => {
  const [username, setUsername] = useState('deepak');
  const [email, setEmail] = useState('<EMAIL>');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (username.trim() && email.trim()) {
      setIsLoading(true);
      try {
        await onLogin(username.trim(), email.trim());
      } catch (error) {
        console.error('Login error:', error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleDemoLogin = () => {
    setUsername('deepak');
    setEmail('<EMAIL>');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-indigo-900 via-purple-900 to-indigo-700 relative overflow-hidden">
      {/* Animated Starry Background */}
      <div className="absolute inset-0">
        {/* Large floating particles */}
        <div className="absolute top-20 left-10 w-4 h-4 bg-white/20 rounded-full animate-float-slow"></div>
        <div className="absolute top-32 right-20 w-2 h-2 bg-pink-300/30 rounded-full animate-float-delayed"></div>
        <div className="absolute top-60 right-10 w-6 h-6 bg-purple-300/20 rounded-full animate-float"></div>
        <div className="absolute bottom-40 left-16 w-3 h-3 bg-blue-300/25 rounded-full animate-float-slow"></div>
        <div className="absolute bottom-20 left-1/3 w-5 h-5 bg-white/15 rounded-full animate-float-delayed"></div>
        <div className="absolute top-10 right-1/3 w-2 h-2 bg-indigo-300/30 rounded-full animate-float"></div>
        <div className="absolute top-1/2 left-8 w-4 h-4 bg-pink-200/20 rounded-full animate-float-slow"></div>
        <div className="absolute bottom-60 right-1/4 w-3 h-3 bg-purple-200/25 rounded-full animate-float"></div>

        {/* Small twinkling stars */}
        <div className="absolute top-16 left-1/4 w-1 h-1 bg-white/40 rounded-full animate-twinkle"></div>
        <div className="absolute top-40 right-1/3 w-1 h-1 bg-white/50 rounded-full animate-twinkle-delayed"></div>
        <div className="absolute bottom-32 left-1/2 w-1 h-1 bg-white/45 rounded-full animate-twinkle"></div>
        <div className="absolute top-3/4 right-12 w-1 h-1 bg-white/35 rounded-full animate-twinkle-delayed"></div>
        <div className="absolute top-1/3 left-12 w-1 h-1 bg-white/40 rounded-full animate-twinkle"></div>
        <div className="absolute bottom-16 right-1/2 w-1 h-1 bg-white/50 rounded-full animate-twinkle-delayed"></div>
      </div>

      <div className="relative z-10 w-full max-w-md px-6">
        <div className="bg-white/10 backdrop-blur-xl rounded-3xl p-8 shadow-2xl border border-white/20 ring-1 ring-white/10">
          <div className="text-center mb-8">
            <div className="flex items-center justify-center gap-4 mb-4">
              <div className="text-5xl animate-pulse">🤖</div>
              <h1 className="text-3xl font-bold text-white">
                Sozhaa <span className="text-pink-400">AI</span>
              </h1>
            </div>
            <p className="text-indigo-200 text-lg font-medium">Your Intelligent Conversation Partner</p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="username" className="flex items-center gap-2 mb-2 text-indigo-200 font-semibold">
                <span className="text-lg">👤</span>
                Username
              </label>
              <input
                type="text"
                id="username"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                placeholder="Enter your username"
                required
                className="w-full px-4 py-3 rounded-xl bg-white/20 border border-white/30 text-white placeholder-gray-300 focus:ring-2 focus:ring-pink-400 focus:border-pink-400 focus:outline-none transition-all duration-300"
              />
            </div>

            <div>
              <label htmlFor="email" className="flex items-center gap-2 mb-2 text-indigo-200 font-semibold">
                <span className="text-lg">📧</span>
                Email Address
              </label>
              <input
                type="email"
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email address"
                required
                className="w-full px-4 py-3 rounded-xl bg-white/20 border border-white/30 text-white placeholder-gray-300 focus:ring-2 focus:ring-pink-400 focus:border-pink-400 focus:outline-none transition-all duration-300"
              />
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full py-4 bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-400 hover:to-purple-500 disabled:from-gray-400 disabled:to-gray-500 text-white font-bold rounded-xl transition-all duration-300 hover:scale-105 shadow-lg shadow-pink-500/30 hover:shadow-pink-500/50 disabled:hover:scale-100 disabled:hover:shadow-none flex items-center justify-center gap-3 text-lg"
            >
              {isLoading ? (
                <>
                  <div className="w-6 h-6 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  Connecting...
                </>
              ) : (
                <>
                  <span className="text-xl">🚀</span>
                  Start Chatting
                </>
              )}
            </button>

            <div className="text-center mt-6 pt-6 border-t border-white/20">
              <p className="text-indigo-200 text-sm mb-3">Try with demo credentials:</p>
              <button
                type="button"
                onClick={handleDemoLogin}
                className="px-6 py-2 border border-indigo-300/40 text-indigo-100 hover:bg-white/10 rounded-lg font-semibold transition-all duration-300 text-sm"
              >
                Use Demo Account
              </button>
            </div>
          </form>

          <div className="grid grid-cols-3 gap-4 mt-8 pt-6 border-t border-white/20">
            <div className="text-center">
              <div className="text-2xl mb-2">💬</div>
              <span className="text-xs text-indigo-200 font-medium">Smart Conversations</span>
            </div>
            <div className="text-center">
              <div className="text-2xl mb-2">🧠</div>
              <span className="text-xs text-indigo-200 font-medium">AI Powered</span>
            </div>
            <div className="text-center">
              <div className="text-2xl mb-2">📚</div>
              <span className="text-xs text-indigo-200 font-medium">Knowledge Base</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
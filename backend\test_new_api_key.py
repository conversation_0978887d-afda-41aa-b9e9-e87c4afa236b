#!/usr/bin/env python3
"""
Test the new API key directly
"""

import sys
import os
sys.path.append('.')

def test_new_api_key():
    """Test that the new API key works for image analysis"""
    print("🧪 Testing New API Key...")
    
    try:
        import google.generativeai as genai
        from PIL import Image
        
        # Load the API key from environment
        from dotenv import load_dotenv
        load_dotenv()
        
        api_key = os.getenv('GEMINI_API_KEY')
        print(f"✅ API Key loaded: {api_key[:20]}...")
        
        # Configure Gemini
        genai.configure(api_key=api_key)
        
        # Create a test image
        test_image = Image.new('RGB', (100, 100), color=(255, 0, 0))
        
        # Initialize the model
        model = genai.GenerativeModel('gemini-2.0-flash-exp')
        
        # Test a simple request
        print("🔄 Testing API call...")
        response = model.generate_content([
            "Describe this image in one sentence.",
            test_image
        ])
        
        print("✅ API call successful!")
        print(f"📝 Response: {response.text}")
        
        return True
        
    except Exception as e:
        print(f"❌ API test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run API key test"""
    print("🧪 New API Key Test")
    print("=" * 40)
    
    success = test_new_api_key()
    
    print("\n" + "=" * 40)
    if success:
        print("✅ New API key is working!")
        print("🎯 The issue must be in the application logic")
    else:
        print("❌ New API key has issues")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
"""
Test script to verify the fixed /api/analyze/image and /api/analyze/file endpoints
"""

import requests
import io
import tempfile
from PIL import Image

def test_image_endpoint():
    """Test the /api/analyze/image endpoint"""
    print("🖼️ Testing /api/analyze/image endpoint...")
    
    try:
        # Create a simple test image
        test_image = Image.new('RGB', (200, 200), color='blue')
        
        # Add some text to make it more interesting
        from PIL import ImageDraw
        draw = ImageDraw.Draw(test_image)
        draw.text((50, 100), "TEST IMAGE", fill='white')
        
        # Convert to bytes
        img_bytes = io.BytesIO()
        test_image.save(img_bytes, format='JPEG')
        img_bytes.seek(0)
        
        # Prepare form data (matching what frontend sends)
        files = {'file': ('test_image.jpg', img_bytes, 'image/jpeg')}
        data = {
            'query': 'Analyze this test image in detail',
            'user_id': 'test-user-123'
        }
        
        # Make request to the endpoint
        response = requests.post(
            'http://localhost:5000/api/analyze/image',
            files=files,
            data=data,
            timeout=30
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Image endpoint working correctly!")
            print(f"Success: {result.get('success', False)}")
            if result.get('analysis'):
                analysis = result['analysis']
                comprehensive_text = analysis.get('comprehensive_analysis', '')
                print(f"Analysis length: {len(comprehensive_text)} characters")
            return True
        else:
            print(f"❌ Image endpoint failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing image endpoint: {e}")
        return False

def test_file_endpoint():
    """Test the /api/analyze/file endpoint"""
    print("\n📄 Testing /api/analyze/file endpoint...")
    
    try:
        # Create a test text file
        test_content = """
        This is a comprehensive test document for the file analysis endpoint.
        
        It contains multiple sections:
        1. Introduction: Testing the file analysis system
        2. Content: Various types of text and formatting
        3. Conclusion: Verification of the endpoint functionality
        
        The document includes structured content to test the analyzer's capabilities.
        """
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(test_content)
            temp_file_path = f.name
        
        # Prepare form data (matching what frontend sends)
        with open(temp_file_path, 'rb') as f:
            files = {'file': ('test_document.txt', f, 'text/plain')}
            data = {
                'query': 'Provide comprehensive analysis of this document',
                'user_id': 'test-user-123'
            }
            
            # Make request to the endpoint
            response = requests.post(
                'http://localhost:5000/api/analyze/file',
                files=files,
                data=data,
                timeout=30
            )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ File endpoint working correctly!")
            print(f"Success: {result.get('success', False)}")
            if result.get('analysis'):
                analysis = result['analysis']
                comprehensive_text = analysis.get('comprehensive_analysis', '')
                print(f"Analysis length: {len(comprehensive_text)} characters")
            return True
        else:
            print(f"❌ File endpoint failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing file endpoint: {e}")
        return False
    finally:
        # Clean up
        try:
            import os
            os.unlink(temp_file_path)
        except:
            pass

def main():
    """Run endpoint tests"""
    print("🧪 Testing Fixed Analysis Endpoints")
    print("=" * 50)
    
    # Test both endpoints
    image_success = test_image_endpoint()
    file_success = test_file_endpoint()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"  Image Endpoint: {'✅ PASS' if image_success else '❌ FAIL'}")
    print(f"  File Endpoint: {'✅ PASS' if file_success else '❌ FAIL'}")
    
    if image_success and file_success:
        print("\n🎉 All endpoints are working correctly!")
        print("The 'Sorry, I encountered an error' issue should be resolved.")
    else:
        print("\n⚠️ Some endpoints are still failing. Check the error messages above.")
    
    return image_success and file_success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

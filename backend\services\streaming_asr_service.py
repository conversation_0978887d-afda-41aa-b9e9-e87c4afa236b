import os
import io
import json
import base64
import tempfile
import threading
import time
from typing import Dict, Optional, Callable
from dotenv import load_dotenv
import requests
from collections import defaultdict

load_dotenv()

class StreamingASRService:
    """
    Server-side streaming ASR service for real-time speech recognition
    Supports OpenAI Whisper and Google Cloud Speech streaming
    """
    
    def __init__(self):
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        self.google_credentials = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
        self.sessions = {}
        self.session_lock = threading.Lock()
        
        # Language mapping for different providers
        self.whisper_language_map = {
            'hi-IN': 'hi',    # Hindi
            'ta-IN': 'ta',    # Tamil
            'te-IN': 'te',    # Telugu
            'ml-IN': 'ml',    # Malayalam
            'kn-IN': 'kn',    # Kannada
            'bn-IN': 'bn',    # Bengali
            'gu-IN': 'gu',    # Gujarati
            'mr-IN': 'mr',    # Marathi
            'pa-IN': 'pa',    # Punjabi
            'or-IN': 'or',    # Odia
            'as-IN': 'as',    # Assamese
            'ur-IN': 'ur',    # Urdu
            'en-IN': 'en',    # English (India)
            'en-US': 'en',    # English (US)
        }
    
    def create_session(self, session_id: str, language: str = 'ta-IN', callback: Callable = None):
        """Create a new streaming ASR session"""
        with self.session_lock:
            self.sessions[session_id] = {
                'language': language,
                'callback': callback,
                'audio_buffer': io.BytesIO(),
                'last_transcript': '',
                'is_active': True,
                'chunk_count': 0,
                'created_at': time.time()
            }
            print(f"Created streaming ASR session {session_id} for language {language}")
    
    def push_audio_chunk(self, session_id: str, audio_chunk: bytes) -> Dict:
        """
        Push audio chunk to session and get interim/final results
        Returns dict with 'text', 'is_final', 'confidence'
        """
        with self.session_lock:
            if session_id not in self.sessions:
                return {'error': 'Session not found'}
            
            session = self.sessions[session_id]
            if not session['is_active']:
                return {'error': 'Session inactive'}
            
            # Append audio chunk to buffer
            session['audio_buffer'].write(audio_chunk)
            session['chunk_count'] += 1
            
            # Process every few chunks for near real-time results
            if session['chunk_count'] % 3 == 0:  # Process every 3 chunks (~750ms)
                return self._process_audio_buffer(session_id)
            
            return {'text': '', 'is_final': False, 'confidence': 0.0}
    
    def _process_audio_buffer(self, session_id: str) -> Dict:
        """Process accumulated audio buffer and return transcription"""
        session = self.sessions[session_id]

        # Get audio data from buffer
        audio_data = session['audio_buffer'].getvalue()
        if len(audio_data) < 1024:  # Skip if too little audio
            return {'text': '', 'is_final': False, 'confidence': 0.0}

        language = session['language']

        # Try OpenAI Whisper for all languages (it has good Indian language support)
        if self.openai_api_key:
            result = self._transcribe_with_whisper_streaming(audio_data, language)
            if result['success']:
                # Check if this is new content
                new_text = result['transcript']
                if new_text and new_text.strip() != session['last_transcript'].strip():
                    session['last_transcript'] = new_text.strip()
                    print(f"Streaming transcription for {language}: {new_text}")
                    return {
                        'text': new_text,
                        'is_final': False,  # Streaming results are interim
                        'confidence': result.get('confidence', 0.8),
                        'provider': 'whisper'
                    }

        return {'text': '', 'is_final': False, 'confidence': 0.0}
    
    def _transcribe_with_whisper_streaming(self, audio_data: bytes, language: str) -> Dict:
        """Transcribe audio chunk using OpenAI Whisper API"""
        try:
            if not self.openai_api_key:
                return {'success': False, 'error': 'OpenAI API key not configured'}
            
            # Create temporary file for audio
            with tempfile.NamedTemporaryFile(suffix='.webm', delete=False) as temp_file:
                temp_file.write(audio_data)
                temp_file_path = temp_file.name
            
            try:
                url = "https://api.openai.com/v1/audio/transcriptions"
                headers = {
                    'Authorization': f'Bearer {self.openai_api_key}'
                }
                
                # Map language code for Whisper
                whisper_language = self.whisper_language_map.get(language, language.split('-')[0])
                
                files = {
                    'file': open(temp_file_path, 'rb'),
                    'model': (None, 'whisper-1'),
                    'language': (None, whisper_language),
                    'response_format': (None, 'json'),
                    'temperature': (None, '0.1')  # Lower temperature for more consistent results with Indian languages
                }

                # Add prompt for better Indian language recognition
                if language.endswith('-IN') and language != 'en-IN':
                    # Add a prompt to help with Indian language recognition
                    language_prompts = {
                        'hi-IN': 'नमस्ते, यह हिंदी भाषा में बोला गया है।',
                        'ta-IN': 'வணக்கம், இது தமிழ் மொழியில் பேசப்பட்டது.',
                        'te-IN': 'నమస్కారం, ఇది తెలుగు భాషలో మాట్లాడబడింది.',
                        'ml-IN': 'നമസ്കാരം, ഇത് മലയാളം ഭാഷയിൽ സംസാരിച്ചതാണ്.',
                        'kn-IN': 'ನಮಸ್ಕಾರ, ಇದು ಕನ್ನಡ ಭಾಷೆಯಲ್ಲಿ ಮಾತನಾಡಲಾಗಿದೆ.',
                        'bn-IN': 'নমস্কার, এটি বাংলা ভাষায় বলা হয়েছে।',
                        'gu-IN': 'નમસ્તે, આ ગુજરાતી ભાષામાં બોલાયેલું છે।',
                        'mr-IN': 'नमस्कार, हे मराठी भाषेत बोलले आहे।',
                        'pa-IN': 'ਸਤ ਸ੍ਰੀ ਅਕਾਲ, ਇਹ ਪੰਜਾਬੀ ਭਾਸ਼ਾ ਵਿੱਚ ਬੋਲਿਆ ਗਿਆ ਹੈ।'
                    }
                    if language in language_prompts:
                        files['prompt'] = (None, language_prompts[language])
                
                response = requests.post(url, headers=headers, files=files, timeout=10)
                response.raise_for_status()
                
                result = response.json()
                transcript = result.get('text', '').strip()
                
                return {
                    'success': True,
                    'transcript': transcript,
                    'confidence': 0.8,  # Whisper doesn't provide confidence scores
                    'language': language,
                    'provider': 'openai_whisper'
                }
                
            finally:
                # Clean up temporary file
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
                
        except Exception as e:
            print(f"Whisper streaming error: {e}")
            return {
                'success': False,
                'error': str(e),
                'provider': 'openai_whisper'
            }
    
    def finalize_session(self, session_id: str) -> Dict:
        """Finalize session and get final transcript"""
        with self.session_lock:
            if session_id not in self.sessions:
                return {'error': 'Session not found'}
            
            session = self.sessions[session_id]
            session['is_active'] = False
            
            # Process final audio buffer
            final_result = self._process_audio_buffer(session_id)
            if final_result.get('text'):
                final_result['is_final'] = True
            
            return final_result
    
    def cleanup_session(self, session_id: str):
        """Clean up session resources"""
        with self.session_lock:
            if session_id in self.sessions:
                session = self.sessions[session_id]
                session['audio_buffer'].close()
                del self.sessions[session_id]
                print(f"Cleaned up streaming ASR session {session_id}")
    
    def cleanup_old_sessions(self, max_age_seconds: int = 300):
        """Clean up sessions older than max_age_seconds"""
        current_time = time.time()
        with self.session_lock:
            sessions_to_remove = []
            for session_id, session in self.sessions.items():
                if current_time - session['created_at'] > max_age_seconds:
                    sessions_to_remove.append(session_id)
            
            for session_id in sessions_to_remove:
                self.cleanup_session(session_id)
    
    def get_session_status(self, session_id: str) -> Dict:
        """Get status of a streaming session"""
        with self.session_lock:
            if session_id not in self.sessions:
                return {'exists': False}
            
            session = self.sessions[session_id]
            return {
                'exists': True,
                'language': session['language'],
                'is_active': session['is_active'],
                'chunk_count': session['chunk_count'],
                'buffer_size': len(session['audio_buffer'].getvalue()),
                'age_seconds': time.time() - session['created_at']
            }
    
    def is_configured(self) -> Dict:
        """Check if streaming ASR is properly configured"""
        return {
            'openai_whisper': bool(self.openai_api_key),
            'google_cloud': bool(self.google_credentials and os.path.exists(self.google_credentials)),
            'any_provider': bool(self.openai_api_key or (self.google_credentials and os.path.exists(self.google_credentials)))
        }

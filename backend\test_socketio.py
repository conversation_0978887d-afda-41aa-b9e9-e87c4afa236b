#!/usr/bin/env python3
"""
Test SocketIO modes
"""

from flask import Flask
from flask_socketio import SocketIO

app = Flask(__name__)

print("Testing different SocketIO configurations...")

# Test 1: Default mode
try:
    socketio1 = SocketIO(app, cors_allowed_origins="*")
    print("✓ Default mode works")
except Exception as e:
    print(f"✗ Default mode failed: {e}")

# Test 2: Threading mode explicitly
try:
    socketio2 = SocketIO(app, cors_allowed_origins="*", async_mode='threading')
    print("✓ Threading mode works")
except Exception as e:
    print(f"✗ Threading mode failed: {e}")

# Test 3: Eventlet mode
try:
    socketio3 = SocketIO(app, cors_allowed_origins="*", async_mode='eventlet')
    print("✓ Eventlet mode works")
except Exception as e:
    print(f"✗ Eventlet mode failed: {e}")

# Test 4: Gevent mode
try:
    socketio4 = SocketIO(app, cors_allowed_origins="*", async_mode='gevent')
    print("✓ Gevent mode works")
except Exception as e:
    print(f"✗ Gevent mode failed: {e}")

print("Testing completed!")

#!/usr/bin/env python3
"""
Test script to verify quota handling works correctly
"""

import sys
import os
sys.path.append('.')

from PIL import Image
import tempfile

def test_quota_handling():
    """Test that quota handling works correctly"""
    print("🧪 Testing Quota Handling...")
    
    try:
        # Import services
        from services.image_analyzer import ImageAnalyzer
        from services.file_analyzer import FileAnalyzer
        from services.language_service import LanguageService
        
        print("✅ Services imported successfully")
        
        # Initialize services
        language_service = LanguageService()
        image_analyzer = ImageAnalyzer()
        file_analyzer = FileAnalyzer()
        
        print("✅ Services initialized successfully")
        
        # Test basic image info function (should work without API)
        test_image = Image.new('RGB', (800, 600), color='blue')
        basic_info = image_analyzer._get_basic_image_info(test_image)
        
        print("✅ Basic image info extraction working:")
        print(f"  - Dimensions: {basic_info['width']} × {basic_info['height']}")
        print(f"  - Format: {basic_info['format']}")
        print(f"  - Mode: {basic_info['mode_description']}")
        print(f"  - Aspect Ratio: {basic_info['aspect_ratio']}")
        print(f"  - Resolution: {basic_info['resolution_category']}")
        
        # Test file content statistics (should work without API)
        test_content = "This is a test document with multiple sentences. It contains various words and punctuation marks!"
        
        # Create a temporary file for testing
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(test_content)
            temp_file_path = f.name
        
        try:
            stats = file_analyzer._calculate_content_statistics(test_content)
            print("✅ File content statistics working:")
            print(f"  - Character count: {stats.get('character_count', 'N/A')}")
            print(f"  - Word count: {stats.get('word_count', 'N/A')}")
            print(f"  - Line count: {stats.get('line_count', 'N/A')}")
        except Exception as e:
            print(f"⚠️ File statistics test failed: {e}")
        
        # Clean up
        try:
            os.unlink(temp_file_path)
        except:
            pass
        
        return True
        
    except Exception as e:
        print(f"❌ Error in quota handling test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run quota handling tests"""
    print("🧪 Quota Handling Test")
    print("=" * 40)
    
    success = test_quota_handling()
    
    print("\n" + "=" * 40)
    if success:
        print("✅ Quota handling is ready!")
        print("\n📝 What happens when quota is exceeded:")
        print("  • Image uploads: Show basic technical info + quota message")
        print("  • File uploads: Show file info + content stats + quota message")
        print("  • User gets helpful information instead of errors")
        print("  • Clear instructions on how to resolve the issue")
        print("\n🎯 Your application will now handle quota limits gracefully!")
    else:
        print("❌ Some issues detected in quota handling")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

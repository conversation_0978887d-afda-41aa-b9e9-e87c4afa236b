import os
import uuid
import json
from werkzeug.utils import secure_filename
import tempfile
import shutil
from PIL import Image, ExifTags
import base64
import io

# Temporarily disable other imports for testing
# import fitz  # PyMuPDF - temporarily disabled
# from docx import Document as DocxDocument
# import openpyxl
# from pptx import Presentation
# from bs4 import BeautifulSoup
# import pandas as pd

class DocumentService:
    def __init__(self):
        self.upload_folder = 'uploads'
        self.allowed_extensions = {
            # Document types
            'txt', 'json', 'pdf', 'docx', 'doc', 'csv', 'xlsx', 'xls', 'pptx', 'ppt', 'html',
            # Image types
            'png', 'jpg', 'jpeg', 'gif', 'webp', 'svg'
        }
        self.image_extensions = {'png', 'jpg', 'jpeg', 'gif', 'webp', 'svg'}
        self.document_extensions = {'txt', 'json', 'pdf', 'docx', 'doc', 'html'}
        self.spreadsheet_extensions = {'csv', 'xlsx', 'xls'}
        self.presentation_extensions = {'pptx', 'ppt'}
        self.max_file_size = 10 * 1024 * 1024  # 10MB

        # Create upload directory if it doesn't exist
        if not os.path.exists(self.upload_folder):
            os.makedirs(self.upload_folder)
    
    def allowed_file(self, filename):
        """Check if file extension is allowed"""
        return '.' in filename and \
               filename.rsplit('.', 1)[1].lower() in self.allowed_extensions
    
    def save_uploaded_file(self, file):
        """Save uploaded file and return file path"""
        if not file or not file.filename:
            raise ValueError("No file provided")
        
        if not self.allowed_file(file.filename):
            raise ValueError(f"File type not allowed. Supported types: {', '.join(self.allowed_extensions)}")
        
        # Generate unique filename
        filename = secure_filename(file.filename)
        unique_filename = f"{uuid.uuid4()}_{filename}"
        file_path = os.path.join(self.upload_folder, unique_filename)
        
        # Save file
        file.save(file_path)
        
        # Check file size
        if os.path.getsize(file_path) > self.max_file_size:
            os.remove(file_path)
            raise ValueError("File size exceeds 10MB limit")
        
        return file_path, filename
    
    def extract_text_from_pdf(self, file_path):
        """Extract text from PDF using pdfplumber"""
        try:
            import pdfplumber

            text = ""
            metadata = {
                "pages": 0,
                "title": "",
                "author": "",
                "subject": ""
            }

            with pdfplumber.open(file_path) as pdf:
                metadata["pages"] = len(pdf.pages)

                # Extract metadata if available
                if pdf.metadata:
                    metadata["title"] = pdf.metadata.get('Title', '') or ''
                    metadata["author"] = pdf.metadata.get('Author', '') or ''
                    metadata["subject"] = pdf.metadata.get('Subject', '') or ''

                # Extract text from all pages
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n\n"

                # Also extract tables if any
                for page in pdf.pages:
                    tables = page.extract_tables()
                    for table in tables:
                        if table:
                            for row in table:
                                if row:
                                    text += " | ".join(str(cell) if cell else "" for cell in row) + "\n"
                            text += "\n"

            if not text.strip():
                text = "No readable text content found in this PDF file."

            return text.strip(), metadata

        except ImportError:
            raise Exception("pdfplumber is required for PDF processing. Please install it: pip install pdfplumber")
        except Exception as e:
            raise Exception(f"Error extracting PDF: {str(e)}")
    
    def extract_text_from_docx(self, file_path):
        """Extract text from DOCX file using python-docx"""
        try:
            from docx import Document

            doc = Document(file_path)
            text = ""

            # Extract text from paragraphs
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text += paragraph.text + "\n"

            # Extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        cell_text = cell.text.strip()
                        if cell_text:
                            row_text.append(cell_text)
                    if row_text:
                        text += " | ".join(row_text) + "\n"
                text += "\n"

            # Extract metadata
            metadata = {
                "paragraphs": len(doc.paragraphs),
                "tables": len(doc.tables),
                "title": "",
                "author": "",
                "subject": ""
            }

            # Try to get document properties
            try:
                core_props = doc.core_properties
                metadata["title"] = core_props.title or ""
                metadata["author"] = core_props.author or ""
                metadata["subject"] = core_props.subject or ""
            except:
                pass

            if not text.strip():
                text = "No readable text content found in this DOCX file."

            return text.strip(), metadata

        except ImportError:
            raise Exception("python-docx is required for DOCX processing. Please install it: pip install python-docx")
        except Exception as e:
            raise Exception(f"Error extracting DOCX: {str(e)}")
    
    def extract_text_from_txt(self, file_path):
        """Extract text from TXT file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                text = file.read()
            
            metadata = {
                "lines": len(text.split('\n')),
                "characters": len(text)
            }
            
            return text.strip(), metadata
        except UnicodeDecodeError:
            # Try with different encoding
            with open(file_path, 'r', encoding='latin-1') as file:
                text = file.read()
            
            metadata = {
                "lines": len(text.split('\n')),
                "characters": len(text),
                "encoding": "latin-1"
            }
            
            return text.strip(), metadata
        except Exception as e:
            raise Exception(f"Error extracting TXT: {str(e)}")
    
    def extract_text_from_csv(self, file_path):
        """Extract text from CSV file"""
        try:
            df = pd.read_csv(file_path)
            
            # Convert DataFrame to readable text
            text = f"CSV Data Summary:\n"
            text += f"Rows: {len(df)}, Columns: {len(df.columns)}\n\n"
            text += f"Column Names: {', '.join(df.columns.tolist())}\n\n"
            
            # Add first few rows as sample
            text += "Sample Data (first 5 rows):\n"
            text += df.head().to_string(index=False)
            
            # Add basic statistics for numeric columns
            numeric_cols = df.select_dtypes(include=['number']).columns
            if len(numeric_cols) > 0:
                text += "\n\nNumeric Column Statistics:\n"
                text += df[numeric_cols].describe().to_string()
            
            metadata = {
                "rows": len(df),
                "columns": len(df.columns),
                "column_names": df.columns.tolist(),
                "numeric_columns": numeric_cols.tolist()
            }
            
            return text, metadata
        except Exception as e:
            raise Exception(f"Error extracting CSV: {str(e)}")
    
    def extract_text_from_xlsx(self, file_path):
        """Extract text from Excel file"""
        try:
            workbook = openpyxl.load_workbook(file_path, data_only=True)
            text = ""
            metadata = {
                "sheets": workbook.sheetnames,
                "total_sheets": len(workbook.sheetnames)
            }
            
            for sheet_name in workbook.sheetnames:
                sheet = workbook[sheet_name]
                text += f"\n--- Sheet: {sheet_name} ---\n"
                
                # Get data from sheet
                data = []
                for row in sheet.iter_rows(values_only=True):
                    if any(cell is not None for cell in row):
                        data.append([str(cell) if cell is not None else '' for cell in row])
                
                if data:
                    df = pd.DataFrame(data[1:], columns=data[0] if data else [])
                    text += df.to_string(index=False)
                    text += f"\n(Rows: {len(df)}, Columns: {len(df.columns)})\n"
            
            workbook.close()
            return text.strip(), metadata
        except Exception as e:
            raise Exception(f"Error extracting Excel: {str(e)}")
    
    def extract_text_from_pptx(self, file_path):
        """Extract text from PowerPoint file"""
        try:
            prs = Presentation(file_path)
            text = ""
            metadata = {
                "slides": len(prs.slides),
                "title": prs.core_properties.title or '',
                "author": prs.core_properties.author or ''
            }
            
            for i, slide in enumerate(prs.slides):
                text += f"\n--- Slide {i + 1} ---\n"
                
                for shape in slide.shapes:
                    if hasattr(shape, "text"):
                        text += shape.text + "\n"
            
            return text.strip(), metadata
        except Exception as e:
            raise Exception(f"Error extracting PowerPoint: {str(e)}")
    
    def extract_text_from_html(self, file_path):
        """Extract text from HTML file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            soup = BeautifulSoup(content, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
            
            # Get text
            text = soup.get_text()
            
            # Clean up text
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = '\n'.join(chunk for chunk in chunks if chunk)
            
            metadata = {
                "title": soup.title.string if soup.title else '',
                "links": len(soup.find_all('a')),
                "images": len(soup.find_all('img')),
                "paragraphs": len(soup.find_all('p'))
            }
            
            return text.strip(), metadata
        except Exception as e:
            raise Exception(f"Error extracting HTML: {str(e)}")
    
    def extract_text_from_json(self, file_path):
        """Extract text from JSON file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                data = json.load(file)
            
            # Convert JSON to readable text
            text = "JSON Data Structure:\n"
            text += json.dumps(data, indent=2, ensure_ascii=False)
            
            metadata = {
                "type": type(data).__name__,
                "size": len(str(data))
            }
            
            if isinstance(data, dict):
                metadata["keys"] = list(data.keys())
            elif isinstance(data, list):
                metadata["items"] = len(data)
            
            return text, metadata
        except Exception as e:
            raise Exception(f"Error extracting JSON: {str(e)}")

    def extract_metadata_from_image(self, file_path):
        """Extract metadata and basic information from image files"""
        try:
            with Image.open(file_path) as img:
                # Basic image information
                metadata = {
                    "format": img.format,
                    "mode": img.mode,
                    "size": img.size,
                    "width": img.width,
                    "height": img.height,
                    "has_transparency": img.mode in ('RGBA', 'LA') or 'transparency' in img.info
                }

                # Extract EXIF data if available
                exif_data = {}
                if hasattr(img, '_getexif') and img._getexif() is not None:
                    exif = img._getexif()
                    for tag_id, value in exif.items():
                        tag = ExifTags.TAGS.get(tag_id, tag_id)
                        exif_data[tag] = value

                if exif_data:
                    metadata["exif"] = exif_data

                # Convert image to base64 for analysis (resize if too large)
                max_size = (800, 600)
                if img.width > max_size[0] or img.height > max_size[1]:
                    img.thumbnail(max_size, Image.Resampling.LANCZOS)

                # Convert to RGB if necessary
                if img.mode != 'RGB':
                    img = img.convert('RGB')

                # Convert to base64
                buffer = io.BytesIO()
                img.save(buffer, format='JPEG', quality=85)
                img_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')

                # Create descriptive text for the image
                text = f"""Image Analysis:

File Format: {metadata['format']}
Dimensions: {metadata['width']} x {metadata['height']} pixels
Color Mode: {metadata['mode']}
File Size: {os.path.getsize(file_path)} bytes

This is a {metadata['format']} image with dimensions {metadata['width']}x{metadata['height']} pixels.
"""

                if exif_data:
                    text += "\nEXIF Data found - this image may contain camera information, location data, or other metadata.\n"

                metadata["base64"] = img_base64
                metadata["description"] = text

                return text, metadata

        except Exception as e:
            raise Exception(f"Error processing image: {str(e)}")

    def process_document(self, file_path, original_filename):
        """Process document and extract text based on file type"""
        try:
            file_extension = original_filename.rsplit('.', 1)[1].lower()

            # Handle image files
            if file_extension in self.image_extensions:
                text, metadata = self.extract_metadata_from_image(file_path)
            # Handle document files
            elif file_extension == 'txt':
                text, metadata = self.extract_text_from_txt(file_path)
            elif file_extension == 'json':
                text, metadata = self.extract_text_from_json(file_path)
            elif file_extension == 'pdf':
                text, metadata = self.extract_text_from_pdf(file_path)
            elif file_extension in ['docx', 'doc']:
                text, metadata = self.extract_text_from_docx(file_path)
            elif file_extension == 'csv':
                text, metadata = self.extract_text_from_csv(file_path)
            elif file_extension in ['xlsx', 'xls']:
                text, metadata = self.extract_text_from_xlsx(file_path)
            elif file_extension == 'html':
                text, metadata = self.extract_text_from_html(file_path)
            elif file_extension in ['pptx', 'ppt']:
                text, metadata = self.extract_text_from_pptx(file_path)
            else:
                raise ValueError(f"Unsupported file type: {file_extension}. Supported types: images (PNG, JPG, JPEG, GIF, WebP, SVG), documents (PDF, DOCX, DOC, TXT, JSON, HTML), spreadsheets (XLSX, XLS, CSV), and presentations (PPTX, PPT).")

            # Add general metadata
            metadata.update({
                "filename": original_filename,
                "file_type": file_extension,
                "file_size": os.path.getsize(file_path),
                "text_length": len(text),
                "is_image": file_extension in self.image_extensions
            })

            return text, metadata

        except Exception as e:
            raise Exception(f"Error processing document: {str(e)}")
        finally:
            # Clean up uploaded file
            if os.path.exists(file_path):
                os.remove(file_path)
    
    def analyze_document_with_llm(self, text, metadata, user_query=None):
        """Prepare comprehensive document analysis prompt for detailed LLM analysis"""
        is_image = metadata.get('is_image', False)

        if not user_query:
            if is_image:
                user_query = "Please provide a comprehensive, detailed analysis of this image including all visual elements, text content, context, and meaning."
            else:
                user_query = "Please provide a comprehensive, detailed analysis of this document including structure, content, tone, insights, and implications."

        if is_image:
            analysis_prompt = f"""
COMPREHENSIVE IMAGE ANALYSIS REQUEST

CRITICAL INSTRUCTIONS:
- Provide COMPLETE, DETAILED, and COMPREHENSIVE image analysis
- DO NOT summarize or condense your observations
- Include ALL visible elements, details, and contextual information
- Write in full paragraphs with thorough descriptions
- Analyze every aspect of the image comprehensively

File Information:
- Filename: {metadata.get('filename', 'Unknown')}
- File Type: {metadata.get('file_type', 'Unknown')}
- File Size: {metadata.get('file_size', 0)} bytes
- Dimensions: {metadata.get('width', 'Unknown')} x {metadata.get('height', 'Unknown')} pixels
- Format: {metadata.get('format', 'Unknown')}
- EXIF Data: {json.dumps(metadata.get('exif_data', {}), indent=2) if metadata.get('exif_data') else 'None'}

Image Content:
{text}

User Query: {user_query}

Provide a comprehensive analysis covering ALL of the following aspects in detail:

1. COMPLETE VISUAL DESCRIPTION:
   - Describe every visible element in the image thoroughly
   - Include detailed descriptions of all objects, people, animals, structures
   - Describe the overall scene, setting, and environment
   - Note the perspective, angle, and viewpoint of the image

2. DETAILED OBJECT AND ELEMENT ANALYSIS:
   - Identify and describe every object, person, or element visible
   - Include colors, shapes, sizes, positions, and relationships
   - Describe clothing, expressions, poses, and activities if people are present
   - Note any brands, logos, signs, or identifying marks

3. COMPREHENSIVE TEXT AND OCR ANALYSIS:
   - Extract and transcribe ALL visible text in the image
   - Include signs, labels, captions, watermarks, and any written content
   - Describe the font styles, sizes, and positioning of text
   - Interpret the meaning and context of any text found

4. DETAILED COLOR AND COMPOSITION ANALYSIS:
   - Describe the color palette, lighting, and mood
   - Analyze the composition, balance, and visual flow
   - Note artistic techniques, style, and photographic elements
   - Describe shadows, highlights, and lighting conditions

5. CONTEXTUAL AND ENVIRONMENTAL ANALYSIS:
   - Identify the location, setting, or environment
   - Describe the time of day, season, or weather conditions
   - Analyze the cultural, historical, or social context
   - Note any symbolic or metaphorical elements

6. TECHNICAL AND QUALITY ANALYSIS:
   - Assess image quality, resolution, and clarity
   - Note any technical aspects like depth of field, focus, exposure
   - Identify the likely camera type or photographic technique used
   - Comment on any post-processing or editing visible

7. COMPREHENSIVE INTERPRETATION AND INSIGHTS:
   - Provide detailed interpretation of the image's meaning or purpose
   - Analyze the emotional impact or message conveyed
   - Discuss any artistic, cultural, or historical significance
   - Offer insights into the photographer's intent or the image's context

8. SPECIFIC RESPONSE TO USER QUERY:
   - Address the user's specific question or request in detail
   - Provide comprehensive answers with supporting evidence from the image
   - Include any additional relevant information or observations

Write your analysis in detailed, comprehensive paragraphs that thoroughly explore each aspect without summarization or condensation.
"""
        else:
            analysis_prompt = f"""
COMPREHENSIVE DOCUMENT ANALYSIS REQUEST

CRITICAL INSTRUCTIONS:
- Provide COMPLETE, DETAILED, and COMPREHENSIVE document analysis
- DO NOT summarize or condense information
- Include ALL relevant details, insights, and contextual information
- Write in full paragraphs with thorough explanations
- Analyze every aspect of the document comprehensively
- Cover structure, content, tone, style, and implications in detail

File Information:
- Filename: {metadata.get('filename', 'Unknown')}
- File Type: {metadata.get('file_type', 'Unknown')}
- File Size: {metadata.get('file_size', 0)} bytes
- Text Length: {metadata.get('text_length', 0)} characters
- Pages: {metadata.get('pages', 'Unknown')}
- Author: {metadata.get('author', 'Unknown')}
- Title: {metadata.get('title', 'Unknown')}
- Subject: {metadata.get('subject', 'Unknown')}

Additional Metadata:
{json.dumps({k: v for k, v in metadata.items() if k not in ['filename', 'file_type', 'file_size', 'text_length', 'base64', 'is_image']}, indent=2)}

COMPLETE DOCUMENT CONTENT:
{text}

User Query: {user_query}

Provide a comprehensive analysis covering ALL of the following aspects in detail:

1. COMPLETE DOCUMENT OVERVIEW AND STRUCTURE:
   - Provide a thorough overview of the document's purpose and scope
   - Analyze the document structure, organization, and layout
   - Identify sections, chapters, headings, and organizational elements
   - Describe the document type, format, and intended audience

2. DETAILED CONTENT ANALYSIS:
   - Analyze ALL main topics, themes, and subject matters thoroughly
   - Identify and explain key concepts, ideas, and arguments presented
   - Describe supporting evidence, examples, and case studies
   - Note any data, statistics, figures, or quantitative information

3. COMPREHENSIVE TONE AND STYLE ANALYSIS:
   - Analyze the writing style, tone, and voice throughout the document
   - Identify the author's perspective, bias, or point of view
   - Describe the language level, complexity, and target audience
   - Note any rhetorical devices, persuasive techniques, or literary elements

4. DETAILED FACTUAL AND DATA ANALYSIS:
   - Extract and analyze ALL important facts, figures, and data points
   - Identify trends, patterns, or relationships in the information
   - Verify consistency and accuracy of information presented
   - Note any contradictions, gaps, or areas needing clarification

5. CONTEXTUAL AND HISTORICAL ANALYSIS:
   - Provide historical context and background information
   - Analyze the document's relevance to current events or trends
   - Identify cultural, social, or political implications
   - Compare with similar documents or industry standards

6. CRITICAL EVALUATION AND INSIGHTS:
   - Provide critical evaluation of the document's strengths and weaknesses
   - Analyze the validity and reliability of arguments presented
   - Identify potential biases, limitations, or areas for improvement
   - Offer insights into the document's significance and impact

7. PRACTICAL IMPLICATIONS AND APPLICATIONS:
   - Discuss practical applications and real-world implications
   - Identify actionable insights and recommendations
   - Suggest next steps, follow-up actions, or areas for further research
   - Analyze potential outcomes or consequences

8. COMPREHENSIVE RESPONSE TO USER QUERY:
   - Address the user's specific question or request in complete detail
   - Provide thorough answers with supporting evidence from the document
   - Include additional relevant information and context
   - Offer comprehensive insights and recommendations

Write your analysis in detailed, comprehensive paragraphs that thoroughly explore each aspect without summarization or condensation. Ensure complete coverage of all document content and implications.
"""

        return analysis_prompt

# Sozhaa Tech AI - Environment Configuration

# ===============================
# Core API Keys
# ===============================

# Gemini API Key (Required)
# Get from: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your_gemini_api_key_here

# ===============================
# Database Configuration
# ===============================

# PostgreSQL Database URL (Required)
# Format: postgresql://username:password@host:port/database_name
DATABASE_URL=postgresql://postgres:password@localhost:5432/sozhaa_ai

# Flask Secret Key (Required)
# Generate a secure random string for production
SECRET_KEY=your_secret_key_here

# ===============================
# Web Search API Configuration
# ===============================

# Serper API (Primary web search service)
# Get your API key from: https://serper.dev/
# Free tier: 2,500 searches/month
SERPER_API_KEY=your_serper_api_key_here

# Google Custom Search API (Fallback option)
# Get these from: https://developers.google.com/custom-search/v1/introduction
# Free tier: 100 searches/day
GOOGLE_SEARCH_KEY=your_google_search_api_key_here
SEARCH_ENGINE_ID=your_custom_search_engine_id_here

# ===============================
# Optional: Enhanced Speech-to-Text
# ===============================

# Google Cloud Speech-to-Text API (Optional)
# For better accuracy with Indian languages
# GOOGLE_CLOUD_PROJECT_ID=your_project_id
# GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account-key.json

# OpenAI API Key (Optional)
# For Whisper API integration
# OPENAI_API_KEY=your_openai_api_key_here

# ===============================
# Development Settings
# ===============================

# Flask Environment
FLASK_ENV=development
FLASK_DEBUG=True

# CORS Settings (for development)
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

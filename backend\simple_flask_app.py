#!/usr/bin/env python3
"""
Simplified Flask app to test live data functionality without eventlet/socketio dependencies
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import os
import json
import socket
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

app = Flask(__name__)
CORS(app)

# Network health diagnostics for live data APIs
def test_network_connectivity():
    """Test network connectivity to external APIs on startup"""
    api_hosts = [
        "api.open-meteo.com",
        "newsapi.org", 
        "gnews.io",
        "api.cricapi.com",
        "query2.finance.yahoo.com",
        "wttr.in"
    ]
    
    print("🌐 Testing network connectivity to external APIs...")
    
    for host in api_hosts:
        try:
            ip = socket.gethostbyname(host)
            print(f"✅ DNS OK for {host} -> {ip}")
        except socket.gaierror as e:
            print(f"⚠️ DNS FAILED for {host}: {e}")
            # Try basic connectivity test
            try:
                # Test basic socket connection
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(5)
                result = sock.connect_ex((host, 80))
                sock.close()
                if result == 0:
                    print(f"✅ Connection OK for {host}")
                else:
                    print(f"❌ Connection failed for {host}")
            except Exception:
                print(f"❌ Complete connectivity failure for {host}")

# Test connectivity on startup
test_network_connectivity()

# Initialize services
try:
    from services.gemini_web_service import GeminiWebService
    gemini_service = GeminiWebService()
    print("✅ Gemini Web Service initialized successfully")
except Exception as e:
    print(f"❌ Failed to initialize Gemini service: {e}")
    gemini_service = None

@app.route('/api/chat', methods=['POST'])
def chat():
    """Simple chat endpoint for testing live data"""
    try:
        data = request.get_json()
        user_message = data.get('message', '')
        
        if not user_message:
            return jsonify({"error": "No message provided"}), 400
        
        print(f"🔍 User query: {user_message}")
        
        if gemini_service:
            # Use Gemini service for live data
            response = gemini_service.get_comprehensive_response(user_message)
            
            return jsonify({
                "response": response.get("response", "No response generated"),
                "source": response.get("source", "unknown"),
                "timestamp": response.get("timestamp", ""),
                "success": True
            })
        else:
            return jsonify({
                "response": "Gemini service not available",
                "success": False
            }), 500
            
    except Exception as e:
        print(f"❌ Chat error: {e}")
        return jsonify({
            "error": str(e),
            "success": False
        }), 500

@app.route('/api/test-live-data', methods=['GET'])
def test_live_data():
    """Test endpoint for live data functionality"""
    try:
        from services.live_data_service import LiveDataService
        
        live_service = LiveDataService()
        
        # Test weather
        weather_result = live_service.fetch_weather("Chennai")
        
        # Test cricket
        cricket_result = live_service.fetch_cricket()
        
        return jsonify({
            "weather": weather_result,
            "cricket": cricket_result,
            "success": True
        })
        
    except Exception as e:
        print(f"❌ Live data test error: {e}")
        return jsonify({
            "error": str(e),
            "success": False
        }), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "gemini_service": gemini_service is not None,
        "timestamp": str(os.environ.get('TIMESTAMP', 'unknown'))
    })

if __name__ == '__main__':
    print("🚀 Starting simplified Flask app for live data testing...")
    print("📡 Available endpoints:")
    print("  - POST /api/chat - Chat with live data")
    print("  - GET /api/test-live-data - Test live data services")
    print("  - GET /api/health - Health check")
    print("🌐 Server starting on http://localhost:5000")
    
    app.run(debug=True, host='0.0.0.0', port=5000)

import React, { useState, useEffect } from 'react';
import { agentAPI } from '../services/api';
import './ToolsPanel.css';

const ToolsPanel = ({ onToolsChange, enabledTools }) => {
  const [availableTools, setAvailableTools] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadAvailableTools();
  }, []);

  const loadAvailableTools = async () => {
    try {
      setIsLoading(true);
      const response = await agentAPI.getTools();
      setAvailableTools(response.data.tools);
    } catch (error) {
      console.error('Error loading tools:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleToolToggle = (toolName) => {
    const newEnabledTools = enabledTools.includes(toolName)
      ? enabledTools.filter(tool => tool !== toolName)
      : [...enabledTools, toolName];
    
    onToolsChange(newEnabledTools);
  };

  return (
    <div className="tools-panel">
      <h4>AI Tools & Capabilities</h4>
      
      <div className="tools-section">
        <label className="tool-toggle">
          <input
            type="checkbox"
            checked={enabledTools.includes('rag')}
            onChange={() => handleToolToggle('rag')}
          />
          <span className="tool-label">
            <span className="tool-name">Knowledge Base</span>
            <span className="tool-description">Search internal documents</span>
          </span>
        </label>

        <label className="tool-toggle">
          <input
            type="checkbox"
            checked={enabledTools.includes('agent')}
            onChange={() => handleToolToggle('agent')}
          />
          <span className="tool-label">
            <span className="tool-name">AI Agent</span>
            <span className="tool-description">Use tools for complex tasks</span>
          </span>
        </label>
      </div>

      <div className="langchain-tools">
        <h5>Available Tools</h5>
        {isLoading ? (
          <div className="loading">Loading tools...</div>
        ) : (
          availableTools.map(tool => (
            <div key={tool} className="tool-item">
              <span className="tool-badge">{tool}</span>
            </div>
          ))
        )}
      </div>

      <div className="capabilities-info">
        <h5>Capabilities</h5>
        <ul>
          <li>💬 Conversational Memory</li>
          <li>🔍 Semantic Search</li>
          <li>🛠️ Tool Usage</li>
          <li>📚 Document Analysis</li>
          <li>🌐 Web Search</li>
        </ul>
      </div>
    </div>
  );
};

export default ToolsPanel;
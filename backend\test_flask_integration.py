#!/usr/bin/env python3
"""
Simple test to verify Flask integration with live data works
"""

import os
import sys
from dotenv import load_dotenv

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
load_dotenv()

def test_flask_integration():
    """Test that our services work within Flask context"""
    print("🧪 Testing Flask Integration with Live Data")
    print("=" * 50)
    
    try:
        # Import Flask and create app context
        from flask import Flask
        app = Flask(__name__)
        
        with app.app_context():
            # Import our services
            from services.live_data_service import LiveDataService
            from services.gemini_web_service import GeminiWebService
            
            print("✅ Services imported successfully")
            
            # Test live data service
            live_service = LiveDataService()
            print("✅ LiveDataService initialized")
            
            # Test a simple query
            result = live_service.fetch_live_data("weather in Chennai")
            if result['success']:
                print(f"✅ Live data fetch successful: {result['source']}")
            else:
                print(f"⚠️ Live data fetch failed: {result['error']}")
            
            # Test Gemini web service
            web_service = GeminiWebService()
            print("✅ GeminiWebService initialized")
            
            # Test a comprehensive response
            response = web_service.get_comprehensive_response("What's the weather in Chennai?")
            if response and len(response) > 50:
                print("✅ Comprehensive response generated successfully")
                print(f"📄 Response preview: {response[:100]}...")
            else:
                print("⚠️ Comprehensive response failed or too short")
            
            print("\n🎉 Flask integration test completed successfully!")
            return True
            
    except Exception as e:
        print(f"❌ Flask integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_flask_integration()
    if success:
        print("\n✅ Ready to start Flask app with live data integration!")
        print("🚀 Run: python app.py")
    else:
        print("\n❌ Flask integration has issues - check the errors above")
